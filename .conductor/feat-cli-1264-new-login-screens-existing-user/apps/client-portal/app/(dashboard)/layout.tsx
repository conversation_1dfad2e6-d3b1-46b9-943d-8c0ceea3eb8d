import { SignedIn } from "@clerk/nextjs";
import type { PropsWithChildren } from "react";

import { DashboardNavbar } from "@/components/navbar";
import { AuthProvider } from "@/components/providers/auth-provider";
import { RouteGuard } from "@/components/providers/route-guard";
import { SDKInitializer } from "@/components/sdk-initializer";
import { useInactivity as UseInactive } from "@/hooks/use-inactive";

import { LayoutContent } from "./_layout-content";

export default async function DashboardLayout({ children }: PropsWithChildren) {
  return (
    <>
      <SignedIn>
        <AuthProvider>
          <RouteGuard>
            <DashboardNavbar />
            <div className="mt-navbar !flex !min-h-[calc(100dvh-var(--navbar-height))] items-start">
              <SDKInitializer />
              <LayoutContent>{children}</LayoutContent>
            </div>
            <UseInactive />
          </RouteGuard>
        </AuthProvider>
      </SignedIn>
    </>
  );
}
