import { PropsWithChildren } from "react";

import { cn } from "@/lib/utils";

import { SwitchCountries } from "./countries-selection";
import { Sponsors } from "./sponsors";

export default function AuthenticationLayout({ children }: PropsWithChildren) {
  return (
    <div className="relative flex h-screen w-screen items-center justify-end">
      <div
        style={{
          position: "absolute",
          top: 0,
          bottom: 0,
          width: "100%",
          height: "100%",
          overflow: "hidden",
          objectFit: "fill",
        }}
      >
        <video
          autoPlay
          loop
          muted
          className={cn(
            "absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 object-cover",
            "h-auto min-h-full w-auto min-w-full",
            // "brightness-200 grayscale",
          )}
          style={{
            filter:
              "hue-rotate(41deg) saturate(1) brightness(1.4) contrast(1.3)",
          }}
        >
          <source
            src="https://framerusercontent.com/assets/hlHa0WmqM9HmzZ5IcszBllNFe4.mp4"
            type="video/mp4"
          />
        </video>
      </div>
      {/* <div className="z-[100] h-full w-full bg-blue-800 opacity-75"></div> */}

      <div className="z-10 flex h-full w-1/2 flex-col items-center justify-between space-y-6 bg-white">
        <SwitchCountries />

        {children}
        <Sponsors />
      </div>
    </div>
  );
}
