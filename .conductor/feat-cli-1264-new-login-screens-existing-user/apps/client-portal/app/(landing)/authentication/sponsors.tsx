"use client";

import { sponsors } from "@/components/site-sidebar";
import { Certificated } from "@/components/site-sidebar/icons/certificated";

export const Sponsors = () => {
  return (
    <div className="mx-auto flex w-full items-center justify-center border-t border-gray-200 py-2.5 text-xs">
      <div className="flex items-center">
        <div className="flex items-center justify-center gap-x-[5px]">
          <Certificated />
          <span className="font-bold leading-[18px]">
            Certified Compliance Standard
          </span>
        </div>

        <div className="text-primary-500 flex justify-center gap-x-[5px] font-medium">
          {sponsors.map((sponsor, idx) => (
            <div
              key={idx}
              className="flex h-[22px] items-center gap-x-[5px] rounded-2xl bg-gray-50 px-2"
            >
              {sponsor.logo}
              {sponsor.name}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
