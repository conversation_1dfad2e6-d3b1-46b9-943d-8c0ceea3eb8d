"use client";

import { Dropdown } from "flowbite-react";
import Image from "next/image";
import { useEffect, useState } from "react";

import { cn } from "@/lib/utils";

export const SwitchCountries = () => {
  const [selectedCountry, setSelectedCountry] = useState("us");

  useEffect(() => {
    if (window !== undefined) {
      const isUsa = window.location.href.includes(".us.");

      if (isUsa) {
        setSelectedCountry("us");
      } else {
        setSelectedCountry("ca");
      }
    }
  }, []);

  function changeCountry(country: string) {
    const url = window.location.href;

    if (country === "us" && url.includes(".ca.")) {
      window.location.replace(url.replace(".ca.", ".us."));
      return;
    }

    if (country === "ca" && url.includes(".us.")) {
      window.location.replace(url.replace(".us.", ".ca."));
    }
  }

  return (
    <div
      className={cn(
        "[&>button]:border [&>button]:border-gray-300 [&>button]:!ring-blue-500 [&>button]:dark:border-gray-600",
        "[&>button]:bg-gray-100 [&>button]:hover:!bg-gray-100 [&>button]:dark:bg-gray-700 [&>button]:dark:hover:!bg-gray-700",
        "[&>button]:text-black [&>button]:dark:text-white",
        "[&_ul]:list-none",
        "[&_li>button]:gap-2",
        "flex w-full justify-end p-5",
      )}
    >
      <Dropdown
        key={selectedCountry}
        label={
          <SelectedCountry
            key={selectedCountry}
            selectedCountry={selectedCountry}
          />
        }
        dismissOnClick={false}
      >
        <Dropdown.Item onClick={() => changeCountry("us")}>
          <Image
            src="/images/flags/us.svg"
            alt="USA flag"
            width={28}
            height={20}
          />{" "}
          <span>USA</span>
        </Dropdown.Item>
        <Dropdown.Item onClick={() => changeCountry("ca")}>
          <Image
            src="/images/flags/ca.svg"
            alt="Canada flag"
            width={28}
            height={20}
          />{" "}
          <span>Canada</span>
        </Dropdown.Item>
      </Dropdown>
    </div>
  );
};

export const SelectedCountry = ({
  selectedCountry,
}: {
  selectedCountry: string;
}) => {
  if (selectedCountry === "ca") {
    return (
      <div className="flex items-center gap-x-2">
        <Image
          src="/images/flags/ca.svg"
          alt="Canada flag"
          width={28}
          height={20}
        />
        <span>Canada</span>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-x-2">
      <Image src="/images/flags/us.svg" alt="USA flag" width={28} height={20} />
      <span>USA</span>
    </div>
  );
};
