import { SignedOut } from "@clerk/nextjs";
import { Metadata } from "next";

import { cn } from "@/lib/utils";

import { SignInForm } from "./sign-in-form";

export const metadata: Metadata = {
  title: "Sign In - Clincove",
};

export default function SignInPage() {
  return (
    <SignedOut>
      <div className="relative flex h-screen w-screen items-center justify-end">
        <div
          style={{
            position: "absolute",
            top: 0,
            bottom: 0,
            width: "100%",
            height: "100%",
            overflow: "hidden",
            objectFit: "fill",
          }}
        >
          <video
            autoPlay
            loop
            muted
            className={cn(
              "absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 object-cover",
              "h-auto min-h-full w-auto min-w-full",
              // "brightness-200 grayscale",
            )}
            style={{
              filter:
                "hue-rotate(22deg) saturate(1.3) brightness(2) contrast(1.3)",
            }}
          >
            <source
              src="https://framerusercontent.com/assets/hlHa0WmqM9HmzZ5IcszBllNFe4.mp4"
              type="video/mp4"
            />
          </video>
        </div>
        {/* <div className="z-[100] h-full w-full bg-blue-800 opacity-75"></div> */}
        <SignInForm />
      </div>
    </SignedOut>
  );
}
