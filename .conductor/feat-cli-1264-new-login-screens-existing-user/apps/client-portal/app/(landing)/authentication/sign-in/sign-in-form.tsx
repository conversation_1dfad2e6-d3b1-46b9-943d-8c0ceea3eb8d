"use client";

import { useSignIn } from "@clerk/nextjs";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { <PERSON><PERSON><PERSON>, Hi<PERSON>yeOff } from "react-icons/hi";
import toast from "react-hot-toast";
import { z } from "zod";

import { Button } from "@/components/ui/button";
import { InputField } from "@/components/ui/form/input";
import { Label } from "@/components/ui/form/label";
import { cn } from "@/lib/utils";

// Form validation schema
const signInSchema = z.object({
  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address"),
  password: z.string().min(1, "Password is required"),
});

type SignInFormData = z.infer<typeof signInSchema>;

export function SignInForm() {
  const { isLoaded, signIn, setActive } = useSignIn();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const form = useForm<SignInFormData>({
    resolver: zodResolver(signInSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const onSubmit = async (data: SignInFormData) => {
    if (!isLoaded) return;

    setIsLoading(true);

    try {
      const result = await signIn.create({
        identifier: data.email,
        password: data.password,
      });

      if (result.status === "complete") {
        await setActive({ session: result.createdSessionId });
        // Redirect will be handled by Clerk's middleware
      } else {
        // Handle other statuses or additional steps if needed
        console.error("Sign-in incomplete:", result.status);
        toast.error("Sign-in process incomplete. Please try again.");
      }
    } catch (error: any) {
      console.error("Sign-in error:", error);
      
      // Handle specific Clerk errors
      if (error.errors) {
        const clerkError = error.errors[0];
        switch (clerkError.code) {
          case "form_identifier_not_found":
            toast.error("No account found with this email address.");
            break;
          case "form_password_incorrect":
            toast.error("Incorrect password. Please try again.");
            break;
          case "form_identifier_exists":
            toast.error("An account with this email already exists.");
            break;
          case "too_many_requests":
            toast.error("Too many attempts. Please try again later.");
            break;
          default:
            toast.error(clerkError.longMessage || clerkError.message || "Sign-in failed. Please try again.");
        }
      } else {
        // Handle network or other errors
        toast.error("Something went wrong. Please check your connection and try again.");
      }
    } finally {
      setIsLoading(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="w-full max-w-md space-y-6">
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Sign in to your account
        </h1>
        <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
          Enter your email and password to access your account
        </p>
      </div>

      <FormProvider {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <div>
            <Label htmlFor="email" required>
              Email Address
            </Label>
            <InputField
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              placeholder="Enter your email address"
              disabled={isLoading}
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="password" required>
              Password
            </Label>
            <div className="relative mt-1">
              <InputField
                id="password"
                name="password"
                type={showPassword ? "text" : "password"}
                autoComplete="current-password"
                placeholder="Enter your password"
                disabled={isLoading}
                className="pr-10"
              />
              <button
                type="button"
                onClick={togglePasswordVisibility}
                disabled={isLoading}
                className={cn(
                  "absolute inset-y-0 right-0 flex items-center pr-3",
                  "text-gray-400 hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-100",
                  "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 rounded",
                  "disabled:opacity-50 disabled:cursor-not-allowed"
                )}
                aria-label={showPassword ? "Hide password" : "Show password"}
              >
                {showPassword ? (
                  <HiEyeOff className="h-5 w-5" aria-hidden="true" />
                ) : (
                  <HiEye className="h-5 w-5" aria-hidden="true" />
                )}
              </button>
            </div>
          </div>

          <Button
            type="submit"
            isLoading={isLoading}
            disabled={isLoading || !isLoaded}
            className="w-full"
          >
            {isLoading ? "Signing in..." : "Sign In"}
          </Button>
        </form>
      </FormProvider>
    </div>
  );
}
