import type { VariantProps } from "class-variance-authority";
import { cva } from "class-variance-authority";
import type { ButtonProps as FlowbiteButtonProps } from "flowbite-react";
import { Button as FlowbiteButton, Spinner } from "flowbite-react";
import type { ForwardedRef, PropsWithChildren } from "react";
import { forwardRef } from "react";
import type { Merge } from "type-fest";

import { cn } from "@/lib/utils";

const buttonVariants = cva(
  "px-5 py-2.5 rounded-lg flex items-center h-10 justify-center gap-2 [&>span]:p-0 [&>span]:flex [&>span]:items-center [&>span]:gap-2 !ring-0 duration-[20ms]",
  {
    variants: {
      variant: {
        default: "bg-primary-700 text-white hover:!bg-primary-600",
        green: "bg-green-500 text-white hover:!bg-green-600",
        primary: "bg-primary-700 text-white hover:!bg-primary-600",
        yellow: "bg-yellow-400 text-white hover:!bg-yellow-600",
        purple: "bg-purple-500 text-white hover:!bg-purple-600",
        outline:
          "bg-transparent border border-gray-300 hover:!bg-inherit text-gray-700 hover:!bg-gray-100",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
);

export type ButtonProps = Merge<
  VariantProps<typeof buttonVariants>,
  FlowbiteButtonProps & {
    isLoading?: boolean;
    disabledForInvalid?: boolean;
    enabledForDirty?: boolean;
  }
>;

export const Button = forwardRef(
  (
    {
      className,
      variant,
      children,
      onClick,
      isLoading,
      disabled,
      disabledForInvalid = true,
      enabledForDirty,
      type,
      ...props
    }: PropsWithChildren<ButtonProps>,
    ref: ForwardedRef<HTMLButtonElement>,
  ) => {
    return (
      <FlowbiteButton
        ref={ref}
        type={type}
        className={cn(
          buttonVariants({ variant }),
          disabledForInvalid &&
            type === "submit" &&
            "group-[.is-invalid]:pointer-events-none group-[.is-invalid]:opacity-50",
          enabledForDirty &&
            type === "submit" &&
            "group-[.not-dirty]:pointer-events-none group-[.not-dirty]:opacity-50",
          isLoading && "cursor-progress",
          className,
        )}
        onClick={onClick}
        disabled={isLoading || disabled}
        {...props}
      >
        {isLoading && <Spinner size="sm" className="fill-primary-500" />}
        {children}
      </FlowbiteButton>
    );
  },
);

Button.displayName = "Button";
