"use client";
import { Progress } from "flowbite-react";
import { useEffect } from "react";

import { useRole } from "@/hooks/auth/use-role";
import { useAllStudies } from "@/hooks/studies/use-all-studies";
import { usePersistedStudies } from "@/hooks/studies/use-persisted-studies";
import { STUDY_PHASES } from "@/lib/constants";
import { cn } from "@/lib/utils";
import { useSiteSidebarStore } from "@/stores/site-sidebar-store";

import { Link } from "../shared/link";
import { StudyStatusBadge } from "../ui/badge/study-status-badge";
import { Certificated } from "./icons/certificated";
import { Fda } from "./icons/fda";
import { Hipaa } from "./icons/hipaa";
import { Ich } from "./icons/ich";
import { Pipeda } from "./icons/pipeda";
import { MobileSiteSidebar } from "./mobile-site-sidebar";
import { SidebarItems } from "./sidebar-items";
import { StudySelector } from "./study-selector";

export const sponsors = [
  {
    logo: <Pipeda />,
    name: "PIPEDA",
  },
  {
    logo: <Hipaa />,
    name: "HIPAA",
  },
  {
    logo: <Ich />,
    name: "ICH",
  },
  {
    logo: <Fda />,
    name: "",
  },
];

export const SiteSidebar = () => {
  const { data: allStudies } = useAllStudies();
  const {
    studies: persistedStudies,
    currentStudy,
    isDataLoaded,
    refreshWithNewData,
  } = usePersistedStudies();

  const { enrolledPatients, patientTarget } = currentStudy || {};
  const patientProgress =
    enrolledPatients && patientTarget && patientTarget !== 0
      ? (enrolledPatients / patientTarget) * 100
      : 0;

  const { isCollapsed } = useSiteSidebarStore((state) => state.desktop);

  const { is } = useRole();

  useEffect(() => {
    if (allStudies && isDataLoaded) {
      const processedStudies = is("site")
        ? allStudies.results
        : allStudies.results.flatMap((study) => {
            return (
              study.localStudies?.map((localStudy) => {
                return {
                  ...localStudy,
                  ...study,
                  siteId: localStudy?.siteId,
                };
              }) || []
            );
          });

      const hasDataChanged =
        JSON.stringify(persistedStudies) !== JSON.stringify(processedStudies);

      if (hasDataChanged) {
        refreshWithNewData(processedStudies);
      }
    }
  }, [allStudies, persistedStudies, isDataLoaded, is, refreshWithNewData]);

  return (
    <>
      <MobileSiteSidebar />
      <aside
        className={cn(
          "pt-navbar fixed inset-y-0 left-0 z-20 hidden h-full flex-col border-r border-gray-200 bg-white shadow-lg transition-[width] duration-200 ease-in-out lg:flex dark:border-gray-700",
          isCollapsed ? "w-16" : "w-sidebar",
          // userType === "cro" && "!hidden",
        )}
      >
        <div className="flex-1 divide-y overflow-y-auto">
          <div className={cn("p-5 py-0", isCollapsed && "hidden")}>
            <StudySelector />
          </div>

          {currentStudy && (
            <Link
              href={`/studies/${currentStudy.id}?siteId=${currentStudy.siteId}`}
            >
              <div className={cn("p-5 pb-0", isCollapsed && "hidden")}>
                <div className="text-md font-medium">
                  {currentStudy.sponsor.name}
                </div>
                <div className="text-xl font-bold">{currentStudy.name}</div>
                <div className="text-xl font-bold">
                  {currentStudy.studyCode}
                </div>
                <div className="text-base font-medium">
                  {currentStudy.principalInvestigator || "Not assigned"}
                </div>
                <div className="flex items-center justify-start gap-2.5">
                  <div className="text-base font-medium">
                    {
                      STUDY_PHASES[
                        currentStudy.phase as keyof typeof STUDY_PHASES
                      ]
                    }
                  </div>
                  <StudyStatusBadge status={currentStudy.status} />
                </div>
                <div className="flex items-center gap-2 py-3">
                  <div className="flex-1">
                    <Progress
                      progress={patientProgress}
                      className="[&>div]:bg-gray-600"
                    />
                  </div>
                  <div className="flex flex-1 justify-end whitespace-nowrap text-sm font-medium tracking-[0%]">
                    {patientProgress.toFixed(2)}% ({enrolledPatients || 0}/
                    {patientTarget || 0})
                  </div>
                </div>
              </div>
            </Link>
          )}
          {currentStudy && <SidebarItems isCollapsed={isCollapsed} />}
        </div>

        <div className="space-y-2.5 border-t border-gray-200 py-2.5 text-xs">
          <div className="flex items-center justify-center gap-x-[5px]">
            <Certificated />
            <span className="font-bold leading-[18px]">
              Certified Compliance Standard
            </span>
          </div>

          <div className="text-primary-500 flex justify-center gap-x-[5px] font-medium">
            {sponsors.map((sponsor, idx) => (
              <div
                key={idx}
                className="flex h-[22px] items-center gap-x-[5px] rounded-2xl bg-gray-50 px-2"
              >
                {sponsor.logo}
                {sponsor.name}
              </div>
            ))}
          </div>
        </div>
      </aside>
    </>
  );
};
