import { createTheme, type CustomFlowbiteTheme, theme } from "flowbite-react";
import { twMerge } from "tailwind-merge";

export const customTheme: CustomFlowbiteTheme = createTheme({
  checkbox: {
    root: {
      color: {
        default:
          "focus:ring-primary-300 dark:focus:ring-primary-600 border-gray-300 bg-gray-50 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800",
      },
    },
  },
  modal: {
    content: {
      inner: twMerge(
        theme.modal.content.inner,
        "overflow-hidden dark:bg-gray-800",
      ),
      base: twMerge(theme.modal.content.base, "h-auto"),
    },
    header: {
      base: twMerge(
        theme.modal.header.base,
        "items-center p-3 sm:p-4 dark:border-gray-700",
      ),
      title: twMerge(theme.modal.header.title, "font-semibold"),
      close: {
        base: twMerge(
          theme.modal.header.close.base,
          "hover:bg-gray-200 dark:hover:bg-gray-700",
        ),
      },
    },
    body: {
      base: twMerge(theme.modal.body.base, "p-4 sm:p-6"),
    },
    footer: {
      base: twMerge(theme.modal.footer.base, "dark:border-gray-700"),
    },
  },
  progress: {
    color: {
      blue: "bg-primary-600",
      dark: "bg-gray-900 dark:bg-white",
    },
    size: {
      md: "h-2",
    },
  },
  select: {
    field: {
      select: {
        sizes: {
          md: twMerge(
            theme.select.field.select.sizes.md,
            "text-base sm:text-sm",
          ),
        },
        colors: {
          gray: twMerge(
            theme.select.field.select.colors.gray,
            "focus:border-blue-500 focus:ring-blue-500 dark:focus:border-blue-500 dark:focus:ring-blue-500",
          ),
        },
        base: twMerge(theme.select.field.select.base, "!text-sm !font-normal"),
      },
    },
  },
  sidebar: {
    root: {
      inner: twMerge(theme.sidebar.root.inner, "bg-white"),
    },
    collapse: {
      button: twMerge(
        theme.sidebar.collapse.button,
        "text-gray-900 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700",
      ),
    },
    item: {
      base: twMerge(
        theme.sidebar.collapse.button,
        "justify-start text-gray-900 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700",
      ),
      label:
        "bg-primary-100 text-primary-800 ml-3 inline-flex h-5 w-5 items-center justify-center rounded-full p-1 text-sm font-medium",
    },
  },
  textarea: {
    base: twMerge(theme.textarea.base, "p-4"),
    colors: {
      gray: twMerge(
        theme.textarea.colors.gray,
        "text-base focus:border-blue-500 focus:ring-blue-500 sm:text-sm dark:focus:border-blue-500 dark:focus:ring-blue-500",
      ),
    },
  },
  textInput: {
    field: {
      input: {
        base: twMerge(
          theme.textInput.field.input.base,
          "text-sm outline-none sm:text-base",
        ),
        sizes: {
          md: "p-2.5 sm:text-sm",
        },
        colors: {
          gray: twMerge(
            theme.textInput.field.input.colors.gray,
            "focus:border-primary-500 focus:ring-primary-500 dark:focus:border-primary-500 dark:focus:ring-primary-500",
          ),
        },
      },
    },
  },
  toggleSwitch: {
    toggle: {
      base: twMerge(
        theme.toggleSwitch.toggle.base,
        "toggle-bg rounded-full border",
      ),
      checked: {
        ...theme.toggleSwitch.toggle.checked,
        color: {
          ...theme.toggleSwitch.toggle.checked.color,
          blue: twMerge(
            theme.toggleSwitch.toggle.checked.color.blue,
            "border-blue-600 bg-blue-600",
          ),
        },
      },
    },
  },
  card: {
    root: {
      base: twMerge(
        theme.card.root.base,
        "border-none shadow-md [&>div]:!gap-0",
      ),
      children: twMerge(
        theme.card.root.children,
        "flex h-full flex-col justify-center p-4 sm:p-6",
      ),
    },
  },
  button: {
    base: twMerge(
      theme.button.base,
      "leading-5.25 focus:outline-none [&_span]:px-3",
    ),
  },
  badge: {
    root: {
      base: twMerge(theme.badge.root.base, "w-fit"),
    },
  },
  tabs: {
    tablist: {
      tabitem: {
        base: twMerge(
          theme.tabs.tabitemcontainer.base,
          "flex items-center justify-center gap-2 p-3 text-sm disabled:cursor-not-allowed disabled:text-gray-300 sm:p-4 sm:text-base disabled:dark:text-gray-600",
        ),
      },
    },
  },
});
