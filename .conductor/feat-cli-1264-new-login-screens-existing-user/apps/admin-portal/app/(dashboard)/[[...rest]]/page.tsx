"use client";

import { useAuth } from "@clerk/nextjs";
import { sdkSetURL, sdkSetUser } from "@clincove-eng/backend-sdk-temp";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { AiOutlineLoading } from "react-icons/ai";

import { configs } from "@/lib/config";
import { CLERK_TOKEN_TEMPLATE } from "@/lib/constants";

export default function Page() {
  const { isLoaded, userId, getToken } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (isLoaded && userId) {
      getToken({ template: CLERK_TOKEN_TEMPLATE }).then((token) => {
        if (token) {
          sdkSetURL(configs.API_URL ?? "");
          sdkSetUser(token);
        }
      });
    }
  }, [getToken, isLoaded, router, userId]);

  // In case the user signs out while on the page.
  if (!isLoaded || !userId) {
    return (
      <div className="flex h-screen w-screen items-center justify-center bg-gray-50 dark:bg-gray-900">
        <AiOutlineLoading className="h-10 w-10 animate-spin text-gray-900 dark:text-white" />
      </div>
    );
  }

  return null;
}
