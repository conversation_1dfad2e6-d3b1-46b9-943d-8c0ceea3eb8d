import { forwardRef, useImperativeHandle, useState } from "react";

import { Button } from "@/components/ui/button";
import { Modal } from "@/components/ui/modal";

type ModalWarningProps = {
  onAccept: () => void;
  title: string;
  description: string;
};

export type ModalWarningRef = {
  toggleModal: () => void;
};

export const ModalWarning = forwardRef<ModalWarningRef, ModalWarningProps>(
  ({ onAccept, title, description }, ref) => {
    const [show, setShow] = useState(false);

    useImperativeHandle(ref, () => ({
      toggleModal: () => setShow((prev) => !prev),
    }));

    return (
      <Modal show={show} onClose={() => setShow(false)}>
        <Modal.Header>{title}</Modal.Header>
        <Modal.Body>
          <p className="dark:text-white">{description}</p>
          <div className="mt-4 flex w-full justify-end gap-4">
            <Button
              className="border border-gray-300 !bg-inherit text-black !ring-0 hover:!bg-inherit dark:border-gray-500 dark:!text-gray-100"
              onClick={() => setShow(false)}
            >
              Cancel
            </Button>
            <Button variant="primary" onClick={onAccept}>
              Proceed
            </Button>
          </div>
        </Modal.Body>
      </Modal>
    );
  },
);

ModalWarning.displayName = "ModalWarning";
