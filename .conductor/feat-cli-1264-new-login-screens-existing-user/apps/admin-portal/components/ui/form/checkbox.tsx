import { Checkbox as FlowbiteCheckbox } from "flowbite-react";
import React, { forwardRef } from "react";
import { Controller, useFormContext } from "react-hook-form";
import type { SetRequired } from "type-fest";

export type CheckboxProps = SetRequired<
  React.ComponentPropsWithoutRef<typeof FlowbiteCheckbox>,
  "name"
>;

export const Checkbox: React.ForwardRefExoticComponent<
  CheckboxProps & React.RefAttributes<HTMLInputElement>
> = forwardRef(({ name, ...props }, ref) => {
  const { control } = useFormContext();
  return (
    <Controller
      control={control}
      name={name}
      render={({ field: { value, onChange, ...field } }) => (
        <FlowbiteCheckbox
          {...props}
          {...field}
          ref={ref}
          checked={value}
          onChange={(e) => onChange(e.target.checked)}
        />
      )}
    />
  );
});

Checkbox.displayName = "Checkbox";
