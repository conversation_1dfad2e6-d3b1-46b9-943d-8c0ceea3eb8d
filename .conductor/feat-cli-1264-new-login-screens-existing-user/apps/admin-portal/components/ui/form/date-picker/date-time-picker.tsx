"use client";

import "react-day-picker/style.css";
import "./date-picker.css";

import {
  autoUpdate,
  flip,
  offset,
  shift,
  useClick,
  useDismiss,
  useFloating,
  useInteractions,
  useRole,
} from "@floating-ui/react";
import {
  format as formatDate,
  setHours,
  setMilliseconds,
  setMinutes,
  setSeconds,
} from "date-fns";
import { Calendar, X } from "lucide-react";
import { useEffect, useState } from "react";
import { DayPicker } from "react-day-picker";
import { get, useController, useFormContext } from "react-hook-form";

import { cn } from "@/lib/utils";

import { CustomCaptionLabel } from "./custom-caption-label";
import { TimeFooter } from "./time-footer";

export const DATE_FORMATS = {
  "mm/dd/yyyy": "LL/dd/yyyy",
  "dd/mm/yyyy": "dd/LL/yyyy",
  "yyyy/mm/dd": "yyyy/LL/dd",
} as const;

type DateFormat = keyof typeof DATE_FORMATS;

type DateTimePickerProps = {
  name: string;
  placeholder?: string;
  disabled?: boolean;
  format?: DateFormat;
  showTime?: boolean;
  minDate?: Date;
  maxDate?: Date;
  className?: string;
};

const formatDateValue = (date: Date, format: DateFormat, showTime: boolean) => {
  let dateString = formatDate(date, DATE_FORMATS[format]);

  if (showTime) {
    dateString += `, ${formatDate(date, "hh:mm aaa")}`;
  }

  return dateString;
};

export const DateTimePicker = ({
  name,
  placeholder = "Select a date",
  disabled = false,
  format = "mm/dd/yyyy",
  showTime = false,
  minDate,
  maxDate,
  className,
  ...props
}: DateTimePickerProps) => {
  const { control } = useFormContext();
  const {
    field,
    formState: { errors },
  } = useController({ name, control, ...props });
  const [isOpen, setIsOpen] = useState(false);
  const [month, setMonth] = useState<Date>(field.value || new Date());
  const [showYears, setShowYears] = useState(false);

  const { refs, floatingStyles, context } = useFloating({
    open: isOpen,
    onOpenChange: (open) => {
      setIsOpen(open);
      if (!open) field.onBlur();
    },
    middleware: [offset(0), flip(), shift({ padding: 8 })],
    whileElementsMounted: autoUpdate,
    placement: "bottom-end",
  });

  const click = useClick(context);
  const dismiss = useDismiss(context);
  const role = useRole(context);

  const { getReferenceProps, getFloatingProps } = useInteractions([
    click,
    dismiss,
    role,
  ]);

  const isDisabled = disabled || field.disabled;

  const currentYear = month.getFullYear();

  const errorObj = get(errors, name);
  const errorMessage = errorObj?.message;
  const hasError = typeof errorMessage === "string";

  const displayValue = field.value
    ? formatDateValue(field.value, format, showTime)
    : "";

  const handleDateSelect = (date?: Date) => {
    if (!date) {
      setIsOpen(false);
      field.onBlur();
      return field.onChange(undefined);
    }
    if (field.value) {
      const dateWithTime = setMilliseconds(
        setSeconds(
          setMinutes(
            setHours(date, field.value.getHours()),
            field.value.getMinutes(),
          ),
          field.value.getSeconds(),
        ),
        field.value.getMilliseconds(),
      );
      field.onChange(dateWithTime);
    } else {
      if (showTime) {
        const now = new Date();
        const dateWithCurrentTime = setMilliseconds(
          setSeconds(
            setMinutes(setHours(date, now.getHours()), now.getMinutes()),
            0,
          ),
          0,
        );
        field.onChange(dateWithCurrentTime);
      } else {
        field.onChange(date);
      }
    }
    setIsOpen(false);
    field.onBlur();
  };

  const handleTimeChange = (dateTime: Date) => {
    field.onChange(dateTime);
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    field.onChange(undefined);
    field.onBlur();
  };

  const disabledRange = () => {
    if (minDate && maxDate) {
      return {
        after: maxDate,
        before: minDate,
      };
    }
    if (minDate) {
      return {
        before: minDate,
      };
    }
    if (maxDate) {
      return {
        after: maxDate,
      };
    }
    return isDisabled;
  };

  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === "Escape" && isOpen) {
        setIsOpen(false);
        field.onBlur();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscapeKey);
    }

    return () => {
      document.removeEventListener("keydown", handleEscapeKey);
    };
  }, [field]);

  return (
    <>
      <div
        ref={refs.setReference}
        className={cn(
          "date-time-picker relative flex w-full items-center rounded-md border bg-gray-50 p-2.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:focus:ring-blue-400",
          hasError &&
            "border-red-500 focus:ring-red-500 dark:border-red-500 dark:focus:ring-red-500",
          isDisabled && "cursor-not-allowed opacity-50",
          className,
        )}
        {...getReferenceProps()}
      >
        <input
          readOnly
          disabled={isDisabled}
          value={displayValue}
          placeholder={placeholder}
          className="flex-1 bg-transparent text-sm text-gray-900 placeholder:text-gray-400 focus:outline-none dark:text-white dark:placeholder:text-gray-500"
        />
        {field.value ? (
          <button
            type="button"
            onClick={handleClear}
            disabled={isDisabled}
            className="ml-2 flex items-center justify-center rounded-full text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
          >
            <X onClick={handleClear} className="ml-2 size-4" />
          </button>
        ) : (
          <Calendar className="ml-2 size-4 text-gray-400 dark:text-gray-500" />
        )}
      </div>
      {hasError && (
        <span className="text-sm text-red-500 dark:text-red-400">
          {errorMessage}
        </span>
      )}

      {isOpen && (
        <div
          ref={refs.setFloating}
          style={floatingStyles}
          className="z-50"
          {...getFloatingProps()}
        >
          <div className="rounded-lg bg-white shadow-lg dark:bg-gray-800">
            <DayPicker
              mode="single"
              selected={field.value}
              onSelect={handleDateSelect}
              month={month}
              onMonthChange={setMonth}
              className="relative overflow-hidden"
              formatters={{
                formatWeekdayName: (weekday) => formatDate(weekday, "EEEEE"),
              }}
              disabled={disabledRange()}
              components={{
                CaptionLabel: (props) => (
                  <CustomCaptionLabel
                    showYears={showYears}
                    setShowYears={setShowYears}
                    currentYear={currentYear}
                    month={month}
                    setMonth={setMonth}
                    minDate={minDate}
                    maxDate={maxDate}
                  >
                    {props.children}
                  </CustomCaptionLabel>
                ),
              }}
              footer={
                showTime ? (
                  <TimeFooter
                    selectedDate={field.value}
                    onTimeChange={handleTimeChange}
                  />
                ) : undefined
              }
              hideNavigation
            />
          </div>
        </div>
      )}
    </>
  );
};
