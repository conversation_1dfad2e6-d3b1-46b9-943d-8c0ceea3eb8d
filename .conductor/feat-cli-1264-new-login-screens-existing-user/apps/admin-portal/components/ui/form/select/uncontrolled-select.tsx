"use client";

import {
  autoUpdate,
  flip,
  FloatingFocusManager,
  FloatingPortal,
  offset,
  shift,
  size,
  useClick,
  useDismiss,
  useFloating,
  useInteractions,
} from "@floating-ui/react";
import type { SelectProps as FlowbiteSelectProps } from "flowbite-react";
import { useState } from "react";
import { MdOutlineClear } from "react-icons/md";

import { cn } from "@/lib/utils";

type SelectProps = Omit<FlowbiteSelectProps, "name" | "onChange"> & {
  placeholder?: string;
  isDisabled?: boolean;
  options?:
    | { label: React.ReactNode; value: string }[]
    | Readonly<{ label: React.ReactNode; value: string; disabled?: boolean }[]>;
  onChange: (value?: string | null) => void;
  className?: string;
  value: string | null;
  currentLabel?: string;
};

const UncontrolledSelect = ({
  placeholder = "Select an option",
  isDisabled = false,
  options = [],
  onChange,
  className,
  value,
  currentLabel,
}: SelectProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const { refs, floatingStyles, context } = useFloating({
    open: isOpen,
    onOpenChange: setIsOpen,
    placement: "bottom-start",
    middleware: [
      offset(8),
      flip({
        fallbackPlacements: ["top-start"],
        fallbackStrategy: "bestFit",
        padding: 1,
        crossAxis: false,
      }),
      shift({
        padding: 1,
      }),
      size({
        apply({ rects, elements }) {
          Object.assign(elements.floating.style, {
            width: `${rects.reference.width}px`,
          });
        },
        padding: 1,
      }),
    ],
    whileElementsMounted: autoUpdate,
  });

  const { getReferenceProps, getFloatingProps } = useInteractions([
    useClick(context),
    useDismiss(context),
  ]);

  const selectedOption = options.find((opt) => opt.value === value);

  return (
    <div className={cn("relative", className)}>
      {/* Select Button */}
      <div
        ref={refs.setReference}
        {...getReferenceProps()}
        className="relative h-full w-full"
      >
        <button
          type="button"
          className={cn(
            "flex w-full items-center justify-between rounded-lg border bg-gray-50 px-2.5 py-2.5 text-left text-sm font-normal focus:outline-none focus:ring-2",
            "dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200",
            isDisabled
              ? "cursor-not-allowed border-gray-200 bg-gray-50 text-gray-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400"
              : "border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-600",
          )}
          disabled={isDisabled}
        >
          <span
            className={cn(
              "truncate",
              !selectedOption && "text-[#9ca3af] dark:text-[#9ca3af]",
            )}
          >
            {selectedOption
              ? currentLabel || selectedOption.label
              : placeholder}
          </span>
          {selectedOption ? (
            <span
              onClick={(e) => {
                e.stopPropagation();
                onChange?.("");
              }}
            >
              <MdOutlineClear className="size-4" />
            </span>
          ) : (
            <svg
              className={cn(
                "h-5 w-5 shrink-0 text-gray-400 transition-transform duration-200 dark:text-gray-400",
                isOpen ? "rotate-180 transform" : "",
              )}
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          )}
        </button>
      </div>

      {/* Dropdown Panel */}
      {isOpen && (
        <FloatingPortal>
          <FloatingFocusManager context={context} modal={false}>
            <div
              ref={refs.setFloating}
              style={floatingStyles}
              {...getFloatingProps()}
              className="z-50 rounded-lg border border-gray-200 bg-white py-1 shadow-lg dark:border-gray-600 dark:bg-gray-700"
            >
              <div className="select-options max-h-60 overflow-y-auto">
                {options.length === 0 ? (
                  <div className="flex h-10 items-center justify-center px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
                    No options available
                  </div>
                ) : (
                  options.map((option) => {
                    const isSelected = value === option.value;
                    return (
                      <button
                        key={option.value}
                        type="button"
                        className={cn(
                          "w-full px-3 py-2 text-left text-sm",
                          "truncate hover:bg-gray-100 dark:hover:bg-gray-600",
                          isSelected
                            ? "bg-primary-50 text-primary-600 dark:bg-primary-600/20 dark:text-primary-400"
                            : "text-gray-900 dark:text-gray-200",
                        )}
                        onClick={() => {
                          onChange(option.value);
                          setIsOpen(false);
                        }}
                      >
                        {option.label}
                      </button>
                    );
                  })
                )}
              </div>
            </div>
          </FloatingFocusManager>
        </FloatingPortal>
      )}
    </div>
  );
};

export { UncontrolledSelect };
