"use client";

import "react-day-picker/style.css";
import "../date-picker/date-picker.css";

import {
  format as formatDate,
  setHours,
  setMilliseconds,
  setMinutes,
  setSeconds,
} from "date-fns";
import { Popover } from "flowbite-react";
import { AlertCircle, Calendar, X } from "lucide-react";
import { useState } from "react";
import { DayPicker } from "react-day-picker";
import { get, useController, useFormContext } from "react-hook-form";

import { cn } from "@/lib/utils";

import { Dropdown, DropdownContent, DropdownTrigger } from "../../dropdown";
import { CustomCaptionLabel } from "../date-picker/custom-caption-label";
import { TimeFooter } from "../date-picker/time-footer";

export const DATE_FORMATS = {
  "mm/dd/yyyy": "LL/dd/yyyy",
  "dd/mm/yyyy": "dd/LL/yyyy",
  "yyyy/mm/dd": "yyyy/LL/dd",
} as const;

type DateFormat = keyof typeof DATE_FORMATS;

export type Props = {
  name: string;
  placeholderText?: string;
  className?: string;
  format?: DateFormat;
  showTime?: boolean;
  minDate?: Date;
  maxDate?: Date;
};

const formatDateValue = (date: Date, format: DateFormat, showTime: boolean) => {
  let dateString = formatDate(date, DATE_FORMATS[format]);

  if (showTime) {
    dateString += `, ${formatDate(date, "hh:mm aaa")}`;
  }

  return dateString;
};

export const DateTimeCell = ({
  name,
  className,
  placeholderText = "Click to select date",
  format = "mm/dd/yyyy",
  showTime = false,
  minDate,
  maxDate,
}: Props) => {
  const { control } = useFormContext();
  const [isEditing, setIsEditing] = useState(false);
  const [month, setMonth] = useState<Date>(new Date());
  const [showYears, setShowYears] = useState(false);

  const {
    field,
    formState: { errors },
  } = useController({
    name,
    control,
  });

  const errorObj = get(errors, name);
  const errorMessage = errorObj?.message?.valueOf();
  const hasError = typeof errorMessage === "string";

  const currentYear = month.getFullYear();

  const displayValue = field.value
    ? formatDateValue(field.value, format, showTime)
    : placeholderText;

  const handleCellClick = () => {
    if (field.disabled) return;
    setIsEditing(true);
  };

  const handleDateSelect = (date?: Date) => {
    if (!date) {
      return;
    }

    if (field.value) {
      // Preserve existing time
      const dateWithTime = setMilliseconds(
        setSeconds(
          setMinutes(
            setHours(date, field.value.getHours()),
            field.value.getMinutes(),
          ),
          field.value.getSeconds(),
        ),
        field.value.getMilliseconds(),
      );
      field.onChange(dateWithTime);
    } else {
      if (showTime) {
        // Set current time for new date
        const now = new Date();
        const dateWithCurrentTime = setMilliseconds(
          setSeconds(
            setMinutes(setHours(date, now.getHours()), now.getMinutes()),
            0,
          ),
          0,
        );
        field.onChange(dateWithCurrentTime);
      } else {
        field.onChange(date);
      }
    }

    if (!showTime) {
      setIsEditing(false);
      field.onBlur();
    }
  };

  const handleTimeChange = (dateTime: Date) => {
    field.onChange(dateTime);
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    field.onChange(undefined);
    field.onBlur();
    setIsEditing(false);
  };

  const disabledRange = () => {
    if (minDate && maxDate) {
      return {
        after: maxDate,
        before: minDate,
      };
    }
    if (minDate) {
      return {
        before: minDate,
      };
    }
    if (maxDate) {
      return {
        after: maxDate,
      };
    }
    return field.disabled;
  };

  return (
    <div
      className={cn(
        "group/datetime relative flex w-full items-center gap-2 border border-gray-300 bg-white p-2 transition-colors hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-700 dark:hover:bg-gray-600",
        isEditing && "border-blue-400 shadow-sm dark:border-blue-500",
        hasError && "border-red-500",
        field.disabled && "cursor-not-allowed opacity-50",
        className,
      )}
      onClick={handleCellClick}
    >
      <Dropdown
        open={isEditing}
        onOpenChange={(open) => {
          setIsEditing(open);
          if (!open) field.onBlur();
        }}
        placement="bottom-start"
      >
        <DropdownTrigger className="flex w-full items-center justify-between">
          <div
            className={cn(
              "truncate text-gray-900 dark:text-white",
              !field.value && "text-gray-500 dark:text-gray-400",
            )}
          >
            {displayValue}
          </div>

          {field.value ? (
            <button
              type="button"
              onClick={handleClear}
              disabled={field.disabled}
              className="ml-2 flex items-center justify-center rounded-full text-gray-400 opacity-0 transition-opacity hover:text-gray-600 group-hover/datetime:opacity-100 dark:text-gray-500 dark:hover:text-gray-300"
            >
              <X className="size-3" />
            </button>
          ) : (
            <Calendar
              className={cn(
                "ml-2 size-3 flex-shrink-0 text-gray-400 opacity-0 transition-opacity group-hover/datetime:opacity-100 dark:text-gray-500",
                field.disabled && "hidden",
              )}
            />
          )}
        </DropdownTrigger>

        <DropdownContent className="rounded-xl dark:bg-[#1f2937]">
          <DayPicker
            mode="single"
            selected={field.value}
            onSelect={handleDateSelect}
            month={month}
            onMonthChange={setMonth}
            className="w-fit p-3"
            formatters={{
              formatWeekdayName: (weekday) => formatDate(weekday, "EEEEE"),
            }}
            disabled={disabledRange()}
            components={{
              CaptionLabel: (props) => (
                <CustomCaptionLabel
                  showYears={showYears}
                  setShowYears={setShowYears}
                  currentYear={currentYear}
                  month={month}
                  setMonth={setMonth}
                  minDate={minDate}
                  maxDate={maxDate}
                >
                  {props.children}
                </CustomCaptionLabel>
              ),
            }}
            footer={
              showTime ? (
                <TimeFooter
                  selectedDate={field.value}
                  onTimeChange={handleTimeChange}
                />
              ) : undefined
            }
            hideNavigation
          />
        </DropdownContent>
      </Dropdown>

      {hasError && (
        <Popover
          placement="top"
          trigger="hover"
          content={
            <p className="px-2 py-1 text-red-600 dark:text-red-400">
              {errorMessage}
            </p>
          }
        >
          <AlertCircle className="ml-auto h-4 w-4 cursor-help text-red-500" />
        </Popover>
      )}
    </div>
  );
};
