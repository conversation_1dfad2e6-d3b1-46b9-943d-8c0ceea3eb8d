import type { ColumnDef } from "@tanstack/react-table";

import { Table } from "@/components/ui/table";

type TableLoadingProps = {
  columns: ColumnDef<any>[];
  length?: number;
};

export const TableLoading = ({ columns, length }: TableLoadingProps) => {
  return (
    <Table
      columns={columns}
      data={Array.from({ length: length ?? 4 }).map((_, idx) => ({ id: idx }))}
      isLoading
    />
  );
};
