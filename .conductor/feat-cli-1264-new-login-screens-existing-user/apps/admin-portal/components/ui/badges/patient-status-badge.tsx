import { cva } from "class-variance-authority";
import { useTranslations } from "next-intl";

import { cn } from "@/lib/utils";

import { PillBadge } from "./pill-badge";

export type PatientStatus =
  | "preScreening"
  | "screening"
  | "enrolled"
  | "withdrawn"
  | "complete"
  | "screenFailed";

const patientStatusVariants = cva(
  "px-2.5 py-0.5 rounded-md text-xs font-medium leading-4.5 whitespace-nowrap before:w-1.5 before:h-1.5 before:rounded-full before:mr-1.5 before:inline-block",
  {
    variants: {
      variant: {
        default:
          "bg-gray-100 text-gray-900 dark:bg-gray-700 dark:text-white before:bg-gray-900 dark:before:bg-white",
        preScreening:
          "bg-teal-100 text-teal-800 dark:bg-teal-700 dark:text-teal-200 before:bg-teal-800 dark:before:bg-teal-200",
        screening:
          "bg-blue-100 text-blue-800 dark:bg-blue-700 dark:text-blue-200 before:bg-blue-800 dark:before:bg-blue-200",
        enrolled:
          "bg-green-100 text-green-800 dark:bg-green-700 dark:text-green-200 before:bg-green-800 dark:before:bg-green-200",
        withdrawn:
          "bg-orange-100 text-orange-800 dark:bg-orange-700 dark:text-orange-200 before:bg-orange-800 dark:before:bg-orange-200",
        complete:
          "bg-purple-100 text-purple-800 dark:bg-purple-700 dark:text-purple-200 before:bg-purple-800 dark:before:bg-purple-200",
        screenFailed:
          "bg-red-100 text-red-800 dark:bg-red-700 dark:text-red-200 before:bg-red-800 dark:before:bg-red-200",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
);

type StatusProps = {
  className?: string;
  variant: PatientStatus;
};

export const PatientStatusBadge = ({ variant, className }: StatusProps) => {
  const t = useTranslations("PatientStatus");
  return (
    <PillBadge
      className={cn(
        patientStatusVariants({ variant }),
        "gap-0 rounded-2xl",
        className,
      )}
    >
      {t(variant)}
    </PillBadge>
  );
};
