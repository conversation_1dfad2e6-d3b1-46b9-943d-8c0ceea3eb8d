import type { VariantProps } from "class-variance-authority";
import { cva } from "class-variance-authority";
import { Badge } from "flowbite-react";

import { cn } from "@/lib/utils";

const formTypeBadgeVariants = cva("w-fit text-xs font-medium", {
  variants: {
    variant: {
      EDC: "bg-green-50 text-green-400 dark:bg-green-900/30 dark:text-green-400",
      eSource:
        "bg-teal-50 text-teal-500 dark:bg-teal-900/30 dark:text-teal-400",
    },
  },
  defaultVariants: {
    variant: "EDC",
  },
});

type FormTypeBadgeProps = VariantProps<typeof formTypeBadgeVariants> & {
  className?: string;
  children: React.ReactNode;
};

export const FormTypeBadge = ({
  className,
  variant,
  children,
}: FormTypeBadgeProps) => {
  return (
    <Badge className={cn(formTypeBadgeVariants({ variant }), className)}>
      {children}
    </Badge>
  );
};
