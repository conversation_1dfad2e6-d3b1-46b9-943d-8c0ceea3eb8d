import type { VariantProps } from "class-variance-authority";
import { cva } from "class-variance-authority";
import { PropsWithChildren } from "react";

import { cn } from "@/lib/utils";

import { PillBadge } from "./pill-badge";

const EXPIRATION_WARNING_DAYS = 90;

const expirationStatusVariants = cva(
  "px-2.5 py-0.5 rounded-md text-xs font-medium leading-4.5 whitespace-nowrap before:w-1.5 before:h-1.5 before:rounded-full before:mr-1.5 before:inline-block",
  {
    variants: {
      variant: {
        valid:
          "bg-green-100 text-green-500 before:bg-green-500 dark:bg-green-900 dark:text-green-300 dark:before:bg-green-300",
        expiring:
          "bg-orange-100 text-orange-500 before:bg-orange-500 dark:bg-orange-900 dark:text-orange-300 dark:before:bg-orange-300",
        expired:
          "bg-red-100 text-red-500 before:bg-red-500 dark:bg-red-900 dark:text-red-300 dark:before:bg-red-300",
      },
    },
    defaultVariants: {
      variant: "valid",
    },
  },
);

type ExpirationStatusProps = VariantProps<typeof expirationStatusVariants> & {
  className?: string;
  expirationDate?: Date | string;
};

export const ExpirationStatusBadge = ({
  variant,
  expirationDate,
  className,
  children,
}: PropsWithChildren<ExpirationStatusProps>) => {
  const getVariantFromDate = () => {
    if (!expirationDate) return "valid";

    try {
      const expDate = new Date(expirationDate);

      // Check if the date is valid
      if (isNaN(expDate.getTime())) {
        console.warn("Invalid expiration date provided");
        return "valid";
      }

      const now = new Date();
      // Set both dates to start of day to avoid time-of-day comparison issues
      expDate.setHours(0, 0, 0, 0);
      now.setHours(0, 0, 0, 0);
      const MS_PER_DAY = 1000 * 60 * 60 * 24;
      const daysUntilExpiration = Math.ceil(
        (now.getTime() - expDate.getTime()) / MS_PER_DAY,
      );

      if (daysUntilExpiration < 0) return "expired";
      if (daysUntilExpiration <= EXPIRATION_WARNING_DAYS) return "expiring";
      return "valid";
    } catch (error) {
      console.error("Error processing expiration date:", error);
      return "valid";
    }
  };

  const effectiveVariant = variant || getVariantFromDate();

  return (
    <PillBadge
      className={cn(
        expirationStatusVariants({ variant: effectiveVariant }),
        "gap-0 rounded-2xl capitalize",
        className,
      )}
    >
      {children || effectiveVariant}
    </PillBadge>
  );
};
