import type { RequiredDeep } from "type-fest";

import { AddressFormFields } from "@/components/shared/address-form-fields";
import { <PERSON>ton, CloseButton } from "@/components/ui/button";
import { Form, InputField } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { Modal } from "@/components/ui/modal";
import type { Contact, CreateContactPayload } from "@/lib/apis/contacts";
import { addSponsorContactPayloadSchema } from "@/lib/apis/sponsors/types";

type ContactModalProps = {
  show: boolean;
  onClose: () => void;
  onSubmit: (
    data: CreateContactPayload | RequiredDeep<CreateContactPayload>,
  ) => void;
  isLoading: boolean;
  isEditing?: boolean;
  contact?: Contact;
};

export const ContactModal = ({
  show,
  onClose,
  onSubmit,
  isLoading,
  isEditing,
  contact,
}: ContactModalProps) => {
  return (
    <Modal show={show} onClose={onClose}>
      <Modal.Header>{isEditing ? "Edit Contact" : "Add Contact"}</Modal.Header>
      <Modal.Body>
        <Form
          schema={addSponsorContactPayloadSchema}
          onSubmit={onSubmit}
          defaultValues={{
            role: contact?.role || "",
            firstName: contact?.firstName || "",
            lastName: contact?.lastName || "",
            email: contact?.email || "",
            phone: contact?.phone || "",
            address: {
              addressLine: contact?.address?.addressLine || "",
              city: contact?.address?.city || "",
              stateProvinceId: contact?.address?.stateProvince?.id || "",
              zipPostalCode: contact?.address?.zipPostalCode || "",
              countryId: contact?.address?.country?.id || "",
            },
          }}
        >
          <ContactForm />
          <div className="mt-4 flex flex-col justify-end gap-2 sm:mt-6 sm:flex-row">
            <CloseButton onClose={onClose} />
            <Button type="submit" variant="primary" isLoading={isLoading}>
              {isEditing ? "Save" : "Add Contact"}
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};

const ContactForm = () => {
  return (
    <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 sm:gap-4">
      <div className="space-y-1">
        <Label htmlFor="firstName">First Name</Label>
        <InputField id="firstName" name="firstName" placeholder="First Name" />
      </div>

      <div className="space-y-1">
        <Label htmlFor="lastName">Last Name</Label>
        <InputField id="lastName" name="lastName" placeholder="Last Name" />
      </div>

      <div className="space-y-1">
        <Label htmlFor="email">Email</Label>
        <InputField id="email" name="email" placeholder="Email" />
      </div>

      <div className="space-y-1">
        <Label htmlFor="phone">Phone</Label>
        <InputField id="phone" name="phone" placeholder="Phone" />
      </div>

      <AddressFormFields isShowRoleField />
    </div>
  );
};
