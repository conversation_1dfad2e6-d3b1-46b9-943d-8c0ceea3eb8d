import { X } from "lucide-react";
import { ReactNode } from "react";

import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Modal } from "@/components/ui/modal";
import { cn } from "@/lib/utils";

type EditorModalProps = {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: ReactNode;
  formId: string;
  isValid: boolean;
  isSubmitting?: boolean;
  leftFooterAction?: ReactNode;
  className?: string;
};

export const EditorModal = ({
  isOpen,
  onClose,
  title,
  children,
  formId,
  isValid,
  isSubmitting = false,
  leftFooterAction,
  className,
}: EditorModalProps) => {
  return (
    <Modal
      show={isOpen}
      theme={{
        body: {
          base: "h-full max-h-full max-w-full",
        },
        content: {
          base: "h-full max-h-full w-full !max-w-full p-4",
          inner: "h-full max-h-full max-w-full",
        },
      }}
    >
      <Modal.Body>
        <div className="h-full bg-white p-4 dark:bg-gray-900">
          <div className="flex h-full flex-col rounded border border-gray-200 dark:border-gray-700">
            {/* Header */}
            <div className="flex items-center justify-between gap-2 p-5">
              <h3 className="text-2xl font-bold text-gray-900 lg:text-3xl dark:text-white">
                {title}
              </h3>
              <button
                onClick={onClose}
                className="rounded-full p-2 text-gray-500 transition-colors hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Scrollable Content */}
            <div className={cn("flex-1 overflow-y-auto px-5 pb-5", className)}>
              {children}
            </div>

            {/* Footer */}
            <div
              className={cn(
                "flex flex-col gap-4 border-t border-gray-200 bg-gray-50 p-5 sm:flex-row sm:gap-5 dark:border-gray-700 dark:bg-gray-800",
                leftFooterAction ? "justify-between" : "justify-end",
              )}
            >
              {leftFooterAction}
              <div className="flex flex-col gap-4 sm:flex-row sm:gap-5">
                <CloseButton onClose={onClose} />
                <Button
                  disabled={!isValid || isSubmitting}
                  type="submit"
                  form={formId}
                  variant="primary"
                  isLoading={isSubmitting}
                >
                  Save
                </Button>
              </div>
            </div>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};
