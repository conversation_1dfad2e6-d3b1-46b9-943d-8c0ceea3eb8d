import React from "react";
import { AiOutlineLoading3Quarters } from "react-icons/ai";

import { cn } from "@/lib/utils";

type Props = {
  children: React.ReactNode;
  isLoading: boolean;
  className?: string;
};

const LoadingWrapper = ({ children, isLoading, className }: Props) => {
  return (
    <div className={cn("relative", className)}>
      <div
        className={cn(
          "transition-opacity duration-300",
          isLoading && "pointer-events-none opacity-50",
        )}
      >
        {children}
      </div>
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <AiOutlineLoading3Quarters className="size-6 animate-spin dark:text-white" />
        </div>
      )}
    </div>
  );
};

export default LoadingWrapper;
