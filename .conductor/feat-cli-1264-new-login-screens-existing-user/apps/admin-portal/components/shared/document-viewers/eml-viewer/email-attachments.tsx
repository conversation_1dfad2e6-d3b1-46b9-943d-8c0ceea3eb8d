import { useCallback } from "react";
import { FiDownload, FiPaperclip } from "react-icons/fi";

import { EmailAttachment } from ".";
import { downloadAttachment } from "./utils";

interface EmailAttachmentsProps {
  attachments: EmailAttachment[];
}

export const EmailAttachments = ({ attachments }: EmailAttachmentsProps) => {
  const handleDownloadAttachment = useCallback(
    (attachment: EmailAttachment) => {
      downloadAttachment(attachment);
    },
    [],
  );

  if (!attachments || attachments.length === 0) {
    return null;
  }

  return (
    <div className="email-attachments mt-4 border-t border-gray-200 pt-4 dark:border-gray-700">
      <h3 className="mb-2 flex items-center font-semibold text-gray-900 dark:text-white">
        <FiPaperclip className="mr-2" />
        Attachments ({attachments.length})
      </h3>
      <ul className="space-y-2">
        {attachments.map((attachment, index) => (
          <li
            key={index}
            className="flex items-center justify-between rounded-md bg-gray-100 p-2 dark:bg-gray-700"
          >
            <span className="text-sm text-gray-700 dark:text-gray-300">
              {attachment.filename}
              {attachment.size && (
                <span className="ml-2 text-xs text-gray-500">
                  ({Math.round(attachment.size / 1024)} KB)
                </span>
              )}
            </span>
            {attachment.content && (
              <button
                onClick={() => handleDownloadAttachment(attachment)}
                className="ml-2 flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs text-blue-600 hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-900/50"
              >
                <FiDownload className="mr-1" />
                Download
              </button>
            )}
          </li>
        ))}
      </ul>
    </div>
  );
};
