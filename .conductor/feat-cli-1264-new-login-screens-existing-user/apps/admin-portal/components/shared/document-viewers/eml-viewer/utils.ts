import { <PERSON><PERSON><PERSON>ddress, <PERSON><PERSON><PERSON>ttachment, EmailContent } from ".";

/**
 * Parse an email address string into name and email components
 * Handles formats like: "<PERSON> <<EMAIL>>", "<EMAIL>",
 * or complex formats from email clients
 */
export const parseEmailAddress = (address: string): EmailAddress => {
  if (!address || typeof address !== "string") {
    console.warn("Invalid email address format:", address);
    // Return the original value instead of defaulting to unknown
    return { email: String(address) || "" };
  }

  // Try to extract email from angle brackets format: "Name <<EMAIL>>"
  const angleMatch = address.match(/([^<]*)<([^>]*)>/);
  if (angleMatch) {
    const [, name, email] = angleMatch;
    return {
      name: name?.trim() || undefined,
      // Use the extracted email if available, otherwise preserve the original string
      email: email?.trim() || address.trim(),
    };
  }

  // Try to extract email from quoted format: "Name" <<EMAIL>>
  const quotedMatch = address.match(/"([^"]*)"\s*<?([^>]*)>?/);
  if (quotedMatch) {
    const [, name, email] = quotedMatch;
    return {
      name: name?.trim() || undefined,
      // Use the extracted email if available, otherwise preserve the original string
      email: email?.trim() || address.trim(),
    };
  }

  // Check if it's just an email address
  const emailMatch = address.match(
    /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/,
  );
  if (emailMatch) {
    return { email: emailMatch[1] };
  }

  // Fallback: just use the whole string as email
  return { email: address.trim() };
};

/**
 * Parse email addresses from a comma-separated string
 * Handles various formats and edge cases
 */
export const parseEmailAddresses = (addressesStr?: string): EmailAddress[] => {
  if (!addressesStr) return [];
  if (typeof addressesStr !== "string") {
    console.warn("Invalid email addresses string:", addressesStr);
    // Return the original value instead of defaulting to unknown
    return [{ email: String(addressesStr) || "" }];
  }

  // Handle quoted names with commas inside them
  // This is a simplified approach - a full parser would be more complex
  const addresses: EmailAddress[] = [];
  let inQuotes = false;
  let currentAddress = "";

  for (let i = 0; i < addressesStr.length; i++) {
    const char = addressesStr[i];

    if (char === '"' && (i === 0 || addressesStr[i - 1] !== "\\")) {
      inQuotes = !inQuotes;
      currentAddress += char;
    } else if (char === "," && !inQuotes) {
      // End of an address
      if (currentAddress.trim()) {
        addresses.push(parseEmailAddress(currentAddress.trim()));
      }
      currentAddress = "";
    } else {
      currentAddress += char;
    }
  }

  // Add the last address if there is one
  if (currentAddress.trim()) {
    addresses.push(parseEmailAddress(currentAddress.trim()));
  }

  // If the complex parsing failed or returned empty, fall back to simple splitting
  if (addresses.length === 0) {
    return addressesStr.split(/\s*,\s*/).map(parseEmailAddress);
  }

  return addresses;
};

/**
 * Format email addresses for display
 */
export const formatEmailAddresses = (addresses?: EmailAddress[]): string => {
  if (!addresses || addresses.length === 0) return "";
  return addresses
    .map((addr) => (addr.name ? `${addr.name} <${addr.email}>` : addr.email))
    .join(", ");
};

/**
 * Extract email address from a MIME header item
 * This is a helper function to handle various formats of email addresses in MIME headers
 */
export const extractEmailFromMimeItem = (item: any): EmailAddress => {
  console.log("Extracting email from MIME item:", item);

  // Handle different possible structures
  if (item.value && typeof item.value === "string") {
    const decodedValue = decodeRFC2047(item.value);
    console.log("Decoded value:", decodedValue);
    return parseEmailAddress(decodedValue);
  } else if (item.value && typeof item.value === "object") {
    console.log("Value is an object:", item.value);
    // Handle address objects directly
    if (item.value.name && item.value.address) {
      return {
        name: decodeRFC2047(String(item.value.name)),
        email: String(item.value.address),
      };
    } else if (Array.isArray(item.value)) {
      // Handle array of addresses
      console.log("Value is an array:", item.value);
      if (item.value.length > 0) {
        const firstAddress = item.value[0];
        if (typeof firstAddress === "string") {
          return parseEmailAddress(decodeRFC2047(firstAddress));
        } else if (firstAddress && typeof firstAddress === "object") {
          if (firstAddress.name && firstAddress.address) {
            return {
              name: decodeRFC2047(String(firstAddress.name)),
              email: String(firstAddress.address),
            };
          }
          // Try to extract email from any property that might contain it
          for (const prop of ["address", "email", "addr", "mail", "value"]) {
            if (firstAddress[prop] && typeof firstAddress[prop] === "string") {
              return { email: firstAddress[prop] };
            }
          }
        }
      }
    }
  }

  // If we get here, try to extract something useful from the item
  if (item && typeof item === "object") {
    console.log("Trying to extract from raw item:", item);
    // Try various properties that might contain the address
    for (const prop of ["address", "email", "addr", "mail", "value"]) {
      if (item[prop] && typeof item[prop] === "string") {
        return { email: item[prop] };
      }
    }

    // If there's a text property, try to use that
    if (item.text && typeof item.text === "string") {
      return parseEmailAddress(decodeRFC2047(item.text));
    }
  }

  // Last resort: if we have a raw item that's a string, try to parse it
  if (typeof item === "string") {
    return parseEmailAddress(decodeRFC2047(item));
  }

  // If all else fails, use a placeholder but log the issue
  console.warn("Could not extract email address from item:", item);
  return { email: "<EMAIL>" };
};

/**
 * Convert a MimeNode from emailjs-mime-parser to our EmailContent format
 */
export const convertMimeNodeToEmailContent = (mimeNode: any): EmailContent => {
  try {
    if (!mimeNode) {
      console.error("Invalid MIME node:", mimeNode);
      throw new Error("Invalid MIME structure");
    }

    const result: EmailContent = {
      headers: {},
      attachments: [],
    };

    // Process headers
    if (mimeNode.headers) {
      // Log the entire headers structure for debugging
      console.log(
        "MIME headers structure:",
        JSON.stringify(mimeNode.headers, null, 2),
      );

      // Convert headers to our format
      const headers: Record<string, string> = {};
      Object.keys(mimeNode.headers).forEach((key) => {
        const header = mimeNode.headers[key];
        if (Array.isArray(header) && header.length > 0) {
          // Decode RFC 2047 encoded headers
          if (header[0].value !== undefined) {
            headers[key] = decodeRFC2047(String(header[0].value));
          }
        }
      });
      result.headers = headers;

      // Extract basic email information from headers
      result.subject = headers["Subject"] || headers["subject"];
      result.date = headers["Date"] || headers["date"];

      // Handle email addresses directly from the MIME structure
      // From addresses
      if (mimeNode.headers["from"] && Array.isArray(mimeNode.headers["from"])) {
        console.log(
          "From header structure:",
          JSON.stringify(mimeNode.headers["from"]),
        );
        result.from = mimeNode.headers["from"].map(extractEmailFromMimeItem);
      } else {
        result.from = parseEmailAddresses(headers["From"] || headers["from"]);
      }

      // To addresses
      if (mimeNode.headers["to"] && Array.isArray(mimeNode.headers["to"])) {
        console.log(
          "To header structure:",
          JSON.stringify(mimeNode.headers["to"]),
        );
        // Enhanced logging for debugging the To field
        const extractedAddresses = mimeNode.headers["to"].map((item) => {
          console.log("Processing 'to' item:", item);
          const extracted = extractEmailFromMimeItem(item);
          console.log("Extracted result:", extracted);
          return extracted;
        });
        result.to = extractedAddresses;
      } else {
        const parsedAddresses = parseEmailAddresses(
          headers["To"] || headers["to"],
        );
        console.log("Parsed 'to' addresses from headers:", parsedAddresses);
        result.to = parsedAddresses;
      }

      // CC addresses
      if (mimeNode.headers["cc"] && Array.isArray(mimeNode.headers["cc"])) {
        result.cc = mimeNode.headers["cc"].map(extractEmailFromMimeItem);
      } else {
        result.cc = parseEmailAddresses(headers["Cc"] || headers["cc"]);
      }

      // BCC addresses
      if (mimeNode.headers["bcc"] && Array.isArray(mimeNode.headers["bcc"])) {
        result.bcc = mimeNode.headers["bcc"].map(extractEmailFromMimeItem);
      } else {
        result.bcc = parseEmailAddresses(headers["Bcc"] || headers["bcc"]);
      }
    }

    // Process content and child nodes
    const processNode = (node: any) => {
      const contentType = node.contentType?.value || "";
      const contentDisposition =
        node.headers?.["Content-Disposition"]?.[0]?.value ||
        node.headers?.["content-disposition"]?.[0]?.value ||
        "";

      // Handle leaf nodes with content
      if (node.content && node.content.length > 0) {
        if (
          contentType.includes("text/plain") &&
          !contentDisposition.includes("attachment")
        ) {
          // Plain text content
          result.text = uint8ArrayToString(node.content);
        } else if (
          contentType.includes("text/html") &&
          !contentDisposition.includes("attachment")
        ) {
          // HTML content
          result.html = uint8ArrayToString(node.content);
        } else if (
          contentDisposition.includes("attachment") ||
          (!contentType.includes("text/") &&
            !contentType.includes("multipart/"))
        ) {
          // This is an attachment
          let filename = "attachment";

          // Try to extract filename from Content-Disposition
          const filenameMatch =
            contentDisposition.match(/filename="?([^"]+)"?/);
          if (filenameMatch) {
            filename = filenameMatch[1];
          } else {
            // Try to extract filename from Content-Type name parameter
            const nameMatch = contentType.match(/name="?([^"]+)"?/);
            if (nameMatch) {
              filename = nameMatch[1];
            }
          }

          // Get content ID if available
          const contentId =
            node.headers?.["Content-ID"]?.[0]?.value ||
            node.headers?.["content-id"]?.[0]?.value ||
            "";

          // Add to attachments
          if (!result.attachments) result.attachments = [];
          result.attachments.push({
            filename,
            contentType,
            size: node.content.length,
            content: node.content,
            contentId: contentId.replace(/[<>]/g, ""),
            contentDisposition,
          });
        }
      }

      // Process child nodes recursively
      if (node.childNodes && node.childNodes.length > 0) {
        node.childNodes.forEach(processNode);
      }
    };

    // Start processing from the root node
    processNode(mimeNode);

    return result;
  } catch (error) {
    console.error("Error converting MIME node to email content:", error);
    throw new Error("Failed to parse email content");
  }
};

/**
 * Convert a Uint8Array to a string using proper UTF-8 decoding
 */
export const uint8ArrayToString = (array: any, charset = "utf-8"): string => {
  if (!array || !(array instanceof Uint8Array)) {
    console.warn("uint8ArrayToString received invalid input:", array);
    return String(array) || "";
  }

  try {
    return new TextDecoder(charset).decode(array);
  } catch {
    // Fallback to UTF-8 if the specified charset is not supported
    try {
      return new TextDecoder("utf-8").decode(array);
    } catch (fallbackError) {
      console.error("Error decoding Uint8Array:", fallbackError);
      // Last resort: try to convert bytes to characters directly
      return Array.from(array)
        .map((byte) => String.fromCharCode(byte))
        .join("");
    }
  }
};

/**
 * Simple implementation of quoted-printable decoding for RFC 2047
 */
export const decodeQuotedPrintable = (text: string): string => {
  return text
    .replace(/=\r?\n/g, "") // Remove soft line breaks
    .replace(/=(\w{2})/g, (_, hex) => String.fromCharCode(parseInt(hex, 16)));
};

/**
 * Decode RFC 2047 encoded words in headers
 * Format: =?charset?encoding?encoded-text?=
 */
export const decodeRFC2047 = (text: any): string => {
  // Ensure text is a string
  if (!text) return "";
  if (typeof text !== "string") {
    console.warn("decodeRFC2047 received non-string value:", text);
    return String(text) || "";
  }

  // First try to decode RFC 2047 encoded words
  const decodedText = text.replace(
    /=\?([^?]+)\?([BbQq])\?([^?]*)\?=/g,
    (_, charset, encoding, encodedText) => {
      try {
        if (encoding.toUpperCase() === "B") {
          // Base64 encoding
          const binary = window.atob(encodedText);
          // Try to convert to the specified charset, fallback to UTF-8
          try {
            return new TextDecoder(charset).decode(
              new Uint8Array(binary.length).map((_, i) => binary.charCodeAt(i)),
            );
          } catch {
            // If charset is not supported, try UTF-8
            return new TextDecoder().decode(
              new Uint8Array(binary.length).map((_, i) => binary.charCodeAt(i)),
            );
          }
        } else if (encoding.toUpperCase() === "Q") {
          // Quoted-printable encoding
          return decodeQuotedPrintable(encodedText.replace(/_/g, " "));
        }
        return encodedText; // Fallback
      } catch (e) {
        console.error("Error decoding RFC2047:", e);
        return text;
      }
    },
  );

  // Return the decoded text
  return decodedText;
};

export const downloadAttachment = (attachment: EmailAttachment): void => {
  if (!attachment.content) {
    console.error("Attachment content is not available");
    return;
  }

  const blob = arrayToBlob(attachment.content, attachment.contentType);
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = attachment.filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

export const arrayToBlob = (array: Uint8Array, contentType: string): Blob => {
  return new Blob([array], { type: contentType });
};
