"use client";

import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";
import "./styles.css";

import { Tooltip } from "flowbite-react";
import { TextItem } from "pdfjs-dist/types/src/display/api";
import { useCallback, useEffect, useRef, useState } from "react";
import {
  IoChevronBack,
  IoChevronForward,
  IoClose,
  IoSearch,
} from "react-icons/io5";
import {
  PiMagnifyingGlassMinusLight,
  PiMagnifyingGlassPlusLight,
} from "react-icons/pi";
import { TbRotate, TbRotateClockwise } from "react-icons/tb";
import { Document, Page, pdfjs } from "react-pdf";
import { useResizeObserver } from "usehooks-ts";

import { useDebounce } from "@/hooks/use-debounce";

import { DocumentError } from "../error-document";
import { LoadingDocument } from "../loading-document";

pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`;

const options = {
  standardFontDataUrl: "/standard_fonts/",
};

const MIN_SCALE = 0.5;
const MAX_SCALE = 1.5;

type IconButtonProps = {
  icon: React.ComponentType<{ size?: number }>;
  onClick: () => void;
  disabled?: boolean;
  tooltip: string;
  active?: boolean;
} & React.ButtonHTMLAttributes<HTMLButtonElement>;

const IconButton = ({
  icon: Icon,
  onClick,
  disabled,
  tooltip,
  active,
  className,
  ...props
}: IconButtonProps) => (
  <Tooltip content={tooltip} placement="bottom">
    <button
      onClick={onClick}
      disabled={disabled}
      className={`
        group relative flex h-8 w-8 items-center justify-center rounded-md transition-all duration-200
        ${
          active
            ? "bg-blue-100 text-blue-600 dark:bg-blue-900/50 dark:text-blue-400"
            : "text-gray-600 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-200"
        }
        ${disabled ? "cursor-not-allowed opacity-40" : "cursor-pointer"}
        ${className || ""}
      `}
      {...props}
    >
      <Icon size={16} />
    </button>
  </Tooltip>
);

const highlightPattern = (text: string, pattern: string) => {
  if (!pattern.trim()) return text;

  const regex = new RegExp(
    `(${pattern.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")})`,
    "gi",
  );
  return text.replace(
    regex,
    '<mark style="background-color: yellow; color: black;">$1</mark>',
  );
};

export function PDFViewer({ url }: { url: string }) {
  const [numPages, setNumPages] = useState<number>();
  const [currentPage, setCurrentPage] = useState(1);
  const [scale, setScale] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [searchText, setSearchText] = useState("");
  const [width, setWidth] = useState<number>();
  const [showSearch, setShowSearch] = useState(false);
  const [isDocumentLoaded, setIsDocumentLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  const debouncedSearchText = useDebounce(searchText, 300);

  const containerRef = useRef<HTMLDivElement>(null);
  const pageRefs = useRef<Array<HTMLDivElement | null>>([]);
  const observerRef = useRef<IntersectionObserver | null>(null);

  function onDocumentLoadSuccess({ numPages }: { numPages: number }): void {
    setNumPages(numPages);
    pageRefs.current = Array(numPages).fill(null);
    setIsDocumentLoaded(true);
    setHasError(false);
  }

  function onDocumentLoadError(): void {
    setHasError(true);
    setIsDocumentLoaded(false);
  }

  // Reset states when URL changes
  useEffect(() => {
    setIsDocumentLoaded(false);
    setHasError(false);
    setShowSearch(false);
  }, [url]);

  useEffect(() => {
    if (!numPages || pageRefs.current.length === 0) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const pageIndex = pageRefs.current.findIndex(
              (ref) => ref === entry.target,
            );
            if (pageIndex !== -1) {
              setCurrentPage(pageIndex + 1);
            }
          }
        });
      },
      {
        root: containerRef.current,
        rootMargin: "-50% 0px -50% 0px",
        threshold: 0,
      },
    );

    pageRefs.current.forEach((pageRef) => {
      if (pageRef) {
        observerRef.current?.observe(pageRef);
      }
    });

    return () => {
      observerRef.current?.disconnect();
    };
  }, [numPages]);

  const textRenderer = useCallback(
    (textItem: TextItem) => {
      return debouncedSearchText.trim()
        ? highlightPattern(textItem.str, debouncedSearchText)
        : textItem.str;
    },
    [debouncedSearchText],
  );

  useResizeObserver({
    ref: containerRef,
    onResize: (size) => {
      setWidth(size.width ?? undefined);
    },
  });

  const handleZoomIn = () => {
    setScale((prev) => Math.min(prev + 0.1, MAX_SCALE));
  };

  const handleZoomOut = () => {
    setScale((prev) => Math.max(prev - 0.1, MIN_SCALE));
  };

  const handleRotateRight = () => {
    setRotation((prev) => (prev + 90) % 360);
  };

  const handleRotateLeft = () => {
    setRotation((prev) => (prev - 90 + 360) % 360);
  };

  const scrollToPage = (pageNumber: number) => {
    const pageElement = pageRefs.current[pageNumber - 1];
    if (pageElement) {
      pageElement.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
      setCurrentPage(pageNumber);
    }
  };

  const handleNextPage = () => {
    if (numPages && currentPage < numPages) {
      scrollToPage(currentPage + 1);
    }
  };

  const handlePrevPage = () => {
    if (currentPage > 1) {
      scrollToPage(currentPage - 1);
    }
  };

  const clearSearch = () => {
    setSearchText("");
    setShowSearch(false);
  };

  return (
    <div className="flex h-full flex-col">
      {isDocumentLoaded && !hasError && (
        <div className="flex items-center justify-between border-b bg-white px-4 py-2 shadow-sm dark:border-gray-600 dark:bg-gray-800">
          <div className="flex items-center gap-1">
            <IconButton
              icon={IoChevronBack}
              onClick={handlePrevPage}
              disabled={currentPage <= 1}
              tooltip="Previous page"
            />
            <div className="mx-2 flex h-8 min-w-[70px] items-center justify-center rounded-md bg-gray-100 px-2 text-xs font-medium text-gray-700 shadow-sm dark:bg-gray-700 dark:text-gray-200">
              {currentPage} / {numPages || 0}
            </div>
            <IconButton
              icon={IoChevronForward}
              onClick={handleNextPage}
              disabled={!numPages || currentPage >= numPages}
              tooltip="Next page"
            />
          </div>

          <div className="flex items-center gap-1">
            <IconButton
              icon={PiMagnifyingGlassMinusLight}
              onClick={handleZoomOut}
              disabled={scale <= MIN_SCALE}
              tooltip="Zoom out"
              className="hidden sm:flex"
            />
            <div className="mx-1 hidden h-8 min-w-[50px] items-center justify-center rounded-md bg-gray-100 px-2 text-xs font-medium text-gray-700 shadow-sm sm:flex dark:bg-gray-700 dark:text-gray-200">
              {Math.round(scale * 100)}%
            </div>
            <IconButton
              icon={PiMagnifyingGlassPlusLight}
              onClick={handleZoomIn}
              disabled={scale >= MAX_SCALE}
              className="hidden sm:flex"
              tooltip="Zoom in"
            />
            <div className="mx-1 hidden h-4 w-px bg-gray-300 sm:block dark:bg-gray-500" />
            <IconButton
              icon={TbRotate}
              onClick={handleRotateLeft}
              className="hidden sm:flex"
              tooltip="Rotate left"
            />
            <IconButton
              icon={TbRotateClockwise}
              onClick={handleRotateRight}
              className="hidden sm:flex"
              tooltip="Rotate right"
            />
            <IconButton
              icon={IoSearch}
              onClick={() => setShowSearch(!showSearch)}
              active={showSearch}
              tooltip="Search"
            />
          </div>
        </div>
      )}

      {isDocumentLoaded && !hasError && showSearch && (
        <div className="border-b bg-gray-50 px-4 py-3 shadow-sm dark:border-gray-600 dark:bg-gray-700">
          <div className="flex items-center gap-2">
            <input
              type="text"
              placeholder="Search in PDF..."
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              className="flex-1 rounded-md border-0 bg-white px-3 py-2 text-sm text-gray-900 shadow-sm ring-1 ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-100 dark:ring-gray-500 dark:placeholder:text-gray-400 dark:focus:ring-blue-400"
            />
            {/* <IconButton
              icon={IoSearch}
              onClick={handleSearch}
              tooltip="Search"
              disabled={isSearching}
            /> */}
            <IconButton
              icon={IoClose}
              onClick={clearSearch}
              tooltip="Close search"
            />
          </div>
        </div>
      )}

      <div
        className="flex-1 overflow-auto bg-gray-100 dark:bg-gray-800"
        ref={containerRef}
      >
        <Document
          key={url}
          file={url}
          onLoadSuccess={onDocumentLoadSuccess}
          onLoadError={onDocumentLoadError}
          error={<DocumentError />}
          loading={<LoadingDocument />}
          options={options}
          className="h-full w-full"
        >
          <div className="flex flex-col items-center gap-4">
            {Array.from(new Array(numPages), (_, index) => (
              <div
                key={`page_${index + 1}`}
                ref={(el) => {
                  pageRefs.current[index] = el;
                }}
                className="w-full"
              >
                <Page
                  pageNumber={index + 1}
                  loading={<LoadingDocument />}
                  className="mx-auto flex justify-center shadow-2xl"
                  width={Math.min((width ?? 800) * scale, 1200)}
                  rotate={rotation}
                  renderTextLayer={true}
                  renderAnnotationLayer={true}
                  customTextRenderer={textRenderer}
                />
              </div>
            ))}
          </div>
        </Document>
      </div>
    </div>
  );
}
