import { AlertTriangle, Info, XCircle } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Modal } from "@/components/ui/modal";
import { cn } from "@/lib/utils";

type ConfirmationVariant = "danger" | "warning" | "info";

type Props = {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  message: string;
  confirmLabel?: string;
  cancelLabel?: string;
  variant?: ConfirmationVariant;
  isLoading?: boolean;
  itemName?: string;
  warningMessage?: string;
  description?: string;
  showUndoWarning?: boolean;
};

const variantConfig = {
  danger: {
    icon: <XCircle size={32} className="text-red-500" />,
    bgClass: "bg-red-100 dark:bg-red-950/20",
  },
  warning: {
    icon: <AlertTriangle size={32} className="text-yellow-500" />,
    bgClass: "bg-yellow-100 dark:bg-yellow-950/20",
  },
  info: {
    icon: <Info size={32} className="text-blue-500" />,
    bgClass: "bg-blue-100 dark:bg-blue-950/20",
  },
};

const DEFAULT_CONFIRM_LABEL = {
  danger: "Delete",
  warning: "Continue",
  info: "OK",
};

export const ConfirmationModal = ({
  isOpen,
  onClose,
  onConfirm,
  message,
  variant = "warning",
  confirmLabel,
  cancelLabel = "Cancel",
  isLoading,
  itemName,
  description,
  warningMessage = "This action cannot be undone.",
  showUndoWarning = variant === "danger" || !!warningMessage,
}: Props) => {
  const buttonLabel = confirmLabel || DEFAULT_CONFIRM_LABEL[variant];

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Body>
        <div className="flex flex-col items-center justify-center gap-4">
          <div
            className={cn(
              "grid h-16 w-16 place-content-center rounded-full",
              variantConfig[variant].bgClass,
            )}
          >
            {variantConfig[variant].icon}
          </div>

          <div className="text-center">
            <p className="text-lg font-semibold text-gray-900 dark:text-white">
              {message}
            </p>

            {itemName && (
              <p className="mt-2 text-base text-gray-700 dark:text-gray-300">
                <span className="font-medium">"{itemName}"</span>
              </p>
            )}

            {description && (
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                {description}
              </p>
            )}

            {showUndoWarning && (
              <p className="mt-4 text-sm italic text-gray-500 dark:text-gray-500">
                {warningMessage}
              </p>
            )}
          </div>
        </div>
      </Modal.Body>

      <Modal.Footer className="flex justify-end gap-2.5 py-2.5">
        <Button onClick={onClose} disabled={isLoading}>
          {cancelLabel}
        </Button>
        <Button
          onClick={onConfirm}
          variant={variant}
          isLoading={isLoading}
          disabled={isLoading}
        >
          {buttonLabel}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export type { ConfirmationVariant };
