"use client";

import { Card } from "flowbite-react";
import Link from "next/link";
import { BiError } from "react-icons/bi";
import { FiDatabase, FiPackage } from "react-icons/fi";

import { Skeleton } from "@/components/ui/skeleton";

import { useHmacVerificationFailures } from "./hooks/use-system-health-queries";

const FAILURE_CATEGORIES = [
  {
    key: "sourceDocumentFailures",
    label: "Source Document Failures",
    icon: (
      <i className="rounded-lg bg-orange-100 p-2 dark:bg-orange-900/20">
        <FiDatabase className="h-4 w-4 text-orange-600 dark:text-orange-400" />
      </i>
    ),
  },
  {
    key: "artifactVersionFailures",
    label: "Artifact Version Failures",
    icon: (
      <i className="rounded-lg bg-red-100 p-2 dark:bg-red-900/20">
        <FiPackage className="h-4 w-4 text-red-600 dark:text-red-400" />
      </i>
    ),
  },
] as const;

export const HmacVerificationFailures = () => {
  const { data, isPending } = useHmacVerificationFailures();

  if (isPending) {
    return <HmacVerificationFailuresSkeleton />;
  }

  return (
    <>
      <Card className="h-full [&>div]:justify-start">
        <div className="mb-6 flex items-center justify-between">
          <Link
            href="/dashboards/system-health?tab=hmac-failures"
            className="hover:text-primary-500 dark:hover:text-primary-500 text-lg font-semibold text-gray-900 hover:underline dark:text-white"
          >
            HMAC Verification Failures
          </Link>
          <BiError className="h-6 w-6 text-red-500" />
        </div>

        <div className="mb-6 flex items-center justify-between rounded-lg border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-900/10">
          <p className="text-sm font-medium text-red-800 dark:text-red-200">
            Total Verification Failures
          </p>
          <span className="text-3xl font-bold text-red-600 dark:text-red-400">
            {data?.total ?? 0}
          </span>
        </div>

        <div className="space-y-3">
          <h4 className="mb-3 text-sm font-medium text-gray-700 dark:text-gray-300">
            Failure Breakdown by Category
          </h4>

          <div className="grid grid-cols-1 gap-3">
            {FAILURE_CATEGORIES.map((category) => {
              const count = data?.[category.key] ?? 0;

              return (
                <Link
                  href={`/dashboards/system-health?tab=hmac-failures`}
                  key={category.key}
                  className="flex items-center justify-between gap-2 rounded-lg border border-gray-200 bg-gray-50 p-3 transition-colors hover:bg-gray-100 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700"
                >
                  <p className="flex min-w-0 flex-1 items-center gap-3">
                    {category.icon}
                    <span className="min-w-0 flex-1 truncate text-sm font-medium text-gray-900 dark:text-white">
                      {category.label}
                    </span>
                  </p>
                  <span className="flex-shrink-0 text-lg font-semibold text-orange-600 dark:text-orange-400">
                    {count}
                  </span>
                </Link>
              );
            })}
          </div>
        </div>
      </Card>
    </>
  );
};

export const HmacVerificationFailuresSkeleton = () => {
  return (
    <Card>
      <div className="mb-6 flex items-center justify-between">
        <Skeleton className="h-6 w-48" />
        <Skeleton className="h-6 w-6 rounded-full" />
      </div>

      <div className="mb-6 rounded-lg border border-orange-200 bg-orange-50 p-4 dark:border-orange-800 dark:bg-orange-900/10">
        <div className="flex items-center justify-between">
          <Skeleton className="h-4 w-32" />
          <Skeleton className="h-8 w-12" />
        </div>
      </div>

      <div className="space-y-3">
        <Skeleton className="h-4 w-40" />

        <div className="grid grid-cols-1 gap-3">
          {Array.from({ length: 2 }).map((_, index) => (
            <div
              key={index}
              className="flex items-center justify-between gap-2 rounded-lg border border-gray-200 bg-gray-50 p-3 dark:border-gray-700 dark:bg-gray-800"
            >
              <div className="flex min-w-0 flex-1 items-center gap-3">
                <Skeleton className="h-8 w-8 rounded-lg" />
                <Skeleton className="h-4 w-full" />
              </div>
              <Skeleton className="h-6 w-8" />
            </div>
          ))}
        </div>
      </div>
    </Card>
  );
};
