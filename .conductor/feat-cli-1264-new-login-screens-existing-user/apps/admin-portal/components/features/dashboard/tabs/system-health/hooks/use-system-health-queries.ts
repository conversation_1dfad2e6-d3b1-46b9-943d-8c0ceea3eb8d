import { useQuery } from "@tanstack/react-query";
import { parseAsArrayOf, parseAsString, useQueryState } from "nuqs";

import { usePagination } from "@/hooks/use-pagination";
import api from "@/lib/apis";
import type { ProcessingErrorSource } from "@/lib/apis/system-health";

const useSystemHealthKeys = {
  all: () => ["system-health"] as const,
  processingErrors: () =>
    [...useSystemHealthKeys.all(), "processing-errors"] as const,
  processingErrorsList: (params?: {
    page?: number;
    take?: number;
    sources?: ProcessingErrorSource[];
  }) =>
    [...useSystemHealthKeys.all(), "processing-errors-list", params] as const,
  hmacFailures: () => [...useSystemHealthKeys.all(), "hmac-failures"] as const,
  hmacFailuresList: (params?: { page?: number; take?: number }) =>
    [...useSystemHealthKeys.all(), "hmac-failures-list", params] as const,
  offlineScanners: (threshold: number) =>
    [...useSystemHealthKeys.all(), "offline-scanners", threshold] as const,
  databaseGrowth: (params?: {
    fromDate?: string;
    toDate?: string;
    unit?: "day" | "week";
    combine?: boolean;
  }) => [...useSystemHealthKeys.all(), "database-growth", params] as const,
};

export const useSystemProcessingErrors = () => {
  return useQuery({
    queryKey: useSystemHealthKeys.processingErrors(),
    queryFn: () => api.systemHealth.getProcessingErrors(),
  });
};

export const useHmacVerificationFailures = () => {
  return useQuery({
    queryKey: useSystemHealthKeys.hmacFailures(),
    queryFn: () => api.systemHealth.getHmacFailures(),
  });
};

export const useOfflineScanners = (threshold: number) => {
  return useQuery({
    queryKey: [...useSystemHealthKeys.offlineScanners(threshold)],
    queryFn: () => api.systemHealth.getOfflineScanners(threshold),
    placeholderData: (prev) => prev,
  });
};

export const useDatabaseGrowth = (params?: {
  fromDate?: string;
  toDate?: string;
  unit?: "day" | "week";
  combine?: boolean;
}) => {
  return useQuery({
    queryKey: useSystemHealthKeys.databaseGrowth(params),
    queryFn: () => api.systemHealth.getDatabaseGrowth(params),
    placeholderData: (prev) => prev,
  });
};

export const useProcessingErrorsList = () => {
  const { page, take } = usePagination();
  const [selectedSources] = useQueryState(
    "sources",
    parseAsArrayOf(parseAsString).withDefault([]),
  );

  const sources =
    selectedSources.length > 0
      ? (selectedSources as ProcessingErrorSource[])
      : undefined;
  return useQuery({
    queryKey: useSystemHealthKeys.processingErrorsList({
      page,
      take,
      sources: sources,
    }),
    queryFn: () =>
      api.systemHealth.getProcessingErrorsList({
        page,
        take,
        sources: sources,
      }),
    placeholderData: (prev) => prev,
  });
};

export const useHmacFailuresList = () => {
  const { page, take } = usePagination();

  return useQuery({
    queryKey: useSystemHealthKeys.hmacFailuresList({
      page,
      take,
    }),
    queryFn: () =>
      api.systemHealth.getHmacFailuresList({
        page,
        take,
      }),
    placeholderData: (prev) => prev,
  });
};
