import { Card } from "flowbite-react";
import React from "react";

import { LineChart } from "@/components/ui/charts/line";
import { Skeleton } from "@/components/ui/skeleton";
import { formatDate } from "@/lib/utils";

import { useAiSuccessRateTrend } from "./hooks/use-overview-categorization";

const configs = [
  {
    dataKey: "aiSuccessRate",
    name: "Success Rate",
  },
] as const;

export const AiSuccessRateTrend = () => {
  const { data, isPending } = useAiSuccessRateTrend();
  const formatData = data?.map((item) => ({
    date: formatDate(item.date, "LLL dd"),
    aiSuccessRate: item.aiSuccessRate,
  }));

  return (
    <div>
      <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
        AI Success Rate Trend
      </h3>
      {isPending ? (
        <AiSuccessRateTrendSkeleton />
      ) : (
        <Card>
          {formatData?.length ? (
            <LineChart data={formatData} configs={configs} isPercentage />
          ) : (
            <div className="flex h-80 items-center justify-center">
              <p className="text-xl text-gray-500 dark:text-gray-400">
                No data available
              </p>
            </div>
          )}
        </Card>
      )}
    </div>
  );
};

const AiSuccessRateTrendSkeleton = () => {
  return (
    <div className="flex h-80 items-center justify-center">
      <Skeleton className="h-full w-full" />
    </div>
  );
};
