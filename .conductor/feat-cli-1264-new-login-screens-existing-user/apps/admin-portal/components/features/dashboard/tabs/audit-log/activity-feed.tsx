import { Card } from "flowbite-react";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";

import { AuditLogFilter } from "./audit-log-filter";
import { auditLogColumns } from "./columns";
import { useActivityFeed } from "./hooks/use-audit-log-queries";

export const ActivityFeed = () => {
  const { data, isPending, isPlaceholderData } = useActivityFeed();

  return (
    <Card className="[&>div]:p-0">
      <div className="flex items-center justify-between p-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Activity Feed
        </h3>
        <AuditLogFilter />
      </div>

      {isPending ? (
        <TableLoading columns={auditLogColumns} />
      ) : (
        <LoadingWrapper isLoading={isPlaceholderData}>
          <Table columns={auditLogColumns} data={data?.results ?? []} />
          {data?.metadata && <TableDataPagination metadata={data.metadata} />}
        </LoadingWrapper>
      )}
    </Card>
  );
};
