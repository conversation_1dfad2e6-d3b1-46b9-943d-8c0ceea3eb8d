"use client";

import { Card } from "flowbite-react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON><PERSON>, FiFileText } from "react-icons/fi";

import { Skeleton } from "@/components/ui/skeleton";

import { useHighLevelSummaryFilters } from "../hooks/use-document-analytics-filters";
import {
  useDocumentsPendingReview,
  useTotalDocuments,
} from "../hooks/use-document-analytics-queries";

export const KPICards = () => {
  const router = useRouter();
  const { data: totalDocuments, isPending: isLoadingTotal } =
    useTotalDocuments();
  const { data: pendingReview, isPending: isLoadingPending } =
    useDocumentsPendingReview();
  const { filters } = useHighLevelSummaryFilters();

  const handleCardClick = (cardType: "total" | "pending") => {
    const searchParams = new URLSearchParams();

    // Set the tab based on card type
    searchParams.set("tab", cardType === "total" ? "all" : "pending");

    // Pass current filters to the detail page
    if (filters.sponsorId) searchParams.set("sponsorId", filters.sponsorId);
    if (filters.studyId) searchParams.set("studyId", filters.studyId);
    if (filters.fromDate) searchParams.set("fromDate", filters.fromDate);
    if (filters.toDate) searchParams.set("toDate", filters.toDate);

    router.push(`/dashboards/document-analytics?${searchParams.toString()}`);
  };

  const CARDS = [
    {
      title: "Total Documents",
      count: totalDocuments?.count ?? 0,
      type: "total" as const,
      icon: (
        <div className="flex size-14 flex-shrink-0 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/20">
          <FiFileText className="size-7 text-blue-600 dark:text-blue-400" />
        </div>
      ),
      isLoading: isLoadingTotal,
    },
    {
      title: "Pending Review",
      count: pendingReview?.count ?? 0,
      type: "pending" as const,
      icon: (
        <div className="flex size-14 flex-shrink-0 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/20">
          <FiClock className="size-7 text-orange-600 dark:text-orange-400" />
        </div>
      ),
      isLoading: isLoadingPending,
    },
  ];

  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
      {CARDS.map(({ title, count, icon, isLoading, type }, index) =>
        isLoading ? (
          <Skeleton key={index} className="h-[104px] w-full" />
        ) : (
          <Card
            key={title}
            className="cursor-pointer transition-all duration-200 hover:bg-gray-50 dark:hover:bg-gray-700"
            onClick={() => handleCardClick(type)}
          >
            <div className="flex items-center gap-4">
              {icon}
              <div className=" flex-1">
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {count}
                </p>
                <p className="mt-0.5 text-sm font-medium text-gray-500 dark:text-gray-400">
                  {title}
                </p>
              </div>
            </div>
          </Card>
        ),
      )}
    </div>
  );
};
