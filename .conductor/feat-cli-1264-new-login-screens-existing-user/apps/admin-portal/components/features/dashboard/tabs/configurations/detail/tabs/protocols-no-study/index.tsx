"use client";

import { Card } from "flowbite-react";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";

import { protocolsNoStudyColumns } from "./columns";
import { useProtocolsNoStudy } from "./use-protocols-no-study-queries";

export const ProtocolsNoStudyTab = () => {
  const {
    data: protocolsData,
    isPending: isLoadingProtocols,
    isPlaceholderData: isProtocolsPlaceholderData,
  } = useProtocolsNoStudy();

  return (
    <div>
      <Card className="[&>div]:p-0">
        <h4 className="p-4 text-base font-medium text-gray-900 dark:text-white">
          Protocols without Study Assignment
        </h4>

        {isLoadingProtocols ? (
          <TableLoading columns={protocolsNoStudyColumns} />
        ) : (
          <LoadingWrapper isLoading={isProtocolsPlaceholderData}>
            <Table
              columns={protocolsNoStudyColumns}
              data={protocolsData?.results || []}
            />

            {protocolsData?.metadata && (
              <TableDataPagination metadata={protocolsData.metadata} />
            )}
          </LoadingWrapper>
        )}
      </Card>
    </div>
  );
};
