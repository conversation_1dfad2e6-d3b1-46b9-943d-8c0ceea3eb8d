"use client";

import { InactiveSitesTable } from "./components/inactive-site-table";
import { SitePerformanceLeaderboardTable } from "./components/site-activity-leaderboard";
import { SitesDocExchangeTable } from "./components/sites-doc-exchange";

export const SitePerformanceLeaderboard = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          Site Performance & Leaderboard
        </h2>
      </div>

      <div className="space-y-6">
        <SitePerformanceLeaderboardTable />
        <InactiveSitesTable />
        <SitesDocExchangeTable />
      </div>
    </div>
  );
};
