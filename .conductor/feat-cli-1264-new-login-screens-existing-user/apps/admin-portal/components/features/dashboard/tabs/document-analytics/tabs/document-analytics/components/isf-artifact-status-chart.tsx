"use client";

import { Card, useThemeMode } from "flowbite-react";
import {
  Bar,
  BarChart,
  Cell,
  ResponsiveContainer,
  Text,
  Tooltip,
  TooltipContentProps,
  XAxis,
  YAxis,
} from "recharts";
import {
  NameType,
  ValueType,
} from "recharts/types/component/DefaultTooltipContent";
import { TickItemTextProps } from "recharts/types/polar/PolarAngleAxis";

import { Skeleton } from "@/components/ui/skeleton";

import { useISFArtifactStatusBreakdown } from "../hooks/use-document-analytics-queries";

const STATUS_COLORS = {
  rejected: "#ef4444",
  finalized: "#a855f7",
  attention: "#f59e0b",
  reviewed: "#3b82f6",
  submitted: "#10b981",
  draft: "#0d9488",
  placeholder: "#4b5563",
} as const;

const CustomTooltip = ({
  active,
  payload,
  label,
}: TooltipContentProps<ValueType, NameType>) => {
  if (active && payload && payload.length) {
    const data = payload[0];

    return (
      <div className=" rounded-lg border bg-white p-3 shadow-md dark:border-gray-600 dark:bg-gray-800">
        <div className="flex items-center gap-2">
          <div
            className="h-3 w-3 rounded-full"
            style={{ backgroundColor: data.payload.fill }}
          />
          <span className="font-medium capitalize text-black dark:text-white">
            {label}
          </span>
        </div>
        <div className="mt-1 text-sm dark:text-white">
          Count:{" "}
          <span className="font-medium">{data.value.toLocaleString()}</span>
        </div>
      </div>
    );
  }
  return null;
};

const YAxisLeftTick = ({ y, payload: { value } }: TickItemTextProps) => {
  return (
    <Text
      x={0}
      y={y}
      style={{
        fontSize: 12,
        textTransform: "capitalize",
      }}
      textAnchor="start"
      verticalAnchor="middle"
      width={70}
      className="dark:fill-white"
    >
      {value}
    </Text>
  );
};

export const ISFArtifactStatusChart = () => {
  // const [, setStatus] = useQueryState("status", parseAsString);
  const { mode } = useThemeMode();
  const { data, isPending } = useISFArtifactStatusBreakdown();

  const chartData =
    data
      ?.sort((a, b) => b.count - a.count)
      .map((item) => ({
        statusName: item.statusName,
        count: item.count,
        fill: STATUS_COLORS[item.statusName] ?? "#4b5563",
      })) || [];

  return (
    <Card>
      <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
        ISF Artifact Status
      </h3>
      {isPending ? (
        <Skeleton className="h-80 w-full" />
      ) : (
        <div className="h-80">
          {chartData.length === 0 ? (
            <div className="grid h-full place-content-center text-center">
              <p className="mt-2 text-sm text-gray-500">
                No ISF artifact status data available
              </p>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={chartData} layout="vertical">
                <XAxis
                  type="number"
                  dataKey="count"
                  tick={{ fontSize: 12 }}
                  stroke={mode === "dark" ? "#fff" : "#000"}
                  tickFormatter={(value) => value.toLocaleString()}
                />
                <YAxis
                  type="category"
                  dataKey="statusName"
                  tick={YAxisLeftTick}
                  stroke={mode === "dark" ? "#fff" : "#000"}
                  width={70}
                />
                <Tooltip cursor={false} content={CustomTooltip} />
                <Bar
                  // onClick={(e) => {
                  //   if ("statusName" in e) {
                  //     setStatus((e.statusName as string) || null);
                  //   }
                  // }}
                  className="cursor-pointer"
                  dataKey="count"
                  barSize={30}
                >
                  {chartData.map((entry, index) => (
                    <Cell key={`bar-${index}`} fill={entry.fill} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          )}
        </div>
      )}
    </Card>
  );
};
