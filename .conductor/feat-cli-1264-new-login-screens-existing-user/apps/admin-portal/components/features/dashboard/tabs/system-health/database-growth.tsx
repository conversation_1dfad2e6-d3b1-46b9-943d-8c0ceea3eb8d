"use client";

import { Card } from "flowbite-react";
import { useState } from "react";
import { DateRange } from "react-day-picker";
import { FiDatabase } from "react-icons/fi";

import { LineChart } from "@/components/ui/charts";
import { Skeleton } from "@/components/ui/skeleton";
import {
  DatabaseGrowthResponse,
  DatabaseTableType,
} from "@/lib/apis/system-health";
import { formatDate } from "@/lib/utils";

import {
  calculateDateRange,
  DateOptions,
  DateRangeFilter,
} from "../components";
import { PeriodToggle } from "../components/period-toggle";
import { useDatabaseGrowth } from "./hooks/use-system-health-queries";

const transformDataForChart = (apiData?: DatabaseGrowthResponse) => {
  if (!apiData?.tables?.length) return [];

  const allDates = new Set<string>();
  apiData.tables.forEach((table) => {
    table.data.forEach((point) => {
      allDates.add(point.date);
    });
  });

  const sortedDates = Array.from(allDates).sort();

  return sortedDates.map((date) => {
    const dataPoint: Partial<
      Record<DatabaseTableType | "date", number | string>
    > = {
      date: formatDate(date, "LLL dd"),
    };

    apiData.tables.forEach((table) => {
      const tableData = table.data.find((point) => point.date === date);
      dataPoint[table.table] = tableData?.count || 0;
    });

    return dataPoint;
  });
};
const chartConfigs = [
  {
    dataKey: "activity_log",
    name: "Activity Log",
  },
  {
    dataKey: "artifact_files",
    name: "Artifact Files",
  },
  {
    dataKey: "patients",
    name: "Patients",
  },
  {
    dataKey: "source_documents",
    name: "Source Documents",
  },
] as const;

export const DatabaseGrowth = () => {
  const [unit, setUnit] = useState<"daily" | "weekly">("daily");
  const [selectedDateFilter, setSelectedDateFilter] = useState<
    DateOptions | ""
  >("last_month");
  const [selectedRange, setSelectedRange] = useState<DateRange>({
    from: undefined,
    to: undefined,
  });

  const handleFilterChange = (
    filter: DateOptions | "",
    customRange?: DateRange,
  ) => {
    setSelectedDateFilter(filter);
    if (customRange) {
      setSelectedRange(customRange);
    }
  };

  const dateRange = calculateDateRange(selectedDateFilter, selectedRange);

  const { data, isPending } = useDatabaseGrowth({
    fromDate: dateRange?.fromDate,
    toDate: dateRange?.toDate,
    unit: unit === "daily" ? "day" : "week",
  });

  if (isPending) {
    return <DatabaseGrowthSkeleton />;
  }

  const chartData = transformDataForChart(data);

  return (
    <Card>
      <div className="mb-6 flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Database Growth Over Time
        </h3>
        <FiDatabase className="h-6 w-6 text-blue-500" />
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-end gap-4">
          <PeriodToggle currentPeriod={unit} setCurrentPeriod={setUnit} />
          <DateRangeFilter
            selectedFilter={selectedDateFilter}
            onFilterChange={handleFilterChange}
            selectedRange={selectedRange}
          />
        </div>

        <div className="h-80">
          {!chartData?.length ? (
            <div className=" grid h-full place-content-center gap-2 text-center text-gray-500 dark:text-gray-400">
              <FiDatabase className="mx-auto h-12 w-12 text-gray-300 dark:text-gray-600" />
              <p className="mt-2 text-sm">No database growth data available</p>
            </div>
          ) : (
            <LineChart data={chartData} configs={chartConfigs} />
          )}
        </div>
      </div>
    </Card>
  );
};

export const DatabaseGrowthSkeleton = () => {
  return (
    <Card>
      <div className="mb-6 flex items-center justify-between">
        <Skeleton className="h-6 w-48" />
        <Skeleton className="h-6 w-6 rounded-full" />
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-end gap-4">
          <div className="flex items-center gap-2">
            <Skeleton className="h-4 w-8" />
            <Skeleton className="h-6 w-10 rounded-full" />
            <Skeleton className="h-4 w-12" />
          </div>
          <Skeleton className="h-8 w-48" />
        </div>

        <Skeleton className="h-80 w-full rounded-lg" />
      </div>
    </Card>
  );
};
