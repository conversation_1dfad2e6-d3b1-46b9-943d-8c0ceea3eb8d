"use client";

import { Card } from "flowbite-react";
import { useState } from "react";

import {
  StackedAreaChart,
  StackedAreaChartPoint,
} from "@/components/ui/charts/stacked-area";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Method,
  MethodTrendsResponse,
} from "@/lib/apis/categorization-analytics";
import { formatDate } from "@/lib/utils";

import { PeriodToggle } from "../../../components/period-toggle";
import { useMethodTrends } from "./hooks/use-overview-categorization";

const transformData = (
  trendsData?: MethodTrendsResponse,
): StackedAreaChartPoint<Method>[] => {
  if (!trendsData) return [];

  const dateMap = new Map();

  trendsData.data.forEach((item) => {
    const date = formatDate(item.date, "LLL dd");

    if (!dateMap.has(date)) {
      dateMap.set(date, {
        date,
        auto: 0,
        manual: 0,
        auto_to_manual: 0,
        ai_failed_manual: 0,
        [item.method]: item.count,
      });
    }

    dateMap.set(date, {
      ...dateMap.get(date),
      [item.method]: item.count,
    });
  });

  return Array.from(dateMap.values()).sort(
    (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime(),
  );
};

const chartConfigs = [
  {
    dataKey: "auto",
    name: "Auto",
  },
  {
    dataKey: "manual",
    name: "Manual",
  },
  {
    dataKey: "auto_to_manual",
    name: "Auto-to-Manual",
  },
  {
    dataKey: "ai_failed_manual",
    name: "AI Failed Manual",
  },
] as const;

export const CategorizationMethodTrends = () => {
  const [period, setPeriod] = useState<"daily" | "weekly">("daily");
  const { data: trendsData, isPending } = useMethodTrends({
    granularity: period === "daily" ? "day" : "week",
  });
  const chartData = transformData(trendsData);

  if (isPending) {
    return (
      <Card>
        <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
          Categorization Method Trends
        </h3>
        <Skeleton className="h-80 w-full" />
      </Card>
    );
  }

  return (
    <div>
      <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
        Categorization Method Trends
      </h3>
      <Card>
        <div className="flex justify-end">
          <PeriodToggle currentPeriod={period} setCurrentPeriod={setPeriod} />
        </div>
        {!trendsData?.data?.length ? (
          <div className="flex h-80 items-center justify-center">
            <p className="text-xl text-gray-500 dark:text-gray-400">
              No data available
            </p>
          </div>
        ) : (
          <StackedAreaChart data={chartData} configs={chartConfigs} />
        )}
      </Card>
    </div>
  );
};
