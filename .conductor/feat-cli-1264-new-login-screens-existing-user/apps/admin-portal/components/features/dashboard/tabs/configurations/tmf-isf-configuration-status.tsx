"use client";

import { Card } from "flowbite-react";
import { useState } from "react";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";

import { noCategoryArtifactsColumns, noTMFISFStudiesColumns } from "./columns";
import {
  useNoCategoryArtifacts,
  useNoTMFISFStudies,
} from "./hooks/use-data-integrity-queries";

export const TMFISFConfigurationStatus = () => {
  const [currentStudiesPage, setCurrentStudiesPage] = useState(1);
  const [currentArtifactsPage, setCurrentArtifactsPage] = useState(1);

  const {
    data: noTMFISFStudiesData,
    isPending: isLoadingNoTMFISFStudies,
    isPlaceholderData: isNoTMFISFStudiesPlaceholderData,
  } = useNoTMFISFStudies(currentStudiesPage);

  const {
    data: noCategoryArtifactsData,
    isPending: isLoadingNoCategoryArtifacts,
    isPlaceholderData: isNoCategoryArtifactsPlaceholderData,
  } = useNoCategoryArtifacts(currentArtifactsPage);

  return (
    <div>
      <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
        TMF/ISF Configuration Status
      </h3>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <Card className="h-fit [&>div]:p-0">
          <h4 className="p-4 text-base font-medium text-gray-900 dark:text-white">
            Studies without TMF/ISF Structure
          </h4>

          {isLoadingNoTMFISFStudies ? (
            <TableLoading columns={noTMFISFStudiesColumns} />
          ) : (
            <LoadingWrapper isLoading={isNoTMFISFStudiesPlaceholderData}>
              <Table
                columns={noTMFISFStudiesColumns}
                data={noTMFISFStudiesData?.results || []}
              />

              {noTMFISFStudiesData?.metadata && (
                <TableDataPagination
                  metadata={noTMFISFStudiesData.metadata}
                  page={currentStudiesPage}
                  setPage={setCurrentStudiesPage}
                  isUseExternalState
                />
              )}
            </LoadingWrapper>
          )}
        </Card>

        <Card className="h-fit [&>div]:p-0">
          <h4 className="p-4 text-base font-medium text-gray-900 dark:text-white">
            Artifacts without a Category
          </h4>

          {isLoadingNoCategoryArtifacts ? (
            <TableLoading columns={noCategoryArtifactsColumns} />
          ) : (
            <LoadingWrapper isLoading={isNoCategoryArtifactsPlaceholderData}>
              <Table
                columns={noCategoryArtifactsColumns}
                data={noCategoryArtifactsData?.results || []}
              />

              {noCategoryArtifactsData?.metadata && (
                <TableDataPagination
                  metadata={noCategoryArtifactsData.metadata}
                  page={currentArtifactsPage}
                  setPage={setCurrentArtifactsPage}
                  isUseExternalState
                />
              )}
            </LoadingWrapper>
          )}
        </Card>
      </div>
    </div>
  );
};
