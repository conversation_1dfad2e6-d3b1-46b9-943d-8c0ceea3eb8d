import { useQuery, useQueryClient } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import api from "@/lib/apis";
import { Prompt } from "@/lib/apis/app-prompts";
import {
  ArtifactCategory,
  CategoryVersion,
} from "@/lib/apis/artifact-categories";
import type { EntityType, MethodType } from "@/lib/apis/config-sync/types";
import { ISFRefModel } from "@/lib/apis/isf-ref-models";
import { PromptVariable } from "@/lib/apis/prompt-variables";
import { TMFRefModel } from "@/lib/apis/tmf-ref-models";
import { ListBaseResponse } from "@/lib/apis/types";

export const configSyncKeys = {
  all: () => ["config-sync"] as const,
  status: () => [...configSyncKeys.all(), "status"] as const,
  updates: () => [...configSyncKeys.all(), "updates"] as const,
  allEntityList: () => [...configSyncKeys.all(), "entity-list"] as const,
  byEntity: (entityType: EntityType, method: MethodType, page = 1) =>
    [...configSyncKeys.allEntityList(), entityType, method, page] as const,
};

export const useConfigSyncStatus = () => {
  return useQuery({
    queryKey: configSyncKeys.status(),
    queryFn: () => api.configSync.getStatus(),
  });
};

export const useConfigSyncUpdates = () => {
  return useQuery({
    queryKey: configSyncKeys.updates(),
    queryFn: () => api.configSync.checkForUpdates(),
    enabled: false,
  });
};

export const useConfigByEntity = <T = any>(
  entityType: EntityType,
  method: MethodType,
  page = 1,
) => {
  const { take } = usePagination();
  return useQuery({
    queryKey: configSyncKeys.byEntity(entityType, method, page),
    queryFn: () =>
      api.configSync.fetchByEntity<T>({
        entityType,
        method,
        page,
        take: take || 50,
      }),
    placeholderData: (prev) => prev,
  });
};

export const useArtifactCategoriesData = (method: MethodType, page = 1) =>
  useConfigByEntity<ArtifactCategory>("artifactCategories", method, page);

export const usePromptTemplatesData = (method: MethodType, page = 1) =>
  useConfigByEntity<Prompt>("aiPromptTemplate", method, page);

export const usePromptVariablesData = (method: MethodType, page = 1) =>
  useConfigByEntity<PromptVariable>("aiPromptVariable", method, page);

export const useCategoryVersionsData = (method: MethodType, page = 1) =>
  useConfigByEntity<CategoryVersion>("artifactCategoryVersions", method, page);

export const useTMFReferenceModelsData = (method: MethodType, page = 1) =>
  useConfigByEntity<TMFRefModel>("artifactTmfRefModel", method, page);

export const useISFReferenceModelsData = (method: MethodType, page = 1) =>
  useConfigByEntity<ISFRefModel>("artifactIsfRefModel", method, page);

const ENTITIES = [
  { type: "artifactCategories", name: "Artifact Categories" },
  { type: "artifactCategoryVersions", name: "Category Versions" },
  { type: "artifactTmfRefModel", name: "TMF Reference Models" },
  { type: "artifactIsfRefModel", name: "ISF Reference Models" },
  { type: "aiPromptTemplate", name: "Prompt Templates" },
  { type: "aiPromptVariable", name: "Prompt Variables" },
] as const;

export const useConfigChangesSummary = () => {
  const queryClient = useQueryClient();

  const changeSummary = ENTITIES.map((entity) => {
    const updateQueryKey = configSyncKeys.byEntity(entity.type, "update", 1);
    const deleteQueryKey = configSyncKeys.byEntity(entity.type, "deletion", 1);

    const updateData = queryClient.getQueryData(
      updateQueryKey,
    ) as ListBaseResponse<any>;
    const deleteData = queryClient.getQueryData(
      deleteQueryKey,
    ) as ListBaseResponse<any>;

    const updates = updateData?.metadata?.totalCount || 0;
    const deletes = deleteData?.metadata?.totalCount || 0;

    return {
      entityType: entity.type,
      entityName: entity.name,
      updates,
      deletes,
      total: updates + deletes,
    };
  });

  const totalChanges = changeSummary.reduce((sum, item) => sum + item.total, 0);

  return {
    changeSummary: changeSummary.filter((item) => item.total > 0),
    totalChanges,
  };
};
