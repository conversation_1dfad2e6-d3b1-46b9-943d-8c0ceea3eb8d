"use client";

import { type ColumnDef } from "@tanstack/react-table";
import { useState } from "react";
import { HiArrowTrendingUp, HiTrash } from "react-icons/hi2";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { ActiveStatusBadge } from "@/components/ui/badges";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import { Prompt } from "@/lib/apis/app-prompts";
import type { MethodType } from "@/lib/apis/config-sync/types";
import { cn } from "@/lib/utils";

import { usePromptTemplatesData } from "../hooks/use-config-sync-queries";

const MODEL_PROVIDER_LABEL = {
  openai: "OpenAi",
  gemini: "Gemini",
};

const columns: ColumnDef<Prompt>[] = [
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    header: "Type",
    cell: ({ row }) => {
      return (
        <span className="capitalize">{row.original.key.replace("_", " ")}</span>
      );
    },
  },
  {
    header: "Model Provider",
    cell: ({ row }) => {
      return MODEL_PROVIDER_LABEL[row.original.modelProvider];
    },
  },
  {
    accessorKey: "model",
    header: "Model",
  },
  {
    accessorKey: "version",
    header: "Version",
  },
  {
    accessorKey: "description",
    header: "Description",
  },
  {
    accessorKey: "isActive",
    header: "Status",
    cell: ({ row }) => {
      return <ActiveStatusBadge isActive={row.original.isActive} />;
    },
  },
];

export const PromptTemplatesTab = () => {
  const [operationType, setOperationType] = useState<MethodType>("update");
  const [page, setPage] = useState(1);

  const updateData = usePromptTemplatesData(
    "update",
    operationType === "update" ? page : 1,
  );
  const deleteData = usePromptTemplatesData(
    "deletion",
    operationType === "deletion" ? page : 1,
  );

  const { data, isPending, isPlaceholderData } =
    operationType === "update" ? updateData : deleteData;

  const handleOperationChange = (newOperation: MethodType) => {
    setOperationType(newOperation);
    setPage(1);
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-4 px-4 ">
        <div
          onClick={() => handleOperationChange("update")}
          className={cn(
            "group cursor-pointer rounded-xl border-2 p-2 transition-all duration-300",
            operationType === "update"
              ? "border-blue-500 bg-blue-50 shadow-lg shadow-blue-500/10 dark:border-blue-400 dark:bg-blue-950/50"
              : "border-gray-200 bg-white hover:border-blue-300 hover:shadow-md dark:border-gray-700 dark:bg-gray-800 dark:hover:border-blue-600",
          )}
        >
          <div className="flex items-center gap-3">
            <div
              className={cn(
                "rounded-full p-2 transition-colors duration-200",
                operationType === "update"
                  ? "bg-blue-500 text-white"
                  : "bg-gray-100 text-gray-600 group-hover:bg-blue-100 group-hover:text-blue-600 dark:bg-gray-700 dark:text-gray-400",
              )}
            >
              <HiArrowTrendingUp className="h-4 w-4" />
            </div>
            <div>
              <div className="flex items-center gap-2">
                <h3
                  className={cn(
                    "font-semibold transition-colors duration-200",
                    operationType === "update"
                      ? "text-blue-900 dark:text-blue-100"
                      : "text-gray-900 group-hover:text-blue-900 dark:text-gray-100 dark:group-hover:text-blue-100",
                  )}
                >
                  Updates
                </h3>
                <span
                  className={cn(
                    "min-w-6 rounded-full px-2 py-0.5 text-xs font-medium",
                    operationType === "update"
                      ? "bg-blue-600 text-white"
                      : "bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300",
                  )}
                >
                  {updateData.data?.metadata?.totalCount || 0}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div
          onClick={() => handleOperationChange("deletion")}
          className={cn(
            "group cursor-pointer rounded-xl border-2 p-2 transition-all duration-300",
            operationType === "deletion"
              ? "border-red-500 bg-red-50 shadow-lg shadow-red-500/10 dark:border-red-400 dark:bg-red-950/50"
              : "border-gray-200 bg-white hover:border-red-300 hover:shadow-md dark:border-gray-700 dark:bg-gray-800 dark:hover:border-red-600",
          )}
        >
          <div className="flex items-center gap-3">
            <div
              className={cn(
                "rounded-full p-2 transition-colors duration-200",
                operationType === "deletion"
                  ? "bg-red-500 text-white"
                  : "bg-gray-100 text-gray-600 group-hover:bg-red-100 group-hover:text-red-600 dark:bg-gray-700 dark:text-gray-400",
              )}
            >
              <HiTrash className="h-4 w-4" />
            </div>
            <div>
              <div className="flex items-center gap-2">
                <h3
                  className={cn(
                    "font-semibold transition-colors duration-200",
                    operationType === "deletion"
                      ? "text-red-900 dark:text-red-100"
                      : "text-gray-900 group-hover:text-red-900 dark:text-gray-100 dark:group-hover:text-red-100",
                  )}
                >
                  Deletes
                </h3>
                <span
                  className={cn(
                    "min-w-6 rounded-full px-2 py-0.5 text-xs font-medium",
                    operationType === "deletion"
                      ? "bg-red-600 text-white"
                      : "bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300",
                  )}
                >
                  {deleteData.data?.metadata?.totalCount || 0}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {isPending ? (
        <TableLoading columns={columns} />
      ) : (
        <LoadingWrapper isLoading={isPlaceholderData}>
          <Table data={data?.results || []} columns={columns} />
          {data?.metadata && (
            <TableDataPagination
              isUseExternalState={true}
              metadata={data.metadata}
              page={page}
              setPage={setPage}
            />
          )}
        </LoadingWrapper>
      )}
    </div>
  );
};
