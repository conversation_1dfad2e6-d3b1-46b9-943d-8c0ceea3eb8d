"use client";

import { useQueryClient } from "@tanstack/react-query";
import { Card, Tooltip } from "flowbite-react";
import { parseAsString, useQueryState } from "nuqs";
import { useState } from "react";
import {
  FiAlertCircle,
  FiCheckCircle,
  FiClock,
  FiPlay,
  FiRefreshCw,
} from "react-icons/fi";
import { HiArrowTrendingUp, HiTrash } from "react-icons/hi2";

import { LoadingOverlay } from "@/components/shared/loading-overlay";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { UncontrolledSelect } from "@/components/ui/form/select/uncontrolled-select";
import { ConfirmModal } from "@/components/ui/modal/confirm-modal";
import { PageHeader } from "@/components/ui/page-header";
import { Skeleton } from "@/components/ui/skeleton";
import { TabsWrapper } from "@/components/ui/tabs-wrapper";
import { formatDate } from "@/lib/utils";

import { useRunConfigSync } from "./hooks/use-config-sync-mutations";
import {
  configSyncKeys,
  useConfigChangesSummary,
  useConfigSyncStatus,
} from "./hooks/use-config-sync-queries";
import {
  ArtifactCategoriesTab,
  CategoryVersionsTab,
  ISFReferenceModelsTab,
  PromptTemplatesTab,
  PromptVariablesTab,
  TMFReferenceModelsTab,
} from "./tabs";

const BREADCRUMB_ITEMS = [{ label: "Configuration" }];

const SYNC_DATA_TABS = [
  {
    key: "artifact-categories",
    title: "Artifact Categories",
    content: <ArtifactCategoriesTab />,
  },
  {
    key: "category-versions",
    title: "Category Versions",
    content: <CategoryVersionsTab />,
  },
  {
    key: "tmf-reference-models",
    title: "TMF Reference Models",
    content: <TMFReferenceModelsTab />,
  },
  {
    key: "isf-reference-models",
    title: "ISF Reference Models",
    content: <ISFReferenceModelsTab />,
  },
  {
    key: "prompt-templates",
    title: "Prompt Templates",
    content: <PromptTemplatesTab />,
  },
  {
    key: "prompt-variables",
    title: "Prompt Variables",
    content: <PromptVariablesTab />,
  },
];

export const ConfigurationContent = () => {
  const queryClient = useQueryClient();
  const [showUpdates, setShowUpdates] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [tab, setTab] = useQueryState(
    "tab",
    parseAsString.withDefault(SYNC_DATA_TABS[0].key),
  );

  const { data: syncStatus, isPending: isLoadingStatus } =
    useConfigSyncStatus();
  const { mutateAsync: runSync, isPending: isSyncing } = useRunConfigSync();
  const { changeSummary, totalChanges } = useConfigChangesSummary();

  const handleCheckForUpdates = async () => {
    setShowUpdates(true);

    if (showUpdates) {
      try {
        setIsScanning(true);
        await queryClient.invalidateQueries({
          queryKey: configSyncKeys.all(),
        });
      } finally {
        setIsScanning(false);
      }
    }
  };

  const handleShowSyncModal = () => {
    setShowConfirmModal(true);
  };

  const handleConfirmSync = async () => {
    await runSync();
    setShowUpdates(false);
    setShowConfirmModal(false);
    setTab(null);
  };

  return (
    <div className="space-y-6">
      <Breadcrumb items={BREADCRUMB_ITEMS} />
      <PageHeader>Configuration</PageHeader>

      <Card>
        <div className="mb-4 flex items-center justify-between">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
            Sync Status Overview
          </h3>
          <div className="flex items-center space-x-2">
            {!showUpdates ? (
              <div className="flex items-center space-x-2 text-amber-600 dark:text-amber-400">
                <FiAlertCircle className="h-5 w-5" />
                <span className="text-sm font-medium">Scan Required</span>
              </div>
            ) : !syncStatus?.hasChanged ? (
              <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
                <FiCheckCircle className="h-5 w-5" />
                <span className="text-sm font-medium">Nothing to Sync</span>
              </div>
            ) : (
              <div className="flex items-center space-x-2 text-green-600 dark:text-green-400">
                <FiCheckCircle className="h-5 w-5" />
                <span className="text-sm font-medium">Ready to Sync</span>
              </div>
            )}
          </div>
        </div>

        <div className="mb-4 rounded-lg border border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100 p-6 dark:border-blue-700/50 dark:from-blue-900/20 dark:to-blue-800/20">
          <div className="flex items-center space-x-4">
            <div className="rounded-lg bg-blue-100 p-3 dark:bg-blue-800/50">
              <FiClock className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <p className="text-base font-medium text-blue-900 dark:text-blue-100">
                Last Successful Sync
              </p>
              <div className="text-sm text-blue-700 dark:text-blue-300">
                {isLoadingStatus ? (
                  <Skeleton className="h-5 w-32" />
                ) : syncStatus?.syncTimestamp ? (
                  formatDate(
                    syncStatus.syncTimestamp,
                    "LLL dd, yyyy 'at' hh:mm a",
                  )
                ) : (
                  "Never synced"
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-3 sm:flex-row">
          <Button
            color="light"
            isLoading={isScanning}
            onClick={handleCheckForUpdates}
            className="flex items-center justify-center space-x-2 border-2 border-gray-200 bg-white transition-colors hover:border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:hover:border-gray-500 [&_span>svg]:fill-black dark:[&_span>svg]:fill-white"
          >
            <FiRefreshCw className="h-4 w-4" />
            <span>Scan for Updates</span>
          </Button>

          {!showUpdates ? (
            <Tooltip
              content="Please scan for updates first before syncing"
              placement="top"
            >
              <Button
                variant="primary"
                onClick={handleShowSyncModal}
                disabled
                className="flex items-center justify-center space-x-2 bg-gradient-to-r from-blue-600 to-blue-700 transition-all hover:from-blue-700 hover:to-blue-800"
              >
                <FiPlay className="h-4 w-4" />
                <span>Synchronize Now</span>
              </Button>
            </Tooltip>
          ) : !syncStatus?.hasChanged ? (
            <Tooltip
              content="No changes detected to synchronize"
              placement="top"
            >
              <Button
                variant="primary"
                onClick={handleShowSyncModal}
                disabled
                className="flex items-center justify-center space-x-2 bg-gradient-to-r from-blue-600 to-blue-700 transition-all hover:from-blue-700 hover:to-blue-800"
              >
                <FiPlay className="h-4 w-4" />
                <span>Synchronize Now</span>
              </Button>
            </Tooltip>
          ) : (
            <Button
              variant="primary"
              onClick={handleShowSyncModal}
              disabled={isSyncing}
              isLoading={isSyncing}
              className="flex items-center justify-center space-x-2 bg-gradient-to-r from-blue-600 to-blue-700 transition-all hover:from-blue-700 hover:to-blue-800"
            >
              <FiPlay className="h-4 w-4" />
              <span>Synchronize Now</span>
            </Button>
          )}
        </div>
      </Card>

      {showUpdates && (
        <Card className="[&>div]:p-0">
          <div>
            <h3 className="p-4 text-xl font-semibold text-gray-900 dark:text-white">
              Data to be Synchronized
            </h3>

            <div className="hidden md:block">
              <TabsWrapper tabs={SYNC_DATA_TABS} queryKey="tab" />
            </div>

            <div className="block space-y-6 md:hidden">
              <div className="px-4">
                <UncontrolledSelect
                  value={tab}
                  onChange={(value) => setTab(value || SYNC_DATA_TABS[0].key)}
                  placeholder="Select tab"
                  options={SYNC_DATA_TABS.map((tabItem) => ({
                    label: tabItem.title,
                    value: tabItem.key,
                  }))}
                />
              </div>
              {SYNC_DATA_TABS.find((tabItem) => tabItem.key === tab)?.content}
            </div>
          </div>
        </Card>
      )}

      <LoadingOverlay isLoading={isSyncing} className="!mt-0" />

      <ConfirmModal
        isOpen={showConfirmModal}
        onClose={() => setShowConfirmModal(false)}
        onConfirm={handleConfirmSync}
        isLoading={isSyncing}
        title="Confirm Synchronization"
        confirmLabel="Synchronize Now"
        size="lg"
      >
        <div className="space-y-4">
          <p className="text-gray-600 dark:text-gray-300">
            You are about to synchronize the following changes. This action
            cannot be undone.
          </p>

          {totalChanges === 0 ? (
            <div className="rounded-lg bg-gray-50 p-4 text-center dark:bg-gray-800">
              <p className="text-gray-500 dark:text-gray-400">
                No changes detected. Please scan for updates first.
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              <div className="rounded-lg bg-blue-50 p-4 dark:bg-blue-950/50">
                <p className="font-medium text-blue-900 dark:text-blue-100">
                  Total Changes: {totalChanges}
                </p>
              </div>

              <div className="space-y-2">
                {changeSummary.map((item) => (
                  <div
                    key={item.entityType}
                    className="flex items-center justify-between rounded-lg border border-gray-200 bg-white p-3 dark:border-gray-700 dark:bg-gray-800"
                  >
                    <span className="font-medium text-gray-900 dark:text-gray-100">
                      {item.entityName}
                    </span>
                    <div className="flex items-center space-x-4">
                      {item.updates > 0 && (
                        <div className="flex items-center space-x-1 text-blue-600">
                          <HiArrowTrendingUp className="h-4 w-4" />
                          <span className="text-sm font-medium">
                            {item.updates}
                          </span>
                        </div>
                      )}
                      {item.deletes > 0 && (
                        <div className="flex items-center space-x-1 text-red-600">
                          <HiTrash className="h-4 w-4" />
                          <span className="text-sm font-medium">
                            {item.deletes}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </ConfirmModal>
    </div>
  );
};
