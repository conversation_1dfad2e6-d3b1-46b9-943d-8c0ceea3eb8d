import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import {
  AddPromptVariablePayload,
  UpdatePromptVariablePayload,
} from "@/lib/apis/prompt-variables";

import { promptVariableKeys } from "./use-prompt-variable-queries";

export const useAddPromptVariable = () => {
  return useMutation({
    mutationFn: (payload: AddPromptVariablePayload) =>
      api.promptVariables.create(payload),
    onError: (err) =>
      toast.error(err?.message || "Fail to create prompt variable"),
    onSettled: (_, err) =>
      !err && toast.success("Create prompt variable successfully"),
    meta: {
      awaits: promptVariableKeys.allLists(),
    },
  });
};

export const useUpdatePromptVariable = () => {
  return useMutation({
    mutationFn: (payload: UpdatePromptVariablePayload) =>
      api.promptVariables.update(payload),
    onError: (err) =>
      toast.error(err?.message || "Fail to update prompt variable"),
    onSettled: (_, err) =>
      !err && toast.success("Update prompt variable successfully"),
    meta: {
      awaits: promptVariableKeys.allLists(),
    },
  });
};

export const useDeletePromptVariable = () => {
  return useMutation({
    mutationFn: (id: string) => api.promptVariables.delete(id),
    onError: (err) =>
      toast.error(err?.message || "Fail to delete prompt variable"),
    onSettled: (_, err) =>
      !err && toast.success("Delete prompt variable successfully"),
    meta: {
      awaits: promptVariableKeys.allLists(),
    },
  });
};
