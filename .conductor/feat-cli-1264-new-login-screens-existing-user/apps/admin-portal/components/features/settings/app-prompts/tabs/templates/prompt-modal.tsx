import { zod<PERSON><PERSON><PERSON>ver } from "@hookform/resolvers/zod";
import { Toolt<PERSON> } from "flowbite-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { HiOutlineDotsHorizontal } from "react-icons/hi";
import { IoIosAddCircleOutline } from "react-icons/io";
import { MdDelete, MdOutlineEdit } from "react-icons/md";
import { TiCloudStorageOutline } from "react-icons/ti";
import { z } from "zod";

import { Button, CloseButton } from "@/components/ui/button";
import {
  Dropdown,
  DropdownContent,
  DropdownItem,
  DropdownTrigger,
} from "@/components/ui/dropdown";
import { Form, InputField, Select, Textarea } from "@/components/ui/form";
import { InputNumber } from "@/components/ui/form/input-number-with-buttons";
import { Label } from "@/components/ui/form/label";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { useDisclosure } from "@/hooks/use-disclosure";
import { cn } from "@/lib/utils";

import { ExtendedPrompt } from ".";
import {
  useAddProtocolPrompt,
  useUpdateProtocolPrompt,
} from "./hooks/use-protocol-prompt-mutations";
import { useModels } from "./hooks/use-protocol-prompt-query";
import {
  AddMessageModal,
  ChatMessage,
  ROLE_MESSAGE_TYPES,
} from "./message-modal";

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedPrompt: ExtendedPrompt | null;
};

export const MODEL_PROVIDERS = [
  {
    label: "Gemini",
    value: "gemini",
  },
  {
    label: "OpenAI",
    value: "openai",
  },
] as const;

const schema = z.object({
  name: z
    .string({
      required_error: "Name is required",
      invalid_type_error: "Name is required",
    })
    .min(1, "Name is required"),
  key: z.enum(
    ["protocolExplorer", "isfArtifactCategory", "tmfArtifactCategory"],
    {
      errorMap: () => ({
        message: "Key is required",
      }),
    },
  ),
  modelProvider: z.enum(["gemini", "openai"], {
    errorMap: () => ({
      message: "Model provider is required",
    }),
  }),
  model: z
    .string({
      required_error: "Model is required",
      invalid_type_error: "Model is required",
    })
    .min(1, "Model is required"),
  temperature: z.coerce
    .number({
      invalid_type_error: "Temperature must be a number",
    })
    .min(0, "Minimum value is 0")
    .optional(),
  topP: z.coerce
    .number({
      invalid_type_error: "TopP must be a number",
    })
    .min(0, "Minimum value is 0")
    .max(1, "Maximum value is 1")
    .optional(),
  topK: z.coerce
    .number({
      invalid_type_error: "TopK must be a number",
    })
    .min(0, "Minimum value is 0")
    .optional(),
  description: z.string().optional(),
  templateContent: z
    .array(
      z.object({
        role: z.enum(ROLE_MESSAGE_TYPES, {
          errorMap: () => ({
            message: "Role is required",
          }),
        }),
        message: z.string().min(1),
        isCacheable: z.boolean(),
      }),
    )
    .min(0),
});

type FormValues = z.infer<typeof schema>;

export type ChatMessageWithId = ChatMessage & {
  id: number;
};

export const PROMPT_TYPES = [
  {
    label: "Protocol Explorer",
    value: "protocolExplorer",
  },
  {
    label: "ISF Artifact Category",
    value: "isfArtifactCategory",
  },
  {
    label: "TMF Artifact Category",
    value: "tmfArtifactCategory",
  },
] as const;

const PromptModal = ({ isOpen, onClose, selectedPrompt }: Props) => {
  const {
    isOpen: isOpenAddMessageModal,
    close: onCloseAddMessageModal,
    open: onOpenAddMessageModal,
  } = useDisclosure();

  const [selectedMessage, setSelectedMessage] =
    useState<ChatMessageWithId | null>(null);

  const formHandler = useForm<FormValues>({
    mode: "onChange",
    resolver: zodResolver(schema),
    defaultValues: {
      templateContent:
        selectedPrompt?.templateContent.map((chat) => ({
          role: chat.role,
          message: chat.parts[0].text,
          isCacheable: chat.isCacheable || false,
        })) ?? [],
      description: selectedPrompt?.description || "",
      name: selectedPrompt?.name || "",
      temperature: selectedPrompt?.generationConfig?.temperature || 0,
      topK: selectedPrompt?.generationConfig?.topK || 40,
      topP: selectedPrompt?.generationConfig?.topP || 0.8,
      key: selectedPrompt?.key,
      model: selectedPrompt?.model,
      modelProvider: selectedPrompt?.modelProvider,
    },
  });

  const { mutateAsync: addPrompt, isPending: isAdding } =
    useAddProtocolPrompt();
  const { mutateAsync: updatePrompt, isPending: isUpdating } =
    useUpdateProtocolPrompt();
  const modelProvider = formHandler.watch("modelProvider");
  const messages = formHandler.watch("templateContent");

  const { data: models } = useModels(modelProvider);

  const isEditing = selectedPrompt?.isEditing;

  const handleSaveMessage = (
    chatMessage: ChatMessage & {
      id?: number;
    },
  ) => {
    const isEditing = typeof chatMessage.id === "number";

    const newMessages = isEditing
      ? messages.map((currentMessage, idx) =>
          idx !== chatMessage.id
            ? currentMessage
            : {
                message: chatMessage.message,
                role: chatMessage.role,
                isCacheable: chatMessage.isCacheable,
              },
        )
      : [
          ...messages,
          {
            message: chatMessage.message,
            role: chatMessage.role,
            isCacheable: chatMessage.isCacheable,
          },
        ];

    formHandler.setValue("templateContent", newMessages);
  };

  const handleCloseMessageModal = () => {
    onCloseAddMessageModal();
    setSelectedMessage(null);
  };

  const handleRemoveMessage = (idx: number) => {
    formHandler.setValue(
      "templateContent",
      messages.filter((_, index) => index !== idx),
    );
  };

  const handleEditMessage = (message: ChatMessageWithId) => {
    setSelectedMessage(message);
    onOpenAddMessageModal();
  };

  const handleSubmit = async (data: FormValues) => {
    const payload = {
      ...data,
      generationConfig: {
        temperature: data.temperature,
        topK: data.topK,
        topP: data.topP,
      },
      templateContent: data.templateContent.map((chat) => ({
        role: chat.role,
        parts: [
          {
            text: chat.message,
          },
        ],
        isCacheable: chat.isCacheable || false,
      })),
    };
    isEditing
      ? await updatePrompt({
          ...payload,
          id: selectedPrompt.id,
        })
      : await addPrompt(payload);
    onClose();
  };

  return (
    <>
      <WrapperModal
        isOpen={isOpen}
        onClose={onClose}
        size="4xl"
        title={`Template Details`}
      >
        <Form
          className="grid  gap-4 sm:grid-cols-2"
          onSubmit={handleSubmit}
          formMethods={formHandler}
          schema={schema}
        >
          <div className="space-y-1">
            <Label htmlFor="name">Name</Label>
            <InputField
              id="name"
              name="name"
              placeholder="Enter name"
              readOnly
            />
          </div>

          <div className="space-y-1">
            <Label htmlFor="key">Key</Label>
            <Select
              id="key"
              name="key"
              placeholder="Select a key"
              options={PROMPT_TYPES.map((type) => ({
                label: type.label,
                value: type.value,
              }))}
              readOnly
            />
          </div>

          <div className="space-y-1">
            <Label htmlFor="key">Model Provider</Label>
            <Select
              id="modelProvider"
              name="modelProvider"
              placeholder="Select a modelProvider"
              options={MODEL_PROVIDERS.map((provider) => ({
                label: provider.label,
                value: provider.value,
              }))}
              readOnly
            />
          </div>

          <div className="space-y-1">
            <Label htmlFor="model">Model</Label>
            <Select
              id="model"
              name="model"
              placeholder="Select a model"
              options={
                models?.results.map((model) => ({
                  label: model,
                  value: model,
                })) ?? []
              }
              dependentFieldNames={["modelProvider"]}
              readOnly
            />
          </div>

          <div className="space-y-2 sm:col-span-2">
            <div className="space-y-2">
              <Label>Chat Message</Label>
              {messages.map((mess, id) => (
                <Message
                  onRemove={handleRemoveMessage}
                  chatMessage={{ ...mess, id: id }}
                  key={id}
                  onEdit={handleEditMessage}
                />
              ))}
            </div>
          </div>
          <div className="space-y-1 sm:col-span-2">
            <Label htmlFor="temperature">Temperature</Label>
            <InputNumber
              id="temperature"
              name="temperature"
              placeholder="Enter temperature"
              min={0}
              isAllowNegative={false}
              readOnly
            />
          </div>
          <div className="grid gap-4 sm:col-span-2 sm:grid-cols-2">
            <div className="space-y-1">
              <Label htmlFor="topP">TopP</Label>
              <InputNumber
                id="topP"
                name="topP"
                placeholder="Enter TopP"
                min={0}
                step={0.1}
                max={1}
                isAllowNegative={false}
                readOnly
              />
            </div>
            <div className="space-y-1">
              <Label htmlFor="topK">TopK</Label>
              <InputNumber
                id="topK"
                name="topK"
                placeholder="Enter TopK"
                min={0}
                isAllowNegative={false}
                readOnly
              />
            </div>
          </div>
          <div className="space-y-1 sm:col-span-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              placeholder="Enter description"
              readOnly
            />
          </div>
        </Form>
      </WrapperModal>

      {isOpenAddMessageModal && (
        <AddMessageModal
          isOpen={isOpenAddMessageModal}
          onClose={handleCloseMessageModal}
          onSave={handleSaveMessage}
          messages={messages}
          selectedMessage={selectedMessage}
        />
      )}
    </>
  );
};

const Message = ({
  chatMessage,
  onRemove,
  onEdit,
}: {
  chatMessage: ChatMessageWithId;
  onRemove: (idx: number) => void;
  onEdit: (data: ChatMessageWithId) => void;
}) => {
  const [open, setOpen] = useState(false);

  return (
    <div className="space-y-1">
      <span className="flex items-center gap-2 text-sm font-medium capitalize dark:text-white">
        {chatMessage.role}{" "}
        <Tooltip content={chatMessage.isCacheable ? "Cached" : "Uncached"}>
          <TiCloudStorageOutline
            className={cn(
              "size-6",
              chatMessage.isCacheable ? "text-green-500" : "text-red-500",
            )}
          />
        </Tooltip>
      </span>
      <div className="flex gap-x-2">
        <p className="flex-1 content-center whitespace-pre-wrap rounded-lg border border-gray-300 bg-white p-2 text-sm shadow dark:border-gray-600 dark:bg-gray-700  dark:text-white">
          {chatMessage.message}
        </p>
      </div>
    </div>
  );
};

export default PromptModal;
