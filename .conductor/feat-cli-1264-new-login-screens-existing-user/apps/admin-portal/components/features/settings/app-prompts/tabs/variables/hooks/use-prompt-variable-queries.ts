import { useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

import { useFilterVariable } from "./useFilterVariable";

export const promptVariableKeys = {
  all: () => ["prompt-variables"] as const,

  allLists: () => [...promptVariableKeys.all(), "list"] as const,
  list: (params?: MetadataParams) =>
    [...promptVariableKeys.all(), "list", { params }] as const,

  resolverList: () => [...promptVariableKeys.all(), "resolvers"] as const,
};

export const usePromptVariables = () => {
  const { page, take } = usePagination();
  const { key, label, type, computed, published, isActive, isCacheable } =
    useFilterVariable();

  const params = {
    page,
    take,
    filter: {
      key: key || undefined,
      label: label || undefined,
      computed: computed || undefined,
      type: type || undefined,
      published: published || undefined,
      isActive: isActive || undefined,
      isCacheable: isCacheable || undefined,
    },
  };

  return useQuery({
    queryKey: promptVariableKeys.list(params),
    queryFn: () => api.promptVariables.list(params),
    placeholderData: (prev) => prev,
  });
};

export const useResolvers = () => {
  return useQuery({
    queryKey: promptVariableKeys.resolverList(),
    queryFn: () => api.promptVariables.listResolvers(),
  });
};
