import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { AddGroupPayload } from "@/lib/apis/groups/types";

import { USE_GROUPS_QUERY_KEY } from "./use-groups";

export const useAddGroup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: AddGroupPayload) => api.groups.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [USE_GROUPS_QUERY_KEY] });
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
};
