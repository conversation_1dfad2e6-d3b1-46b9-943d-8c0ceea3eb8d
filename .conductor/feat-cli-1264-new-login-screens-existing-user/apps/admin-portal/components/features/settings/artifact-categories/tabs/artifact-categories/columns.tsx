import { type ColumnDef } from "@tanstack/react-table";
import { Tooltip } from "flowbite-react";
import { BiHealth } from "react-icons/bi";
import { BsCalendar2XFill } from "react-icons/bs";
import {
  FaFolderOpen,
  FaMagnifyingGlass,
  FaRegFolderOpen,
} from "react-icons/fa6";
import { PiSignature } from "react-icons/pi";

import { ActiveStatusBadge } from "@/components/ui/badges";
import type { ArtifactCategory } from "@/lib/apis/artifact-categories";

export const getColumns = (
  onView: (artifactCategory: ArtifactCategory) => void,
): ColumnDef<ArtifactCategory>[] => [
  {
    header: "Type",
    accessorKey: "type",
    id: "type",
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="flex flex-col gap-1">
          <Tooltip content={`TMF: ${data.tmfRefModel.tmfRefModel}`}>
            <FaRegFolderOpen size={20} className="text-blue-600" />
          </Tooltip>
          <Tooltip content={`ISF: ${data.isfRefModel.isfRefModel}`}>
            <FaFolderOpen size={20} className="text-green-600" />
          </Tooltip>
        </div>
      );
    },
  },
  {
    header: "Document Number",
    accessorKey: "documentNumber",
    id: "documentNumber",
    cell: ({ row }) => {
      const data = row.original;
      const tmfNumber =
        data.tmfZoneNumber && data.tmfSectionNumber && data.tmfRecordGroupNumber
          ? `${data.tmfZoneNumber}.${data.tmfSectionNumber}.${data.tmfRecordGroupNumber}`
          : null;
      const isfNumber =
        data.isfZoneNumber && data.isfSectionNumber && data.isfRecordGroupNumber
          ? `${data.isfZoneNumber}.${data.isfSectionNumber}.${data.isfRecordGroupNumber}`
          : null;

      return (
        <button
          className="text-primary-500 flex cursor-pointer flex-col text-left hover:underline"
          onClick={() => onView(data)}
        >
          {tmfNumber && <span>{tmfNumber}</span>}
          {isfNumber && <span>{isfNumber}</span>}
        </button>
      );
    },
  },
  {
    header: "Record Group",
    accessorKey: "recordGroup",
    id: "recordGroup",
    cell: ({ row }) => {
      const data = row.original;
      const tmfGroup =
        data.tmfZoneName && data.tmfSectionName && data.tmfRecordGroupName
          ? `${data.tmfZoneName} / ${data.tmfSectionName} / ${data.tmfRecordGroupName}`
          : null;
      const isfGroup =
        data.isfZoneName && data.isfSectionName && data.isfRecordGroupName
          ? `${data.isfZoneName} / ${data.isfSectionName} / ${data.isfRecordGroupName}`
          : null;

      return (
        <Tooltip content={data.description || "No description available"}>
          <div className="flex cursor-help flex-col">
            {tmfGroup && <span className="text-sm">{tmfGroup}</span>}
            {isfGroup && <span className="text-sm">{isfGroup}</span>}
          </div>
        </Tooltip>
      );
    },
  },
  {
    header: "Record Type",
    accessorKey: "recordType",
    id: "recordType",
    cell: ({ row }) => {
      const data = row.original;
      const alternativeNames = data.alternativeNames;
      const tooltipContent = alternativeNames
        ? `${alternativeNames}`
        : "No alternative names";

      return (
        <Tooltip content={tooltipContent}>
          <span className="cursor-help">{data.recordType}</span>
        </Tooltip>
      );
    },
  },
  {
    header: "Properties",
    id: "properties",
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="flex items-center gap-2">
          {data.requiresSignature && (
            <Tooltip content="Requires Signature">
              <PiSignature size={20} className="text-blue-600" />
            </Tooltip>
          )}
          {data.expires && (
            <Tooltip content="Expires">
              <BsCalendar2XFill size={20} className="text-orange-600" />
            </Tooltip>
          )}
          {data.inspectableRecord && (
            <Tooltip content="Inspectable">
              <FaMagnifyingGlass size={20} className="text-purple-600" />
            </Tooltip>
          )}
          {data.includesPHI && (
            <Tooltip content="PHI">
              <BiHealth size={20} className="text-red-600" />
            </Tooltip>
          )}
        </div>
      );
    },
  },
  {
    header: "Status",
    cell: ({ row }) => {
      return <ActiveStatusBadge isActive={row.original.isActive} />;
    },
  },
];
