"use client";
import { Card } from "flowbite-react";
import React, { useMemo, useState } from "react";
import { HiPlus } from "react-icons/hi";

import { SearchField } from "@/components/shared";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { TableDataPagination } from "@/components/ui/pagination";
import { TableLoading } from "@/components/ui/table";
import { TableData } from "@/components/ui/table/table";
import { useDisclosure } from "@/hooks/use-disclosure";
import { CategoryVersion } from "@/lib/apis/artifact-categories";

import { CategoryVersionModal } from "./category-version-modal";
import { generateVersionColumns } from "./columns";
import { useDeleteCategoryVersion } from "./hooks/use-category-version-mutations";
import { useCategoryVersions } from "./hooks/use-category-version-queries";

export const CategoryVersionTab = () => {
  const { isOpen, close, open } = useDisclosure();

  const [selectedVersion, setSelectedVersion] =
    useState<CategoryVersion | null>(null);
  // const [selectedVersionForSummary, setSelectedVersionForSummary] =
  //   useState<CategoryVersion | null>(null);
  const { data, isPending, isPlaceholderData } = useCategoryVersions();
  const { mutate, isPending: isDeleting } = useDeleteCategoryVersion();
  const handleCloseModal = () => {
    close();
    setSelectedVersion(null);
  };

  // const handleCloseSummaryModal = () => {
  //   setSelectedVersionForSummary(null);
  // };

  const columns = useMemo(
    () =>
      generateVersionColumns({
        onView: (data) => {
          setSelectedVersion(data);
          open();
        },
      }),
    [open],
  );
  return (
    <>
      <div className="mb-4">
        <h2 className="text-xl font-semibold text-gray-900 sm:text-2xl dark:text-white">
          Category Versions
        </h2>
      </div>
      <Card className="[&>div]:p-0">
        <div className="flex items-center justify-between gap-4 p-4">
          <div>
            <SearchField placeholder="Search..." />
          </div>
        </div>
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <>
            <LoadingWrapper isLoading={isPlaceholderData || isDeleting}>
              <TableData data={data?.results ?? []} columns={columns} />
            </LoadingWrapper>
            {data?.metadata && <TableDataPagination metadata={data.metadata} />}
          </>
        )}
      </Card>
      <CategoryVersionModal
        isOpen={isOpen}
        onClose={handleCloseModal}
        selectedVersion={selectedVersion}
      />
      {/* {selectedVersionForSummary && (
        <SummaryModal
          isOpen={!!selectedVersionForSummary}
          onClose={handleCloseSummaryModal}
          selectedVersion={selectedVersionForSummary}
        />
      )} */}
    </>
  );
};
