import { ColumnDef } from "@tanstack/react-table";

import { TableViewButton } from "@/components/shared/table-action-buttons";
import { CategoryVersion } from "@/lib/apis/artifact-categories";
import { formatDate } from "@/lib/utils";

export const generateVersionColumns = ({
  onView,
}: {
  onView: (data: CategoryVersion) => void;
}): ColumnDef<CategoryVersion>[] => [
  {
    accessorKey: "id",
    header: "ID",
  },
  {
    accessorKey: "version",
    header: "Version",
  },
  {
    header: "Effective Date",
    cell: ({ row }) => {
      const data = row.original;
      return data.effectiveDate
        ? formatDate(data.effectiveDate, "yyyy-MM-dd")
        : null;
    },
  },
  {
    accessorKey: "notes",
    header: "Description",
  },
  {
    header: "Actions",
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="flex gap-x-4">
          <TableViewButton type="button" onClick={() => onView(data)} />
        </div>
      );
    },
  },
];
