import { z } from "zod";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { LazySelect } from "@/components/ui/lazy-select";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { useInfiniteCategoryVersion } from "@/hooks/queries/use-infinite-category-versions";

import { useExportArtifactCategory } from "./hooks/use-artifact-category-mutations";

const schema = z.object({
  version: z.string().optional(),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
};

export const ExportModal = function ({ isOpen, onClose }: Props) {
  const { mutateAsync, isPending: isExporting } = useExportArtifactCategory();

  const onSubmit = async (data: z.infer<typeof schema>) => {
    const res = await mutateAsync(data.version);
    const url = window.URL.createObjectURL(new Blob([res]));

    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", "artifact-categories.csv");
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
    onClose();
  };

  return (
    <WrapperModal
      isOpen={isOpen}
      onClose={onClose}
      title="Export Artifact Category"
      className="[&>div]:max-w-md"
    >
      <Form mode="onChange" schema={schema} onSubmit={onSubmit}>
        <LazySelect
          id="version"
          name="version"
          placeholder="Select a version..."
          useInfiniteQuery={useInfiniteCategoryVersion}
          getOptionLabel={(option) => option.version.toString()}
          getOptionValue={(option) => option.id}
        />

        <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
          <CloseButton onClose={onClose} />
          <Button type="submit" isLoading={isExporting} variant="primary">
            Export
          </Button>
        </div>
      </Form>
    </WrapperModal>
  );
};
