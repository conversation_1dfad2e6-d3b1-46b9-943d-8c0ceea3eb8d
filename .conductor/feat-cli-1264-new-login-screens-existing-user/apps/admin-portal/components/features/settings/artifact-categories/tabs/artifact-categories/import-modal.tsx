import Papa from "papa<PERSON><PERSON>";
import React, { useRef } from "react";
import toast from "react-hot-toast";
import { z } from "zod";

import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Form, FormActions } from "@/components/ui/form";
import { FileDropzone } from "@/components/ui/form/file-drop-zone";
import { Label } from "@/components/ui/form/label";
import { ConfirmModal } from "@/components/ui/modal/confirm-modal";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { useDisclosure } from "@/hooks/use-disclosure";

import { useImportArtifactCategory } from "./hooks/use-artifact-category-mutations";
import { useGetVersions } from "./hooks/use-versions";

const schema = z.object({
  file: z.instanceof(File, { message: "Please select a CSV file" }),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
};

export const ImportModal = ({ isOpen, onClose }: Props) => {
  const formRef = useRef<FormActions<z.ZodType<z.infer<typeof schema>>>>(null);
  const { mutateAsync, isPending } = useImportArtifactCategory();
  const { mutateAsync: getVersions, isPending: isPendingGetVersions } =
    useGetVersions();

  const {
    close: onCloseConfirmCreateNewVersionModal,
    isOpen: isOpenConfirmCreateNewVersionModal,
    open: openConfirmCreateNewVersionModal,
  } = useDisclosure();

  const {
    close: onCloseConfirmExistingVersionModal,
    isOpen: isOpenConfirmExistingVersionModal,
    open: openConfirmExistingVersionModal,
  } = useDisclosure();

  const handleSubmit = async (data: z.infer<typeof schema>) => {
    const selectedFile = data.file;
    Papa.parse(selectedFile, {
      header: true,
      skipEmptyLines: true,
      complete: async (results) => {
        const row = results.data as Record<string, string>[];

        if (!row.length) return toast.error("No data found in the file");

        const uniqueCategoryVersions = new Set(
          row.map((row) => row["category_version"]),
        );

        if (uniqueCategoryVersions.size > 1) {
          return toast.error(
            "Category version mismatch error. All category version ids must be the same.",
          );
        }
        const isMissingCategoryVersion = uniqueCategoryVersions.has("");

        if (isMissingCategoryVersion) {
          return openConfirmCreateNewVersionModal();
        }
        const firstVersion = uniqueCategoryVersions.values().next()
          .value as string;
        const versions = await getVersions(firstVersion);
        const existingVersion = versions?.results.find(
          (version) => version.version.toString() === firstVersion,
        );
        if (existingVersion) {
          return openConfirmExistingVersionModal();
        }
        const formData = new FormData();
        formData.append("file", selectedFile);
        await mutateAsync({
          formData,
        });

        onClose();
      },
      error: (error) => {
        console.log("error", error);
      },
    });
  };

  const handleConfirm = async () => {
    const formData = new FormData();
    formData.append(
      "file",
      formRef.current?.formHandler.getValues("file") as File,
    );
    await mutateAsync({
      formData,
    });
    onCloseConfirmCreateNewVersionModal();
    onClose();
  };

  return (
    <>
      <WrapperModal
        isOpen={isOpen}
        onClose={onClose}
        title="Import Artifact Categories"
      >
        <Form
          schema={schema}
          ref={formRef}
          onSubmit={handleSubmit}
          className="space-y-4"
        >
          <div className="flex flex-col gap-2">
            <Label required htmlFor="file">
              Artifact Categories
            </Label>
            <FileDropzone name="file" acceptTypes={[".csv"]} />
          </div>
          <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
            <CloseButton onClose={onClose} />
            <Button
              type="submit"
              variant="primary"
              isLoading={
                (!isOpenConfirmCreateNewVersionModal &&
                  !isOpenConfirmExistingVersionModal &&
                  isPending) ||
                isPendingGetVersions
              }
            >
              Import
            </Button>
          </div>
        </Form>
      </WrapperModal>
      <ConfirmModal
        isOpen={isOpenConfirmCreateNewVersionModal}
        onClose={onCloseConfirmCreateNewVersionModal}
        onConfirm={handleConfirm}
        isLoading={isPending}
        title="Confirm Import"
      >
        <span className="dark:text-white">
          Importing this will create a new Category Version. Do you want to
          proceed?
        </span>
      </ConfirmModal>

      <ConfirmModal
        isOpen={isOpenConfirmExistingVersionModal}
        onClose={onCloseConfirmExistingVersionModal}
        onConfirm={handleConfirm}
        isLoading={isPending}
        title="Confirm Import"
      >
        <span className="dark:text-white">
          This import will append any non duplicated entries to the selected
          category version. Do you want to continue?
        </span>
      </ConfirmModal>
    </>
  );
};
