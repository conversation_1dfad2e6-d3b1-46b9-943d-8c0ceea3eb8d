"use client";
import { Card } from "flowbite-react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useState } from "react";
import { CiExport, CiImport } from "react-icons/ci";
import { Hi<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, HiX } from "react-icons/hi";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Button } from "@/components/ui/button";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import { useDisclosure } from "@/hooks/use-disclosure";
import type { ArtifactCategory } from "@/lib/apis/artifact-categories";
import { downloadBlob, generateEmptyCsv } from "@/lib/utils";

import { getColumns } from "./columns";
import { ExportModal } from "./export-modal";
import { useLatestVersionArtifactCategories } from "./hooks/use-artifact-categories";
import { useFilterArtifactCategories } from "./hooks/use-filter-artifact-categories";
import { ImportModal } from "./import-modal";
import { ModalArtifactCategory } from "./modal-artifact-categories";
import { ModalFilter } from "./modal-filter";

const ARTIFACT_CATEGORY_HEADERS = [
  "tmf_zone_num",
  "tmf_zone_name",
  "tmf_section_num",
  "tmf_section_name",
  "tmf_record_group_num",
  "tmf_record_group_name",
  "record_type",
  "alternative_names",
  "is_tmf",
  "is_isf",
  "isf_zone_num",
  "isf_zone_name",
  "isf_section_num",
  "isf_section_name",
  "isf_record_group_num",
  "isf_record_group_name",
  "description",
  "requires_signature",
  "expires",
  "inspectable_record",
  "core_or_recommended",
  "includes_phi",
  "origin",
  "iit_study_artifact",
  "tmf_ref_model",
  "isf_ref_model",
  "category_version",
];
export const ArtifactCategoriesTab = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  const {
    tmfZoneName,
    tmfSectionName,
    tmfRecordGroupName,
    isfZoneName,
    isfSectionName,
    isfRecordGroupName,
    recordType,
    alternativeNames,
    isTMF,
    isISF,
    version,
    isActive,
    versionLatest,
    requiresSignature,
    expires,
    inspectableRecord,
    includesPHI,
  } = useFilterArtifactCategories();

  const { data, isPending, isPlaceholderData } =
    useLatestVersionArtifactCategories();

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const {
    close: onCloseImportModal,
    isOpen: isOpenImportModal,
    open: onOpenImportModal,
  } = useDisclosure();

  const {
    close: onCloseExportModal,
    isOpen: isOpenExportModal,
    open: onOpenExportModal,
  } = useDisclosure();

  const [selectedArtifactCategory, setSelectedArtifactCategory] =
    useState<ArtifactCategory>();

  const appliedFilter = [
    // TMF specific filters
    { label: "TMF Zone", value: tmfZoneName, key: "tmfZoneName" },
    { label: "TMF Section", value: tmfSectionName, key: "tmfSectionName" },
    {
      label: "TMF Record Group",
      value: tmfRecordGroupName,
      key: "tmfRecordGroupName",
    },
    // ISF specific filters
    { label: "ISF Zone", value: isfZoneName, key: "isfZoneName" },
    { label: "ISF Section", value: isfSectionName, key: "isfSectionName" },
    {
      label: "ISF Record Group",
      value: isfRecordGroupName,
      key: "isfRecordGroupName",
    },
    // Common filters
    { label: "Record Type", value: recordType, key: "recordType" },
    {
      label: "Alternative Names",
      value: alternativeNames,
      key: "alternativeNames",
    },
    { label: "TMF", value: isTMF, key: "isTMF" },
    { label: "ISF", value: isISF, key: "isISF" },
    { label: "Version", value: version, key: "version" },
    { label: "Active", value: isActive, key: "isActive" },
    { label: "Latest Version", value: versionLatest, key: "versionLatest" },
    // Property filters
    {
      label: "Requires Signature",
      value: requiresSignature,
      key: "requiresSignature",
    },
    { label: "Expires", value: expires, key: "expires" },
    {
      label: "Inspectable Record",
      value: inspectableRecord,
      key: "inspectableRecord",
    },
    { label: "Includes PHI", value: includesPHI, key: "includesPHI" },
  ];

  const handleRemoveAFilter = (key: string) => {
    const currentParams = new URLSearchParams(
      Array.from(searchParams.entries()),
    );
    currentParams.delete(key);
    currentParams.set("page", "1");

    const query = currentParams ? `?${currentParams.toString()}` : "";
    router.push(`${pathname}${query}`);
  };

  const handleViewArtifactCategory = (artifactCategory: ArtifactCategory) => {
    setSelectedArtifactCategory(artifactCategory);
    setIsModalOpen(true);
  };

  const handleAddArtifactCategory = () => {
    setSelectedArtifactCategory(undefined);
    setIsModalOpen(true);
  };

  const handleOpenFilterModal = () => {
    setIsFilterModalOpen(true);
  };

  const handleDownloadTemplate = () => {
    const csv = generateEmptyCsv(ARTIFACT_CATEGORY_HEADERS);
    downloadBlob(new Blob([csv]), "import-template.csv");
  };

  const renderTable = () => {
    if (isPending)
      return <TableLoading columns={getColumns(handleViewArtifactCategory)} />;
    return (
      <>
        <LoadingWrapper isLoading={isPlaceholderData}>
          <Table
            enableSorting
            data={data?.results ?? []}
            columns={getColumns(handleViewArtifactCategory)}
          />
        </LoadingWrapper>
        {data?.metadata && <TableDataPagination metadata={data.metadata} />}
      </>
    );
  };

  return (
    <>
      <div className="">
        <div className="mb-4">
          <h2 className="text-xl font-semibold text-gray-900 sm:text-2xl dark:text-white">
            Artifact Categories
          </h2>
        </div>
        <Card className="[&>div]:p-0">
          <div className="space-y-2 p-4">
            <div className="flex flex-col justify-between gap-2 sm:gap-4 xl:flex-row xl:items-center">
              <Button
                variant="outline"
                className=" w-fit"
                onClick={handleOpenFilterModal}
              >
                <HiFilter />
                <span className="relative">Filter</span>
              </Button>
              <div className="flex flex-1 flex-col gap-2 lg:flex-row lg:items-center lg:justify-end">
                <Button onClick={onOpenExportModal} variant="primary">
                  <CiExport size={18} />
                  Export
                </Button>
              </div>
            </div>
            {/* Filter Tags */}
            <div className="flex max-w-full flex-wrap items-center gap-2 overflow-hidden">
              {appliedFilter.map((filter) => {
                if (!filter.value) return null;
                return (
                  <div
                    key={filter.key}
                    className="flex items-center gap-1 rounded-full bg-gray-100 px-3 py-1 text-xs text-gray-700 dark:bg-gray-700 dark:text-gray-300"
                  >
                    <span className="max-w-[160px] truncate">
                      {filter.label} :{" "}
                      {typeof filter.value === "boolean" ? "Yes" : filter.value}
                    </span>
                    <button
                      onClick={() => handleRemoveAFilter(filter.key)}
                      className="ml-1 flex-shrink-0 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600"
                    >
                      <HiX className="h-4 w-4" />
                    </button>
                  </div>
                );
              })}
            </div>
          </div>
          {renderTable()}
        </Card>
      </div>
      {isModalOpen && (
        <ModalArtifactCategory
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          artifactCategory={selectedArtifactCategory}
        />
      )}
      {isFilterModalOpen && (
        <ModalFilter
          isOpen={isFilterModalOpen}
          onClose={() => setIsFilterModalOpen(false)}
        />
      )}
      {isOpenImportModal && (
        <ImportModal isOpen={isOpenImportModal} onClose={onCloseImportModal} />
      )}
      {isOpenExportModal && (
        <ExportModal isOpen={isOpenExportModal} onClose={onCloseExportModal} />
      )}
    </>
  );
};
