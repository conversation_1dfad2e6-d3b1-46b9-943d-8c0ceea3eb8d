import { ColumnDef } from "@tanstack/react-table";

import { TableViewButton } from "@/components/shared/table-action-buttons";
import { ISFRefModel } from "@/lib/apis/isf-ref-models";

export const generateISFColumns = ({
  onView,
}: {
  onView: (data: ISFRefModel) => void;
}): ColumnDef<ISFRefModel>[] => [
  {
    accessorKey: "id",
    header: "ID",
  },
  {
    accessorKey: "isfRefModel",
    header: "Label",
  },
  {
    accessorKey: "description",
    header: "Description",
  },
  {
    header: "Actions",
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="flex gap-x-4">
          <TableViewButton type="button" onClick={() => onView(data)} />
        </div>
      );
    },
  },
];
