import { useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

export const categoryVersionKeys = {
  all: () => ["category-versions"] as const,

  allLists: () => [...categoryVersionKeys.all(), "lists"] as const,
  list: (params: MetadataParams) =>
    [...categoryVersionKeys.allLists(), params] as const,
};

export const useCategoryVersions = () => {
  const { search } = useSearch();
  const { take, page } = usePagination();

  const params = {
    page,
    take,
    filter: {
      version: search,
    },
  };

  return useQuery({
    queryKey: categoryVersionKeys.list(params),
    queryFn: () => api.artifactCategories.getVersions(params),
    placeholderData: (prev) => prev,
  });
};
