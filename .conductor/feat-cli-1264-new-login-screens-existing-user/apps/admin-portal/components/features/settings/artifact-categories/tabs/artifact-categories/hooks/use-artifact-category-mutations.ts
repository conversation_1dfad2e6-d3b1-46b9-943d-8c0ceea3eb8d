import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type {
  CreateArtifactCategoryPayload,
  UpdateArtifactCategoryPayload,
} from "@/lib/apis/artifact-categories";

import { artifactCategoryKeys } from "./use-artifact-categories";

export const useAddArtifactCategory = () => {
  return useMutation({
    mutationFn: async (data: CreateArtifactCategoryPayload) => {
      return api.artifactCategories.create(data);
    },
    onSettled: (_, err) => {
      !err && toast.success("Artifact category created successfully");
    },

    onError: (error) => {
      toast.error(error.message);
    },
    meta: {
      awaits: artifactCategoryKeys.allList(),
    },
  });
};

export const useEditArtifactCategory = () => {
  return useMutation({
    mutationFn: async (
      data: UpdateArtifactCategoryPayload & { id: string },
    ) => {
      const { id, ...payload } = data;
      return api.artifactCategories.update(id, payload);
    },
    onSettled: (_, err) => {
      if (!err) toast.success("Artifact category updated successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
    meta: {
      awaits: artifactCategoryKeys.allList(),
    },
  });
};

export const useArchiveArtifactCategory = () => {
  return useMutation({
    mutationFn: async (id: string) => {
      return api.artifactCategories.archive(id);
    },
    onSettled: (_, err) => {
      !err && toast.success("Artifact category archived successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
    meta: {
      awaits: artifactCategoryKeys.allList(),
    },
  });
};

export const useImportArtifactCategory = () => {
  return useMutation({
    mutationFn: async (payload: { formData: FormData }) => {
      return api.artifactCategories.import(payload);
    },
    onSettled: (_, err) => {
      !err && toast.success("Import artifact category successfully");
    },
    onError: (error) => {
      toast.error(error?.message || "Fail to import artifact category");
    },
    meta: {
      awaits: artifactCategoryKeys.allList(),
    },
  });
};

export const useExportArtifactCategory = () => {
  return useMutation({
    mutationFn: async (version?: string) => {
      return api.artifactCategories.export(version);
    },
    onSettled: () => {
      toast.success("Export artifact category successfully");
    },
    onError: (error) => {
      toast.error(error?.message || "Fail to export artifact category");
    },
  });
};
