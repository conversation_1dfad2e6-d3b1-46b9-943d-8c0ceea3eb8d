import { ColumnDef } from "@tanstack/react-table";

import {
  TableRemoveButton,
  TableViewButton,
} from "@/components/shared/table-action-buttons";
import { Module } from "@/lib/apis/training-modules";

type Props = {
  onView: (module: Module) => void;
  onDelete: (module: Module) => void;
};

export const generateModulesColumns = ({
  onView,
  onDelete,
}: Props): ColumnDef<Module>[] => [
  {
    header: "Name",
    accessorKey: "name",
    cell: ({ row }) => {
      return (
        <button
          onClick={() => onView(row.original)}
          className="text-primary-500 cursor-pointer whitespace-nowrap underline-offset-4 hover:underline"
        >
          {row.original.name}
        </button>
      );
    },
  },
  {
    header: "Description",
    accessorKey: "description",
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      return (
        <div className="flex items-center gap-x-4">
          <TableViewButton
            type="button"
            onClick={() => {
              onView(row.original);
            }}
          />
          <TableRemoveButton
            onClick={() => {
              onDelete(row.original);
            }}
          />
        </div>
      );
    },
  },
];
