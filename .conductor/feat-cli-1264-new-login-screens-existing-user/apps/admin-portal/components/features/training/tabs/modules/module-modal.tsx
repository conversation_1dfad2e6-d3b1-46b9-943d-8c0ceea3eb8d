import { z } from "zod";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/button";
import { Form, InputField, Textarea } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { Module } from "@/lib/apis/training-modules";

import {
  useAddModule,
  useUpdateModule,
} from "../../hooks/use-training-module-mutations";

const schema = z.object({
  name: z
    .string({
      required_error: "Name is required",
      invalid_type_error: "Name is required",
    })
    .min(1, "Name is required"),
  description: z.string().optional(),
  externalId: z.string().optional(),
});

type FormValues = z.infer<typeof schema>;

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedModule: Module | null;
};

export const ModuleModal = function ({
  isOpen,
  onClose,
  selectedModule,
}: Props) {
  const { mutateAsync: addModule, isPending: isAdding } = useAddModule();

  const { mutateAsync: updateModule, isPending: isUpdating } =
    useUpdateModule();

  const isEditing = !!selectedModule;

  const onSubmit = async (data: FormValues) => {
    isEditing
      ? await updateModule({
          ...data,
          id: selectedModule.id,
        })
      : await addModule(data);
    onClose();
  };

  return (
    <WrapperModal
      isOpen={isOpen}
      onClose={onClose}
      title={`${isEditing ? "Edit" : "Add"} Module`}
    >
      <Form
        defaultValues={{
          name: selectedModule?.name || "",
          description: selectedModule?.description || "",
          externalId: selectedModule?.externalId || "",
        }}
        mode="onChange"
        schema={schema}
        onSubmit={onSubmit}
      >
        <div className="grid grid-cols-1 gap-6">
          <div className="flex flex-col gap-2">
            <Label htmlFor="name">Name</Label>
            <InputField
              id="name"
              name="name"
              placeholder="Enter module name..."
            />
          </div>
          <div className="flex flex-col gap-2">
            <Label htmlFor="externalId">External Id</Label>
            <InputField
              id="externalId"
              name="externalId"
              placeholder="Enter external id..."
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              placeholder="Enter description..."
            />
          </div>
        </div>

        <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
          <CloseButton onClose={onClose} />
          <Button
            type="submit"
            disabled={isAdding || isUpdating}
            isLoading={isUpdating || isAdding}
            variant="primary"
          >
            Save
          </Button>
        </div>
      </Form>
    </WrapperModal>
  );
};
