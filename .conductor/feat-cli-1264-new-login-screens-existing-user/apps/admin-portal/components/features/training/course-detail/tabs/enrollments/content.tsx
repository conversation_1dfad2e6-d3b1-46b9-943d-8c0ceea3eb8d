import { Card } from "flowbite-react";
import { useParams } from "next/navigation";
import { parseAsInteger, parseAsString, useQueryState } from "nuqs";
import { useMemo, useState } from "react";
import { IoMdAdd } from "react-icons/io";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Button } from "@/components/ui/button";
import { UncontrolledSelect } from "@/components/ui/form/select/uncontrolled-select";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import { useDisclosure } from "@/hooks/use-disclosure";
import { Enrollment } from "@/lib/apis/training-modules";

import { useEnrollmentsByCourseId } from "../../../hooks/use-training-module-queries";
import { generateEnrollmentColumns } from "./columns";
import { ConfirmDeleteEnrollmentModal } from "./confirm-delete-enrollment-modal";
import { ENROLLMENT_STATUSES, EnrollmentModal } from "./enrollment-modal";

export const EnrollmentTab = () => {
  const courseId = useParams().id as string;
  const [status, setStatus] = useQueryState("status", parseAsString);
  const [, setPage] = useQueryState("page", parseAsInteger);

  const { isOpen, open, close } = useDisclosure();
  const [selectedEnrollment, setSelectedEnrollment] =
    useState<Enrollment | null>(null);
  const [selectedEnrollmentToDelete, setSelectedEnrollmentToDelete] =
    useState<Enrollment | null>(null);
  const {
    isOpen: isDeleteModalOpen,
    open: openDeleteModal,
    close: closeDeleteModal,
  } = useDisclosure();

  const { data, isPending, isPlaceholderData } =
    useEnrollmentsByCourseId(courseId);

  const handleDeleteEnrollment = (enrollment: Enrollment) => {
    setSelectedEnrollmentToDelete(enrollment);
    openDeleteModal();
  };

  const handleCloseDeleteModal = () => {
    setSelectedEnrollmentToDelete(null);
    closeDeleteModal();
  };

  const handleClose = () => {
    close();
    setSelectedEnrollment(null);
  };

  const columns = useMemo(
    () =>
      generateEnrollmentColumns({
        onView: (data) => {
          setSelectedEnrollment(data);
          open();
        },
        onDelete: handleDeleteEnrollment,
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [open],
  );

  return (
    <>
      <Card className="[&>div]:p-0">
        <div className="mb-4 flex items-start justify-between p-4 pb-0 text-lg font-semibold sm:items-center">
          <h2 className="dark:text-gray-400">Enrollments</h2>

          <div className="flex flex-col-reverse gap-2 sm:flex-row">
            <UncontrolledSelect
              options={ENROLLMENT_STATUSES.map((status) => ({
                label: status.label,
                value: status.value,
              }))}
              className="w-[140px]"
              placeholder="Filter by status"
              value={status || ""}
              onChange={(value) => {
                setStatus(value || null);
                setPage(1);
              }}
            />
            <Button onClick={open} variant="primary">
              <IoMdAdd />
              Add Enrollment
            </Button>
          </div>
        </div>
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <>
            <LoadingWrapper isLoading={isPlaceholderData}>
              <Table columns={columns} data={data?.results ?? []} />
            </LoadingWrapper>
            {data?.metadata && <TableDataPagination metadata={data.metadata} />}
          </>
        )}
      </Card>

      {isOpen && (
        <EnrollmentModal
          onClose={handleClose}
          isOpen={isOpen}
          selectedEnrollment={selectedEnrollment}
        />
      )}
      {selectedEnrollmentToDelete && (
        <ConfirmDeleteEnrollmentModal
          isOpen={isDeleteModalOpen}
          onClose={handleCloseDeleteModal}
          selectedEnrollment={selectedEnrollmentToDelete}
        />
      )}
    </>
  );
};
