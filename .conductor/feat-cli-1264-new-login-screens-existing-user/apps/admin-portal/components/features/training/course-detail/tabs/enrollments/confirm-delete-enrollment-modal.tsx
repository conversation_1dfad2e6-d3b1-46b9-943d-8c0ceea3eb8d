import { TriangleAlert } from "lucide-react";
import { useParams } from "next/navigation";

import { Button } from "@/components/ui/button";
import { Modal } from "@/components/ui/modal";
import { Enrollment } from "@/lib/apis/training-modules";

import { useDeleteEnrollment } from "../../../hooks/use-training-module-mutations";

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedEnrollment: Enrollment;
};

export const ConfirmDeleteEnrollmentModal = ({
  isOpen,
  onClose,
  selectedEnrollment,
}: Props) => {
  const { id: courseId } = useParams();
  const { mutateAsync, isPending } = useDeleteEnrollment(courseId as string);

  const handleConfirmDelete = async () => {
    await mutateAsync(selectedEnrollment.id);
    onClose();
  };

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Delete Enrollment</Modal.Header>
      <Modal.Body>
        <div className="flex flex-col items-center justify-center gap-2.5">
          <TriangleAlert className="text-red-500" size={34} />
          <span className="text-xl font-medium leading-[150%] dark:text-white">
            Are you sure you want to delete this enrollment?
          </span>
          <p className="mt-4 text-center text-sm italic text-gray-500 dark:text-gray-500">
            This action cannot be undone.
          </p>
        </div>
      </Modal.Body>
      <Modal.Footer className="flex justify-end gap-2.5">
        <Button variant="outline" onClick={onClose} className="border-none">
          Cancel
        </Button>
        <Button
          variant="outlineDanger"
          onClick={handleConfirmDelete}
          disabled={isPending}
          isLoading={isPending}
        >
          Delete
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
