import { ColumnDef } from "@tanstack/react-table";

import {
  TableRemoveButton,
  TableViewButton,
} from "@/components/shared/table-action-buttons";
import { Chapter } from "@/lib/apis/training-modules";
import { capitalize } from "@/utils/string";

export const generateChapterColumns = ({
  onView,
  onDelete,
}: {
  onView: (data: Chapter) => void;
  onDelete: (data: Chapter) => void;
}): ColumnDef<Chapter>[] => [
  {
    header: "Name",
    accessorKey: "name",
  },
  {
    header: "Type",
    accessorKey: "type",
    cell: ({ row }) => {
      return capitalize(row.original.type);
    },
  },
  {
    header: "Course",
    accessorKey: "course.name",
  },
  {
    header: "Order",
    accessorKey: "order",
  },
  {
    header: "Description",
    accessorKey: "description",
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      return (
        <div className="flex items-center gap-4">
          <TableViewButton
            type="button"
            onClick={() => {
              onView(row.original);
            }}
          />
          <TableRemoveButton onClick={() => onDelete(row.original)} />
        </div>
      );
    },
  },
];
