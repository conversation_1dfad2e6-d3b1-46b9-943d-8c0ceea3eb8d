import { Breadcrumb } from "@/components/ui/breadcrumb";
import { But<PERSON> } from "@/components/ui/button";
import { PageHeader } from "@/components/ui/page-header";
import { TabsWrapper } from "@/components/ui/tabs-wrapper";

import { useSyncCourses } from "./hooks/use-training-module-mutations";
import { CoursesTab, ModulesTab } from "./tabs";

const BREADCRUMB_ITEMS = [{ label: "Training" }];

const TRAINING_TABS = [
  {
    key: "courses",
    title: "Courses",
    content: <CoursesTab />,
  },
  {
    key: "modules",
    title: "Training Modules",
    content: <ModulesTab />,
  },
];

export const TrainingContent = () => {
  const { mutateAsync, isPending } = useSyncCourses();

  const handleSyncCourses = async () => {
    await mutateAsync();
  };

  return (
    <>
      <Breadcrumb items={BREADCRUMB_ITEMS} />
      <div className="flex items-center justify-between">
        <PageHeader>Training</PageHeader>
        <Button
          onClick={handleSyncCourses}
          isLoading={isPending}
          variant="primary"
        >
          Sync With Ghost
        </Button>
      </div>
      <TabsWrapper tabs={TRAINING_TABS} />
    </>
  );
};
