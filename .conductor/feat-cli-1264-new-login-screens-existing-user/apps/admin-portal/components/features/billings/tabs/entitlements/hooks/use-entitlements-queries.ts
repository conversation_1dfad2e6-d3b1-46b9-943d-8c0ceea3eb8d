import { useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

export const entitlementsKeys = {
  all: () => ["entitlements"] as const,
  allLists: () => [...entitlementsKeys.all(), "list"] as const,
  list: (params?: MetadataParams) =>
    [...entitlementsKeys.allLists(), params] as const,
};

export const useEntitlements = (params?: { page?: number }) => {
  const { page: defaultPage, take: defaultTake } = usePagination();
  const { search } = useSearch();

  const innerParams = {
    page: params?.page ?? defaultPage,
    take: defaultTake,
    filter: {
      search,
    },
  };

  return useQuery({
    queryKey: entitlementsKeys.list(innerParams),
    queryFn: () => api.entitlements.list(innerParams),
    placeholderData: (prev) => prev,
  });
};
