import { ColumnDef } from "@tanstack/react-table";

import { Entitlement } from "@/lib/apis/entitlements";

export const entitlementsColumns: ColumnDef<Entitlement>[] = [
  {
    accessorKey: "displayName",
    header: "Display Name",
  },
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => row.original.name.replaceAll("_", " ").toUpperCase(),
  },
  {
    accessorKey: "bundle",
    header: "Bundle",
  },
  {
    accessorKey: "description",
    header: "Description",
    cell: ({ row }) => (
      <p className="break-words" title={row.getValue("description")}>
        {row.getValue("description") || "-"}
      </p>
    ),
  },
];
