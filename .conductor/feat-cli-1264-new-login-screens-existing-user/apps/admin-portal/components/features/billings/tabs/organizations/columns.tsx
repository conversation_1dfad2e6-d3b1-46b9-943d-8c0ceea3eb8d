import { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "flowbite-react";

import { TableViewButton } from "@/components/shared/table-action-buttons";
import { Entitlement, entitlements } from "@/lib/apis/entitlements";
import { Group } from "@/lib/apis/groups/types";

type OrganizationsColumnsProps = {
  onView: (organization: Group) => void;
};

export const generateOrganizationsColumns = ({
  onView,
}: OrganizationsColumnsProps): ColumnDef<Group>[] => [
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => (
      <button
        type="button"
        onClick={() => onView(row.original)}
        className="text-primary-500 hover:underline"
      >
        {row.getValue("name")}
      </button>
    ),
  },
  {
    accessorKey: "groupInstitutionType",
    header: "Group Institution Type",
    cell: ({ row }) => (
      <span className="capitalize">
        {row.getValue("groupInstitutionType") || "-"}
      </span>
    ),
  },
  {
    accessorKey: "type",
    header: "Type",
    cell: ({ row }) => (
      <span className="capitalize">{row.getValue("type") || "-"}</span>
    ),
  },
  {
    id: "city",
    header: "City",
    accessorFn: (row) => row.address?.city,
    cell: ({ row }) => row.original.address?.city || "-",
  },
  {
    id: "stateProvince",
    header: "State/Province",
    accessorFn: (row) => row.address?.stateProvince?.name,
    cell: ({ row }) => row.original.address?.stateProvince?.name || "-",
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => (
      <div className="flex gap-2">
        <TableViewButton type="button" onClick={() => onView(row.original)} />
      </div>
    ),
  },
];

type EntitlementsColumnsProps = {
  organizationEntitlements: Entitlement[];
  onToggle: (data: { entitlements: Entitlement[]; isAdding: boolean }) => void;
};

export const generateEntitlementsColumns = ({
  organizationEntitlements,
  onToggle,
}: EntitlementsColumnsProps): ColumnDef<Entitlement>[] => [
  {
    header: "Assigned",
    cell: ({ row }) => {
      const hasEntitlement = organizationEntitlements.some(
        (orgEntitlement) => orgEntitlement.id === row.original.id,
      );
      return (
        <Checkbox
          checked={hasEntitlement}
          className="size-5"
          onChange={(e) => {
            const isChecked = e.target.checked;
            const newEntitlements = isChecked
              ? [...organizationEntitlements, row.original]
              : organizationEntitlements.filter(
                  (entitlements) => entitlements.id !== row.original.id,
                );

            onToggle({
              entitlements: newEntitlements,
              isAdding: isChecked,
            });
          }}
        />
      );
    },
  },
  {
    accessorKey: "displayName",
    header: "Display Name",
  },
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => row.original.name.replaceAll("_", " ").toUpperCase(),
  },
  {
    accessorKey: "bundle",
    header: "Bundle",
  },
  {
    accessorKey: "description",
    header: "Description",
    cell: ({ row }) => (
      <p className="break-words" title={row.getValue("description")}>
        {row.getValue("description") || "-"}
      </p>
    ),
  },
];
