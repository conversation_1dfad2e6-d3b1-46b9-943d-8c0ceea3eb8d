import toast from "react-hot-toast";

import { USE_GROUP_QUERY_KEY } from "@/components/features/settings/groups/hooks/use-group";
import { useOptimisticMutation } from "@/hooks/use-optimistic-mutation";
import api from "@/lib/apis";
import { Entitlement } from "@/lib/apis/entitlements";
import { Group } from "@/lib/apis/groups/types";

export const useUpdateEntitlementGroup = (groupId: string) => {
  return useOptimisticMutation({
    queryKey: [USE_GROUP_QUERY_KEY, groupId],
    invalidates: [USE_GROUP_QUERY_KEY, groupId],
    mutationFn: ({
      entitlements,
    }: {
      entitlements: Entitlement[];
      isAdding: boolean;
    }) =>
      api.groups.updateEntitlements(groupId, {
        entitlementIds: entitlements.map((entitlement) => entitlement.id),
      }),
    onMutate: (payload) => {
      toast.success(
        `${payload.isAdding ? "Add" : "Delete"} entitlement successfully`,
      );
    },
    updater: (
      oldData: (Group & { entitlements: Entitlement[] }) | undefined,
      payload,
    ) => {
      if (!oldData) return undefined;
      return {
        ...oldData,
        entitlements: payload.entitlements,
      };
    },
    onError: (error, payload) => {
      toast.error(
        error?.message ??
          `Failed to ${payload.isAdding ? "add" : "delete"} entitlement`,
      );
    },
  });
};
