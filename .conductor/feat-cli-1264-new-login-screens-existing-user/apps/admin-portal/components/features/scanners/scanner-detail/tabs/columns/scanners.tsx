import type { ColumnDef } from "@tanstack/react-table";
import { MdCheckBox, MdOutlineRemoveRedEye } from "react-icons/md";

import type { Scanner } from "@/lib/apis/scanners";

export const generateColumns = (
  onSelectScanner: (scanner: Scanner) => void,
): ColumnDef<Scanner>[] => {
  return [
    {
      header: "Display Name",
      accessorKey: "displayName",
    },
    {
      header: "Model",
      accessorKey: "model.modelName",
    },
    {
      header: "Current Version",
      accessorKey: "currentVersion.versionNumber",
    },
    {
      header: "Target Version",
      accessorKey: "targetVersion.versionNumber",
    },
    {
      header: "Status",
      accessorKey: "isActive",
      cell: ({ row }) => {
        if (row.original.isActive) {
          return <MdCheckBox className="size-5 text-green-500" />;
        }
        return <span className="text-red-500">Inactive</span>;
      },
    },
    {
      header: "IP Address",
      accessorKey: "ipAddress",
    },
    {
      header: "Actions",
      accessorKey: "id",
      cell: ({ row }) => {
        return (
          <div
            className="leading-4.5 text-primary-500 flex cursor-pointer items-center gap-1 text-xs font-medium"
            onClick={() => onSelectScanner(row.original)}
          >
            <span className="whitespace-nowrap">View</span>
            <MdOutlineRemoveRedEye />
          </div>
        );
      },
    },
  ];
};
