import type { ColumnDef } from "@tanstack/react-table";
import { MdOutlineEdit } from "react-icons/md";

import type { ScannerApplication } from "@/lib/apis/scanner-applications";

export const generateScannerApplicationColumns = (
  onSelectApplication: (application: ScannerApplication) => void,
): ColumnDef<ScannerApplication>[] => {
  return [
    {
      header: "Version Number",
      accessorKey: "versionNumber",
    },
    {
      header: "Release Date",
      accessorKey: "releaseDate",
    },
    {
      header: "Notes",
      accessorKey: "releaseNotes",
    },
    {
      header: "Actions",
      accessorKey: "id",
      cell: ({ row }) => {
        return (
          <div
            className="leading-4.5 text-primary-500 flex cursor-pointer items-center gap-1 text-xs font-medium"
            onClick={() => onSelectApplication(row.original)}
          >
            <span className="whitespace-nowrap">Edit</span>
            <MdOutlineEdit />
          </div>
        );
      },
    },
  ];
};
