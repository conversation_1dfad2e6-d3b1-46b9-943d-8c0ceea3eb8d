"use client";

import { useParams } from "next/navigation";
import { useState } from "react";
import { RiEditLine } from "react-icons/ri";

import { OverviewCard, OverviewItem } from "@/components/shared/overview-card";
import { PillBadge } from "@/components/ui/badges/pill-badge";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { PageHeader } from "@/components/ui/page-header";
import { formatDate } from "@/lib/utils";

import { ModalEditScanner } from "../default/modal-scanner";
import { useScanner } from "../hooks/use-scanner";
import { ScannerTabs } from "./tabs";

export const ScannerDetailPageContent = () => {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const params = useParams();
  const id = params.id as string;
  const { data: scanner, isLoading: isLoadingScanner } = useScanner(id);

  const breadcrumbItems = [
    { label: "Scanners", href: "/scanners" },
    {
      label: scanner?.displayName ?? "Scanner Detail",
      loading: isLoadingScanner,
    },
  ];

  if (isLoadingScanner) {
    return null;
  }
  return (
    <>
      <div className="flex flex-col gap-4 ">
        <Breadcrumb items={breadcrumbItems} />
        <div className="flex justify-between gap-x-2">
          <PageHeader showBackButton href="/scanners">
            {scanner?.displayName}
          </PageHeader>

          <Button
            className="flex-shrink-0"
            variant="primary"
            onClick={() => setIsEditModalOpen(true)}
          >
            <RiEditLine />
            Edit Scanner
          </Button>
        </div>
        <OverviewCard title="Overview">
          <div className="grid grid-cols-2 gap-4">
            <OverviewItem label="Name" value={scanner?.displayName ?? ""} />
            <OverviewItem label="Active Status">
              <PillBadge variant={scanner?.isActive ? "success" : "default"}>
                {scanner?.isActive ? "Active" : "Inactive"}
              </PillBadge>
            </OverviewItem>
            <OverviewItem
              label="Description"
              value={scanner?.description ?? ""}
            />
            <OverviewItem
              label="Created Date"
              value={
                scanner?.createdDate
                  ? formatDate(scanner?.createdDate, "MMM dd, yyyy")
                  : ""
              }
            />
          </div>
        </OverviewCard>
        <ScannerTabs scanner={scanner} />
      </div>

      <ModalEditScanner
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        scanner={scanner}
      />
    </>
  );
};
