"use client";

import { Card } from "flowbite-react";
import { useMemo, useState } from "react";

import { HeaderActions } from "@/components/shared/header-actions";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { PageHeader } from "@/components/ui/page-header";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import type { Scanner } from "@/lib/apis/scanners";

import { useScanners } from "../hooks/use-scanners";
import { columns } from "./columns";
import { ModalAddScanner } from "./modal-scanner";

const BREADCRUMB_ITEMS = [{ label: "Scanners" }];

const ScannerPageContent = () => {
  const { data, isLoading } = useScanners();
  const [isAddScannerModalOpen, setIsAddScannerModalOpen] = useState(false);

  const exportCSVData = useMemo(() => {
    if (!data || !data.results) return [];

    const headers = [
      { label: "ID", key: "id" },
      { label: "Name", key: "name" },
      { label: "Status", key: "status" },
      { label: "Current Version", key: "currentVersion.versionNumber" },
      { label: "Description", key: "description" },
    ];

    return [
      headers.map((header) => header.label),
      ...data.results.map((scanner) => [
        ...headers.slice(0, -2).map((header) => {
          if (header.key === "status") {
            return scanner.isActive ? "Active" : "Inactive";
          }
          return scanner[header.key as keyof Scanner];
        }),
        scanner.displayName,
        scanner.ipAddress,
        scanner.description,
        scanner.currentVersion?.versionNumber,
      ]),
    ];
  }, [data]);

  return (
    <>
      <div className="space-y-4">
        <Breadcrumb items={BREADCRUMB_ITEMS} />
        <PageHeader>Scanners</PageHeader>
        <Card className="[&>div]:p-0">
          <HeaderActions
            data={exportCSVData}
            buttonText="Add Scanner"
            filename="scanner.csv"
            onButtonClick={() => setIsAddScannerModalOpen(true)}
          />
          {isLoading && <TableLoading columns={columns} />}
          {data && (
            <>
              <Table data={data.results} columns={columns} />
              {data.metadata && (
                <TableDataPagination metadata={data.metadata} />
              )}
            </>
          )}
        </Card>
      </div>
      <ModalAddScanner
        isOpen={isAddScannerModalOpen}
        onClose={() => setIsAddScannerModalOpen(false)}
      />
    </>
  );
};

export default ScannerPageContent;
