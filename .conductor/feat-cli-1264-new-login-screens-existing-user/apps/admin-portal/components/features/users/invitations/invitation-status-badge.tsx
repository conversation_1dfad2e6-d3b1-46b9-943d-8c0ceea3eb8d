"use client";

import type { VariantProps } from "class-variance-authority";
import { cva } from "class-variance-authority";
import { Badge as FlowbiteBadge } from "flowbite-react";

import { cn } from "@/lib/utils";

const badgeVariants = cva("w-fit text-xs font-normal", {
  variants: {
    variant: {
      pending:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-600 dark:text-yellow-200",
      accepted:
        "bg-green-100 text-green-800 dark:bg-green-600 dark:text-green-200",
      expired: "bg-red-100 text-red-800 dark:bg-red-600 dark:text-red-200",
      revoked: "bg-red-100 text-red-800 dark:bg-red-600 dark:text-red-200",
    },
  },
  defaultVariants: {
    variant: "pending",
  },
});

type BadgeProps = VariantProps<typeof badgeVariants> & {
  className?: string;
};

export const InvitationStatusBadge = ({
  status,
  className,
}: { status: "pending" | "accepted" | "expired" | "revoked" } & BadgeProps) => {
  return (
    <FlowbiteBadge
      className={cn(
        badgeVariants({ variant: status }),
        className,
        "whitespace-nowrap capitalize",
      )}
    >
      {status}
    </FlowbiteBadge>
  );
};
