"use client";
import type { ColumnDef, Row } from "@tanstack/react-table";
import { RotateCcw } from "lucide-react";
import Link from "next/link";
import toast from "react-hot-toast";
import { MdBlock } from "react-icons/md";

import {
  TableGenericButton,
  TableViewButton,
} from "@/components/shared/table-action-buttons";
import { PillBadge } from "@/components/ui/badges/pill-badge";
import type { User } from "@/lib/apis/users/types";
import { formatDate } from "@/lib/utils";

export const generateUsersColumns = ({
  onToggleStatus,
}: {
  onToggleStatus: (id: string, isActive: boolean) => void;
}) => {
  const columns: ColumnDef<User>[] = [
    {
      header: "Name",
      accessorKey: "firstName",
      cell: ({ row }) => {
        return (
          <Link href={`/users/${row.original.id}`}>
            <div className="text-primary-500 cursor-pointer whitespace-nowrap underline-offset-4 hover:underline">
              {`${row.original.firstName} ${row.original.lastName}`}
            </div>
          </Link>
        );
      },
    },
    {
      header: "Email",
      accessorKey: "email",
    },
    {
      header: "Phone",
      accessorKey: "phone",
    },
    {
      header: "Active",
      accessorKey: "isActive",
      cell: ({ row }) => {
        if (row.original.isActive) {
          return <PillBadge variant="success">Active</PillBadge>;
        }
        return <PillBadge variant="default">Inactive</PillBadge>;
      },
    },
    {
      header: "Last Login",
      accessorKey: "lastLogin",
      cell: ({ row }) => {
        if (row.original.lastLogin) {
          return (
            <span className="whitespace-nowrap">
              {formatDate(row.original.lastLogin)}
            </span>
          );
        }
        return null;
      },
    },
    {
      header: "Action",
      accessorKey: "id",
      cell: function Cell({ row }: { row: Row<User> }) {
        return (
          <div className="flex items-center gap-4">
            <TableViewButton type="link" href={`/users/${row.original.id}`} />

            {row.original.isActive ? (
              <TableGenericButton
                type="button"
                className="text-red-500 hover:text-red-700"
                onClick={() => {
                  onToggleStatus(row.original.id, false);
                }}
              >
                Disable
                <MdBlock />
              </TableGenericButton>
            ) : (
              <TableGenericButton
                type="button"
                className="text-green-500 hover:text-green-700"
                onClick={async () => {
                  onToggleStatus(row.original.id, true);
                }}
              >
                Enable
                <RotateCcw size={12} />
              </TableGenericButton>
            )}
          </div>
        );
      },
      enableSorting: false,
    },
  ];

  return columns;
};
