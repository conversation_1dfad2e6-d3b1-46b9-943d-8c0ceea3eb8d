import { skipToken, useQuery } from "@tanstack/react-query";
import { parseAsString, useQueryState } from "nuqs";

import { usePagination } from "@/hooks/use-pagination";
import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

export const enrollmentByUserKeys = {
  all: () => ["user-enrollments"] as const,

  allLists: (userId: string) => [...enrollmentByUserKeys.all(), "list", userId],
  list: (userId: string, params?: MetadataParams) => [
    ...enrollmentByUserKeys.allLists(userId),
    params,
  ],

  allChapterProgressListsByEnrollmentId: (id: string) =>
    [...enrollmentByUserKeys.all(), "chapter-progress", id] as const,

  chapterProgressListByEnrollmentId: (id: string, param?: MetadataParams) => [
    ...enrollmentByUserKeys.allChapterProgressListsByEnrollmentId(id),
    param,
  ],
};

export const useEnrollmentsByUserId = (id: string) => {
  const { take, page } = usePagination();
  const [status] = useQueryState("status", parseAsString);

  const params = {
    take,
    page,
    filter: {
      status: status || undefined,
    },
  };
  return useQuery({
    queryKey: enrollmentByUserKeys.list(id, params),
    queryFn: () => api.trainingModules.getEnrollmentByUserIdd(id, params),
    placeholderData: (prev) => prev,
  });
};

export const useChapterProgressesByEnrollmentId = (id?: string) => {
  return useQuery({
    queryKey: id
      ? enrollmentByUserKeys.chapterProgressListByEnrollmentId(id)
      : [],
    queryFn: id
      ? () => api.trainingModules.getChapterProgressesByEnrollmentId(id)
      : skipToken,
  });
};
