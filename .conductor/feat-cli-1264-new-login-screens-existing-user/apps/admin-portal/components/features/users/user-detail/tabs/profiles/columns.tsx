import type { ColumnDef } from "@tanstack/react-table";
import Link from "next/link";
import { FiX } from "react-icons/fi";
import { GrStatusGood } from "react-icons/gr";
import { IoMdCheckmark } from "react-icons/io";
import { IoDocumentOutline } from "react-icons/io5";
import { MdBlock } from "react-icons/md";

import {
  TableEditButton,
  TableRemoveButton,
} from "@/components/shared/table-action-buttons";
import type { GroupStudy, Profile } from "@/lib/apis/groups/types";
import { AccessibleStudy } from "@/lib/apis/users/types";
import { cn } from "@/lib/utils";
import { capitalize } from "@/utils/string";

export const generateUserProfilesColumns = (
  disableProfile: (profileId: string, isActive: boolean) => Promise<void>,
  onOpenStudiesModal: (profile: Profile) => void,
  onEditProfile: (profile: Profile) => void,
) => {
  const columns: ColumnDef<Profile>[] = [
    {
      accessorKey: "name",
      header: "Name",
    },
    {
      header: "Type",
      cell: ({ row }) => {
        return (
          <span className="uppercase">{row.original.currentGroup.type}</span>
        );
      },
    },
    {
      accessorKey: "currentGroup.name",
      header: "Group",
      cell: ({ row }) => {
        const data = row.original;
        return (
          <Link
            className="text-primary-500 cursor-pointer whitespace-nowrap underline-offset-4 hover:underline"
            href={`/settings/groups/${data.currentGroupId}`}
          >
            {data.currentGroup.name}
          </Link>
        );
      },
    },
    {
      header: "Role",
      cell: ({ row }) => {
        const data = row.original;
        return `${data.rolesProfiles[0].role.name} (${capitalize(data.rolesProfiles[0].role.type)})`;
      },
    },
    {
      accessorKey: "currentGroup.site.name",
      header: "Site",
    },
    {
      header: "Custom Permissions",
      cell: ({ row }) => {
        const value = `${row.original.hasCustomPermissions}`;
        return customPermissionColumn[
          value as keyof typeof customPermissionColumn
        ];
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-2">
            <button
              className={cn(
                "flex items-center gap-1",
                row.original.isActive
                  ? "text-red-500 hover:text-red-600"
                  : "text-green-500 hover:text-green-600",
              )}
              onClick={() => {
                disableProfile(row.original.id, !row.original.isActive);
              }}
            >
              {row.original.isActive ? "Disable" : "Enable"}
              {row.original.isActive ? <MdBlock /> : <GrStatusGood />}
            </button>
            <TableEditButton
              type="button"
              onClick={() => onEditProfile(row.original)}
            />
            <button
              onClick={() => onOpenStudiesModal(row.original)}
              className="text-primary-500 hover:text-primary-600 flex items-center gap-x-1"
            >
              Studies <IoDocumentOutline className="size-4" />
            </button>
          </div>
        );
      },
    },
  ];

  return columns;
};

export const generateDefaultPermissionColumns = () => {
  const columns: ColumnDef<GroupStudy>[] = [
    {
      accessorKey: "site.name",
      header: "Site",
    },
    {
      accessorKey: "study.name",
      header: "Study",
    },
  ];

  return columns;
};

export const customPermissionColumn = {
  true: (
    <div className="grid w-fit place-content-center rounded-md bg-green-400 p-1 dark:bg-green-500">
      <IoMdCheckmark color="#fff" />
    </div>
  ),
  false: (
    <div className="grid w-fit place-content-center rounded-md bg-red-400 p-1 dark:bg-red-500">
      <FiX color="#fff" />
    </div>
  ),
};

export const generateActivePermissionColumns = (
  onRemove: (studyId: string) => void,
) => {
  const columns: ColumnDef<AccessibleStudy>[] = [
    {
      accessorKey: "name",
      header: "Name",
    },
    {
      accessorKey: "studyCode",
      header: "Study Code",
    },
    {
      id: "actions",
      cell: ({ row }) => {
        return (
          <div className="flex gap-2">
            <TableRemoveButton
              onClick={() => {
                onRemove(row.original.id);
              }}
            />
          </div>
        );
      },
    },
  ];

  return columns;
};
