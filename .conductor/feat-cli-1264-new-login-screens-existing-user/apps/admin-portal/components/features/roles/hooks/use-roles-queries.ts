import { useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import { roles } from "@/lib/apis/roles";
import { MetadataParams } from "@/lib/apis/types";
import { MILLISECONDS_IN_MINUTE } from "@/lib/constants";

import { useFilterRole } from "./use-filter-role";

export const roleKeys = {
  all: () => ["roles"] as const,

  allRoleLists: () => [...roleKeys.all(), "list"] as const,
  roleList: (params: MetadataParams) =>
    [...roleKeys.allRoleLists(), params] as const,

  allRoleDetails: () => [...roleKeys.all(), "details"] as const,
  roleDetail: (id: string) => [...roleKeys.allRoleDetails(), id] as const,

  allSubjectLists: () => [...roleKeys.all(), "subjects"] as const,
  subjectList: (params: MetadataParams) =>
    [...roleKeys.allSubjectLists(), params] as const,

  allPermissionLists: () => [...roleKeys.all(), "permissions"] as const,
  permissionList: (params: MetadataParams) =>
    [...roleKeys.allPermissionLists(), params] as const,
};

export const useRoles = () => {
  const { page, take, isActive, name, type } = useFilterRole();
  const params = {
    page,
    take: take || 10,
    filter: {
      name,
      type,
      isActive,
    },
  };
  return useQuery({
    queryKey: roleKeys.roleList(params),
    queryFn: () => roles.getRoles(params),
    placeholderData: (prev) => prev,
  });
};

export const useRole = (id: string) => {
  return useQuery({
    queryKey: roleKeys.roleDetail(id),
    queryFn: () => roles.getRole(id),
  });
};

export const useSubjects = () => {
  const { take, page } = usePagination();
  const params = {
    page,
    take: take || 100,
  };
  return useQuery({
    queryKey: roleKeys.subjectList(params),
    queryFn: () => roles.getSubjects(params),
    staleTime: MILLISECONDS_IN_MINUTE * 5, // set staleTime to 5 mins
    placeholderData: (prev) => prev,
  });
};

export const usePermissions = () => {
  // const { page,take } = usePagination();
  const params = {
    take: 1000,
  };
  return useQuery({
    queryKey: roleKeys.permissionList(params),
    queryFn: () => roles.getPermissions(params),
    staleTime: MILLISECONDS_IN_MINUTE * 3, // set staleTime to 3 mins
  });
};
