import { parseAsBoolean, parseAsString, useQueryState } from "nuqs";

import { usePagination } from "@/hooks/use-pagination";

export const useFilterRole = () => {
  const { page, take, goToPage } = usePagination();
  const [name, setName] = useQueryState("name", parseAsString);

  const [type, setType] = useQueryState("type", parseAsString);
  const [isActive, setIsActive] = useQueryState("active", parseAsBoolean);
  return {
    page,
    take,
    name,
    type,
    setName,
    setType,
    isActive,
    setIsActive,
    goToPage,
  };
};
