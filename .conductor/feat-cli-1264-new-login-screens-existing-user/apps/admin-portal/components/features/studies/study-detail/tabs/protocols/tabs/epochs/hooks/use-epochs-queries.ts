import { useInfiniteQuery, useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import api from "@/lib/apis";
import type { MetadataParams } from "@/lib/apis/types";

export const epochKeys = {
  all: () => ["epochs"] as const,
  allLists: (protocolId: string) =>
    [...epochKeys.all(), protocolId, "list"] as const,
  list: (protocolId: string, params?: MetadataParams) =>
    [...epochKeys.allLists(protocolId), params] as const,
};

export const useProtocolEpochs = (protocolId: string) => {
  const { page, take } = usePagination();

  const params = {
    page,
    take: take || 100,
  };

  return useQuery({
    queryKey: epochKeys.list(protocolId, params),
    queryFn: () => api.protocols.getProtocolEpochs(protocolId, params),
    enabled: !!protocolId,
    placeholderData: (prevData) => prevData,
  });
};

export const useInfiniteEpochs = (
  search: string,
  protocolId: string,
  initialPageSize = 10,
) => {
  return useInfiniteQuery({
    queryKey: ["infinite-epochs", protocolId, search],
    queryFn: ({ pageParam = 1 }) =>
      api.protocols.getProtocolEpochs(protocolId, {
        page: pageParam,
        take: initialPageSize,
        filter: { name: search, isActive: true },
      }),
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};
