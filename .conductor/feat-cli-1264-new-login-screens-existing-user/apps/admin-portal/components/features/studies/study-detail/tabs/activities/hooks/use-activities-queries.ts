import { useMutation, useQuery } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import { ReorderActivityPayload } from "@/lib/apis/activities";
import { MetadataParams } from "@/lib/apis/types";
import { TAKE_ALL } from "@/lib/constants";

export const studyActivityKeys = {
  all: () => ["study-activities"] as const,

  allList: (studyId: string) => [...studyActivityKeys.all(), studyId] as const,
  list: (studyId: string, params?: MetadataParams) =>
    [...studyActivityKeys.allList(studyId), params] as const,
};

export const useActivities = (id: string) => {
  return useQuery({
    queryKey: studyActivityKeys.list(id, {
      take: TAKE_ALL,
    }),
    queryFn: () =>
      api.activities.list(id, {
        take: TAKE_ALL,
      }),
    placeholderData: (prevData) => prevData,
  });
};

export const useReorderActivities = () => {
  return useMutation({
    mutationFn: (payload: ReorderActivityPayload) =>
      api.activities.reorderActivity(payload),
    onSettled: (_, err) => {
      !err && toast.success("Activities reordered successfully");
    },
    onError: (error) => {
      toast.error(error.message ?? "Failed to reorder activities");
    },
    meta: {
      // awaits: [USE_ACTIVITIES_QUERY_KEY],
    },
  });
};
