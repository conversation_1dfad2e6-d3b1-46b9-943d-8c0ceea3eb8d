import { z } from "zod";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { LazyMultipleSelect } from "@/components/ui/lazy-select/lazy-multiple-select";
import { Modal } from "@/components/ui/modal";
import { Skeleton } from "@/components/ui/skeleton";

import { useCreateExistingEncounterActivity } from "../../hooks/use-create-activity";
import {
  useEncounterActivities,
  useInfiniteActivities,
} from "../../hooks/use-encounter-activities";

export const schema = z.object({
  activityIds: z
    .array(
      z.object({
        id: z.string(),
      }),
    )
    .min(1, "Activity is required"),
});

type ModalAddActivityProps = {
  encounterId: string;
  isOpen: boolean;
  onClose: () => void;
  protocolId: string;
};

export const ModalAddExistingActivity = function ({
  encounterId,
  isOpen,
  onClose,
  protocolId,
}: ModalAddActivityProps) {
  const { data: activities } = useEncounterActivities(encounterId as string);
  const { mutateAsync: createActivity, isPending: isCreatingActivity } =
    useCreateExistingEncounterActivity(encounterId);
  async function onSubmit(data: z.infer<typeof schema>) {
    await createActivity({
      activityIds: data.activityIds.map((activity) => activity.id),
      protocolId,
    });
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose} className="[&>div]:max-w-2xl">
      <Modal.Header>Add Existing Activity</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={schema}
          onSubmit={onSubmit}
          formProps={{ shouldFocusError: false }}
        >
          <div className="space-y-1">
            <Label htmlFor="activityIds">Activity</Label>
            <LazyMultipleSelect
              name="activityIds"
              id="activityIds"
              searchPlaceholder="Search activities..."
              useInfiniteQuery={useInfiniteActivities}
              getOptionLabel={(activity) => activity.name}
              getOptionValue={(activity) => activity.id}
              placeholder="Select activity"
              mapData={(options) => {
                return options.filter(
                  (opt) =>
                    !activities?.results.some((act) => act.id === opt.id),
                );
              }}
            />
          </div>
          <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
            <CloseButton onClose={onClose} />
            <Button
              type="submit"
              variant="primary"
              isLoading={isCreatingActivity}
            >
              Add Activity
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};

export const ActivityFormSkeleton = () => {
  return (
    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
      {/* Name Field */}
      <div className="col-span-1 flex flex-col gap-2">
        <Skeleton className="h-5 w-16" />
        <Skeleton className="h-10 w-full" />
      </div>

      {/* Group ID Field */}
      <div className="col-span-1 flex flex-col gap-2">
        <Skeleton className="h-5 w-24" />
        <Skeleton className="h-10 w-full" />
      </div>

      {/* Description Field - spans 2 columns */}
      <div className="col-span-1 flex flex-col gap-2 sm:col-span-2">
        <Skeleton className="h-5 w-24" />
        <Skeleton className="h-24 w-full" />
      </div>

      {/* Is Active Field */}
      <div className="col-span-1 flex items-center gap-2">
        <Skeleton className="h-5 w-5" /> {/* Checkbox */}
        <Skeleton className="h-5 w-28" /> {/* Label */}
      </div>
    </div>
  );
};
