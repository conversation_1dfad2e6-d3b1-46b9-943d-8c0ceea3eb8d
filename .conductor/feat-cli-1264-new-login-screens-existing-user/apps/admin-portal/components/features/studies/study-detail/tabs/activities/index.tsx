import { DragEndEvent } from "@dnd-kit/core";
import { arrayMove } from "@dnd-kit/sortable";
import { Card } from "flowbite-react/components/Card";
import { get } from "lodash";
import { useParams } from "next/navigation";
import { useMemo, useState } from "react";
import { CSVLink } from "react-csv";
import { CiExport, CiImport } from "react-icons/ci";
import { IoMdAdd } from "react-icons/io";

import { Button } from "@/components/ui/button";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table } from "@/components/ui/table";
import { TableLoading } from "@/components/ui/table/table-loading";
import { useDisclosure } from "@/hooks/use-disclosure";
import { Activity } from "@/lib/apis/activities";
import { downloadBlob, generateEmptyCsv } from "@/lib/utils";

import { useStudy } from "../../../hooks/use-studies-queries";
import { getColumns } from "./columns";
import { useReorderActivity } from "./hooks/use-activities-mutations";
import { useActivities } from "./hooks/use-activities-queries";
import { ImportActivitiesModal } from "./import-activities-modal";
import { ModalActivity } from "./modal-activity";
export const ActivitiesTab = () => {
  const studyId = useParams().id as string;
  const { data: study } = useStudy(studyId);
  const { data, isPending } = useActivities(studyId);
  const { mutateAsync: reorderActivities } = useReorderActivity(studyId);

  const [isOpenActivityModal, setIsOpenActivityModal] = useState(false);

  const { close, isOpen, open } = useDisclosure();

  const [selectedActivity, setSelectedActivity] = useState<
    Activity | undefined
  >();
  const exportCSVData = useMemo(() => {
    if (!data?.results) return [];

    const headers = [
      { label: "ID", key: "id" },
      { label: "Name", key: "name" },
      { label: "Description", key: "description" },
      { label: "Study Name", key: "study.name" },
      { label: "Study Id", key: "study.id" },
      { label: "Active", key: "isActive" },
      { label: "Created at", key: "createdDate" },
      { label: "Last updated at", key: "lastUpdatedDate" },
    ];

    return [
      headers.map((header) => header.label),
      ...data.results.map((activity) =>
        headers.map((header) => get(activity, header.key)),
      ),
    ];
  }, [data]);

  const onEdit = (item: Activity) => {
    setSelectedActivity(item);
    setIsOpenActivityModal(true);
  };

  const columns = getColumns(onEdit);

  const onCloseActivityModal = () => {
    setSelectedActivity(undefined);
    setIsOpenActivityModal(false);
  };

  const handleDownloadTemplate = () => {
    const csv = generateEmptyCsv(["Name", "Description"]);
    downloadBlob(new Blob([csv]), "import-template.csv");
  };

  const handleDragEnd = (event: DragEndEvent) => {
    if (!event.over || event.active.id === event.over.id) return;
    const activeIndex = data?.results.findIndex(
      (item) => item.id === event.active.id,
    );
    const overIndex = data?.results.findIndex(
      (item) => item.id === event.over?.id,
    );
    const newOrderedActivity = arrayMove(
      data?.results ?? [],
      activeIndex ?? 0,
      overIndex ?? 0,
    );

    reorderActivities({
      studyId,
      newOrderedActivity,
      orderedIds: newOrderedActivity.map((item) => item.id),
    });
  };

  return (
    <>
      <Card className="[&>div]:p-0">
        <div className="mb-4 flex flex-col justify-between gap-4 p-4 pb-0 text-lg font-semibold xl:flex-row xl:items-center">
          <div className="dark:text-gray-400">Activities</div>
          <div className="flex flex-1 flex-col gap-2 sm:flex-row sm:items-center sm:justify-end">
            <Button variant="primary" onClick={handleDownloadTemplate}>
              <CiImport size={18} />
              Download Import Template
            </Button>
            <CSVLink
              data={exportCSVData}
              filename={
                study?.name ? `${study.name}-activities.csv` : "activities.csv"
              }
            >
              <Button className="w-full" variant="primary">
                <CiExport size={18} />
                Export
              </Button>
            </CSVLink>
            <Button variant="primary" onClick={open}>
              <CiImport size={18} />
              Import
            </Button>
            <Button
              variant="primary"
              onClick={() => setIsOpenActivityModal(true)}
            >
              <IoMdAdd size={18} />
              Add Activity
            </Button>
          </div>
        </div>
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <>
            <Table
              draggable={{
                enable: true,
                handleDragEnd,
              }}
              columns={columns}
              data={data?.results ?? []}
            />
            {data?.metadata && <TableDataPagination metadata={data.metadata} />}
          </>
        )}
      </Card>

      {isOpenActivityModal && (
        <ModalActivity
          isOpen={isOpenActivityModal}
          onClose={onCloseActivityModal}
          activity={selectedActivity}
        />
      )}
      {isOpen && <ImportActivitiesModal isOpen={isOpen} onClose={close} />}
    </>
  );
};
