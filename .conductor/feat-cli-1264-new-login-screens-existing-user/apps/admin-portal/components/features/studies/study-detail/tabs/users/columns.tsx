import { ColumnDef } from "@tanstack/react-table";
import Link from "next/link";

import { PillBadge } from "@/components/ui/badges/pill-badge";
import { User } from "@/lib/apis/users/types";

export const userColumns: ColumnDef<User>[] = [
  {
    header: "First Name",
    cell: ({ row }) => {
      return (
        <Link
          href={`/users/${row.original.id}`}
          className="text-primary-500 cursor-pointer whitespace-nowrap underline-offset-4 hover:underline"
        >
          {row.original.firstName}
        </Link>
      );
    },
  },
  {
    header: "First Name",
    cell: ({ row }) => {
      return (
        <Link
          href={`/users/${row.original.id}`}
          className="text-primary-500 cursor-pointer whitespace-nowrap underline-offset-4 hover:underline"
        >
          {row.original.lastName}
        </Link>
      );
    },
  },

  {
    header: "Active",
    accessorKey: "isActive",
    cell: ({ row }) => {
      if (row.original.isActive) {
        return <PillBadge variant="success">Active</PillBadge>;
      }
      return <PillBadge variant="default">Inactive</PillBadge>;
    },
  },
];
