import { skipToken, useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import api from "@/lib/apis";
import type { MetadataParams } from "@/lib/apis/types";

export const protocolKeys = {
  all: () => ["protocols"] as const,
  allLists: (studyId: string) =>
    [...protocolKeys.all(), studyId, "list"] as const,
  list: (studyId: string, params?: MetadataParams) =>
    [...protocolKeys.allLists(studyId), params] as const,

  allDetails: () => ["all-protocol-details"] as const,
  detail: (protocolId: string) =>
    [...protocolKeys.allDetails(), protocolId] as const,
};

export const useStudyProtocols = (studyId: string) => {
  const { page, take } = usePagination();

  const params = {
    page,
    take: take || 100,
  };

  return useQuery({
    queryKey: protocolKeys.list(studyId, params),
    queryFn: () => api.studies.getProtocols(studyId, params),
    enabled: !!studyId,
    placeholderData: (prevData) => prevData,
  });
};

export const useProtocol = (protocolId?: string) => {
  return useQuery({
    queryKey: protocolId ? protocolKeys.detail(protocolId) : [],
    queryFn: protocolId ? () => api.protocols.get(protocolId) : skipToken,
  });
};

export const useDownloadedProtocol = (id?: string) => {
  return useQuery({
    queryFn: id ? () => api.protocols.download(id) : skipToken,
    queryKey: ["downloaded-protocol", id],
  });
};
