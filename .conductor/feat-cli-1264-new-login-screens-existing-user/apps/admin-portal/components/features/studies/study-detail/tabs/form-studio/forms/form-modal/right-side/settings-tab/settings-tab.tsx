import { Checkbox } from "flowbite-react";
import { ChevronDown, ChevronRight, Folder, FolderOpen } from "lucide-react";
import React, { useState } from "react";
import { useFormContext } from "react-hook-form";

import { InputField, Textarea } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";

type TreeFolder = {
  id: string;
  name: string;
  children?: TreeFolder[];
  formCount?: number;
};

type FolderTreeNodeProps = {
  folder: TreeFolder;
  level: number;
};

const FolderTreeNode = ({ folder, level }: FolderTreeNodeProps) => {
  const { watch, setValue } = useFormContext();
  const folderIds = watch("folderIds") || [];
  const [isExpanded, setIsExpanded] = useState(true);
  const hasChildren = folder.children && folder.children.length > 0;
  const isChecked = folderIds.includes(folder.id);

  const handleToggle = (checked: boolean) => {
    const updatedFolders = checked
      ? [...folderIds, folder.id]
      : folderIds.filter((id: string) => id !== folder.id);

    setValue("folderIds", updatedFolders);
  };

  return (
    <>
      <li className="flex items-center gap-6 border-b px-4 py-2 last:border-b-0 hover:bg-gray-50 sm:px-6 dark:hover:bg-gray-800">
        <Checkbox
          id={folder.id}
          checked={isChecked}
          onChange={(e) => {
            handleToggle(e.target.checked);
          }}
        />
        <div
          className="flex flex-1 items-center justify-between"
          style={{ paddingLeft: `${level * 20}px` }}
        >
          <Label htmlFor={folder.id} className="flex items-center gap-2.5">
            {hasChildren ? (
              isExpanded ? (
                <FolderOpen className="size-5 text-gray-600 dark:text-gray-400" />
              ) : (
                <Folder className="size-5 text-gray-600 dark:text-gray-400" />
              )
            ) : (
              <Folder className="size-5 text-gray-600 dark:text-gray-400" />
            )}

            <span className="select-none text-sm font-medium text-gray-700 dark:text-gray-300">
              {folder.name}
            </span>

            {typeof folder.formCount === "number" && (
              <span className="ml-2 flex h-5 w-5 items-center justify-center rounded-full bg-blue-100 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                {folder.formCount}
              </span>
            )}
          </Label>
          {hasChildren && (
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="w-fit rounded p-1 hover:bg-gray-200 dark:hover:bg-gray-700"
            >
              {isExpanded ? (
                <ChevronDown className="size-4 text-gray-600 dark:text-gray-400" />
              ) : (
                <ChevronRight className="size-4 text-gray-600 dark:text-gray-400" />
              )}
            </button>
          )}
        </div>
      </li>

      {hasChildren &&
        isExpanded &&
        folder.children!.map((child) => (
          <FolderTreeNode key={child.id} folder={child} level={level + 1} />
        ))}
    </>
  );
};

export const SettingsTab = () => {
  const { watch, setValue } = useFormContext();
  const folderIds = watch("folderIds") || [];

  const mockFolders: TreeFolder[] = [
    {
      id: "1",
      name: "Medical Forms",
      formCount: 12,
      children: [
        {
          id: "1-1",
          name: "Patient Intake",
          formCount: 5,
          children: [
            { id: "1-1-1", name: "Demographics", formCount: 2 },
            { id: "1-1-2", name: "Medical History", formCount: 3 },
          ],
        },
        {
          id: "1-2",
          name: "Assessments",
          formCount: 7,
          children: [
            { id: "1-2-1", name: "Physical Exam", formCount: 4 },
            { id: "1-2-2", name: "Lab Results", formCount: 3 },
          ],
        },
      ],
    },
    {
      id: "2",
      name: "Administrative",
      formCount: 6,
      children: [
        { id: "2-1", name: "Consent Forms", formCount: 4 },
        { id: "2-2", name: "Insurance", formCount: 2 },
      ],
    },
    {
      id: "3",
      name: "Research",
      formCount: 8,
      children: [
        { id: "3-1", name: "Clinical Trials", formCount: 5 },
        { id: "3-2", name: "Surveys", formCount: 3 },
      ],
    },
  ];

  // Get all folder IDs (including nested ones)
  const getAllFolderIds = (folders: TreeFolder[]): string[] => {
    const ids: string[] = [];
    folders.forEach((folder) => {
      ids.push(folder.id);
      if (folder.children) {
        ids.push(...getAllFolderIds(folder.children));
      }
    });
    return ids;
  };

  const allFolderIds = getAllFolderIds(mockFolders);
  const isAllSelected =
    allFolderIds.length > 0 &&
    allFolderIds.every((id) => folderIds.includes(id));

  const handleToggleAll = (checked: boolean) => {
    if (checked) {
      setValue("folderIds", allFolderIds);
    } else {
      setValue("folderIds", []);
    }
  };

  return (
    <div className="flex flex-col gap-6 rounded-lg border border-gray-300 bg-white p-4 dark:border-gray-500 dark:bg-gray-900">
      <div className="space-y-5">
        <div className="space-y-1">
          <Label required htmlFor="title">
            Form Name
          </Label>
          <InputField
            name="title"
            id="title"
            placeholder="Enter form title..."
            className="w-full"
          />
        </div>

        <div className="space-y-1">
          <Label htmlFor="description">Description</Label>
          <Textarea
            name="description"
            id="description"
            placeholder="Enter form description..."
          />
        </div>
      </div>

      <div className="flex flex-1 flex-col overflow-hidden rounded-lg border border-gray-200">
        <div className="flex items-center gap-6 border-b px-4 py-3 lg:px-6">
          <Checkbox
            id="folders"
            checked={isAllSelected}
            onChange={(e) => handleToggleAll(e.target.checked)}
          />
          <Label htmlFor="folders">Folders </Label>
        </div>
        <ul className="flex-1 overflow-y-auto 2xl:max-h-80">
          {mockFolders.map((folder) => (
            <FolderTreeNode key={folder.id} folder={folder} level={0} />
          ))}
        </ul>
      </div>
    </div>
  );
};
