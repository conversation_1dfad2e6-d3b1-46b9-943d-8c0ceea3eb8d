import { useQuery } from "@tanstack/react-query";
import { parseAsString } from "nuqs";
import { useQueryState } from "nuqs";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import { useSort } from "@/hooks/use-sort";
import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

export const questionsKeys = {
  all: () => ["questions"] as const,
  allLists: () => [...questionsKeys.all(), "list"] as const,
  list: (params?: MetadataParams) =>
    [...questionsKeys.allLists(), params] as const,
  allDetails: () => [...questionsKeys.all(), "detail"] as const,
  detail: (id: string) => [...questionsKeys.allDetails(), id] as const,
};

export const useQuestions = () => {
  const { page, take } = usePagination();
  const { orderBy, orderDirection } = useSort();
  const { search } = useSearch();
  const [questionType] = useQueryState("questionType", parseAsString);
  const [status] = useQueryState("status", parseAsString);

  const params = {
    page,
    take,
    orderBy: orderBy || undefined,
    orderDirection: orderDirection || undefined,
    filter: {
      search: search || undefined,
      questionType: questionType || undefined,
      status: status || undefined,
    },
  };

  return useQuery({
    queryKey: questionsKeys.list(params),
    queryFn: () => api.questions.list(params),
    placeholderData: (prev) => prev,
  });
};

export const useQuestion = (id: string) => {
  return useQuery({
    queryKey: questionsKeys.detail(id),
    queryFn: () => api.questions.get(id),
    enabled: !!id,
  });
};
