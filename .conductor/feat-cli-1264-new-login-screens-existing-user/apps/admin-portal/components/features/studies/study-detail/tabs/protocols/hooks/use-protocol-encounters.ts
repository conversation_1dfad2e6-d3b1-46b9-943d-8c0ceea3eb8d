import { useQuery } from "@tanstack/react-query";

import api from "@/lib/apis";

export const USE_PROTOCOL_ENCOUNTERS_QUERY_KEY = "protocol-encounters";

/**
 * Custom hook to fetch protocol encounters for a given protocol ID
 * @param id - Protocol ID
 * @returns Query result containing protocol encounters data
 */
export const useProtocolEncounters = (id?: string, enabled = true) => {
  return useQuery({
    queryKey: [USE_PROTOCOL_ENCOUNTERS_QUERY_KEY, id],
    queryFn: () => api.protocols.getProtocolEncounters(id!),
    enabled: !!id && enabled,
  });
};
