import { Tooltip } from "flowbite-react";
import { Database, Minus, Plus } from "lucide-react";
import { useState } from "react";
import {
  Controller,
  useController,
  useFieldArray,
  useFormContext,
} from "react-hook-form";

import { InlineEdit } from "@/components/ui/inline-edit";
import { cn } from "@/lib/utils";

import { FieldSelectionModal } from "./field-selection-modal";
import type { QuestionValues } from "./question-modal";

export const LogQuestionColumns = () => {
  const { control } = useFormContext<QuestionValues>();
  const { fields, append, remove } = useFieldArray({
    control,
    name: "config.columns",
  });
  const [selectedColumnIndex, setSelectedColumnIndex] = useState<number | null>(
    null,
  );

  const isOpenModal = typeof selectedColumnIndex === "number";

  const addColumn = () => {
    append({
      field: undefined,
      description: "",
    });
  };

  const removeColumn = (index: number) => {
    if (fields.length > 1) {
      remove(index);
    }
  };

  const handleFieldSelection = (columnIndex: number) => {
    setSelectedColumnIndex(columnIndex);
  };

  const handleClose = () => {
    setSelectedColumnIndex(null);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <span className="text-sm font-medium">Add Column</span>
        <button
          type="button"
          onClick={() => removeColumn(fields.length - 1)}
          disabled={fields.length <= 1}
          className="rounded p-1 transition-colors hover:bg-gray-100 disabled:opacity-50 disabled:hover:bg-transparent"
        >
          <Minus size={20} />
        </button>
        <span className="font-mono text-sm">{fields.length}</span>
        <button
          type="button"
          onClick={addColumn}
          className="rounded p-1 transition-colors hover:bg-gray-100"
        >
          <Plus size={20} />
        </button>
      </div>

      <div className="grid w-fit max-w-full grid-flow-col overflow-x-auto">
        {fields.map((field, index) => {
          return (
            <div key={`field-${field.id}-${index}`} className=" w-64 border">
              <FieldSelectionButton
                index={index}
                onFieldSelection={() => handleFieldSelection(index)}
              />
              <InlineEditDescription index={index} />
            </div>
          );
        })}
      </div>

      {isOpenModal && (
        <FieldSelectionModal
          isOpen={isOpenModal}
          onClose={handleClose}
          columnIndex={selectedColumnIndex}
        />
      )}
    </div>
  );
};

// Separate component for field selection button
type FieldSelectionButtonProps = {
  index: number;
  onFieldSelection: () => void;
};

const FieldSelectionButton = ({
  index,
  onFieldSelection,
}: FieldSelectionButtonProps) => {
  const { watch } = useFormContext<QuestionValues>();

  const field = watch(`config.columns.${index}.field`);

  return (
    <button
      type="button"
      onClick={onFieldSelection}
      className="flex h-11 w-full flex-1 items-center justify-between border-gray-300 bg-gray-50 p-2.5 transition-colors hover:bg-gray-100"
    >
      <span className="truncate text-sm text-gray-700">
        {field?.displayName || "Select Field"}
      </span>
      <Database size={20} className="text-gray-400" />
    </button>
  );
};

const InlineEditDescription = ({ index }: { index: number }) => {
  const { control } = useFormContext<QuestionValues>();
  const { field } = useController({
    control,
    name: `config.columns.${index}.description`,
  });

  const trimValue = field.value?.trim();

  return (
    <div className={cn("flex h-11 border-t px-4")}>
      <InlineEdit
        placeholder="Add description"
        saveButtonClassName="size-8"
        cancelButtonClassName="size-8"
        inputClassName="h-8"
        value={trimValue}
        className={cn(
          "flex min-w-0 flex-1 items-center",
          !trimValue && "justify-center",
        )}
        viewModeClassName={cn(
          "truncate text-xs",
          !trimValue && "text-gray-300 justify-center",
        )}
        onSave={(value) => {
          field.onChange(value);
        }}
      />
    </div>
  );
};
