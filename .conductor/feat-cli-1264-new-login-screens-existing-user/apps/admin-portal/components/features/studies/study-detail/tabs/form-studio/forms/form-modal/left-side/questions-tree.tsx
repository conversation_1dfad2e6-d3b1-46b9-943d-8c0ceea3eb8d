import { Plus } from "lucide-react";
import React, { useEffect, useState } from "react";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { useDisclosure } from "@/hooks/use-disclosure";

import { FolderModal } from "./folder-modal";
import { TreeNode } from "./tree-node";
import {
  Question,
  QuestionFolder,
  useQuestionFolders,
} from "./use-questions-queries";

type Props = {
  selectedFolderId: string | null;
  onFolderSelect: (folderId: string, path: string) => void;
  draggingFolder: QuestionFolder | null;
  draggingQuestion: Question | null;
  isMovingFolder: boolean;
  isMovingQuestion: boolean;
};

export const QuestionsTree = ({
  selectedFolderId,
  onFolderSelect,
  draggingFolder,
  draggingQuestion,
  isMovingFolder,
  isMovingQuestion,
}: Props) => {
  const {
    data: folders,
    isLoading: isLoadingFolder,
    isSuccess,
  } = useQuestionFolders();

  const {
    isOpen: isFolderModalOpen,
    open: onFolderModalOpen,
    close: onFolderModalClose,
  } = useDisclosure();

  // Auto-select first folder when data loads
  useEffect(() => {
    if (selectedFolderId) return;
    if (isSuccess && folders?.length > 0) {
      onFolderSelect(folders[0].id, `/${folders[0].name}`);
    }
  }, [folders, isSuccess, selectedFolderId, onFolderSelect]);

  return (
    <div className="flex h-full flex-col gap-4">
      <div className="flex h-[46px] items-center justify-center gap-2.5">
        <span className="bg-primary-500 rounded-full px-2 py-1 font-medium text-white">
          350
        </span>
        <span className="text-lg dark:text-white">Questions</span>
      </div>
      <div className="flex w-full flex-1 flex-col overflow-hidden rounded-lg border border-gray-300 pb-2 dark:border-gray-500 [&>div:last-child]:flex-1">
        <div className="p-5 pb-2.5">
          <Button
            className="[&>span]:!px-0"
            variant="outline"
            onClick={onFolderModalOpen}
          >
            <Plus className="size-4" />
            <span className="min-w-0 truncate">New Folder</span>
          </Button>
        </div>

        {isLoadingFolder ? (
          <FolderSkeleton />
        ) : (
          <LoadingWrapper isLoading={isMovingFolder || isMovingQuestion}>
            <div className="overflow-hidden">
              {folders?.map((folder) => (
                <TreeNode
                  key={folder.id}
                  folder={folder}
                  level={0}
                  draggingFolder={draggingFolder}
                  draggingQuestion={draggingQuestion}
                  path={`/${folder.name}`}
                  selectedFolderId={selectedFolderId}
                  onFolderSelect={onFolderSelect}
                  rootPath={`/${folder.name}`}
                />
              ))}
            </div>
          </LoadingWrapper>
        )}
      </div>

      <FolderModal isOpen={isFolderModalOpen} onClose={onFolderModalClose} />
    </div>
  );
};

const FolderSkeleton = () => (
  <div className="flex-1">
    {[1, 2, 3, 4].map((i) => (
      <div key={i} className="py-2 pl-4 pr-3">
        <Skeleton className="h-7 w-full " />
      </div>
    ))}
  </div>
);
