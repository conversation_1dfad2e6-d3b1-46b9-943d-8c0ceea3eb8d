import { ColumnDef } from "@tanstack/react-table";
import { Md<PERSON>rch<PERSON>, Md<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, MdPublish } from "react-icons/md";

import {
  TableEditButton,
  TableGenericButton,
  TableRemoveButton,
} from "@/components/shared/table-action-buttons";
import { FormStudioStatusBadge } from "@/components/ui/badges/form-studio-status-badge";
import {
  Dropdown,
  DropdownContent,
  DropdownTrigger,
} from "@/components/ui/dropdown";
import { Codelist } from "@/lib/apis/codelist-definitions/types";

type CodelistActions = {
  onEdit: (codelist: Codelist) => void;
  onCopy: (codelist: Codelist) => void;
  onPublish: (codelist: Codelist) => void;
  onArchive: (codelist: Codelist) => void;
  onDelete: (codelist: Codelist) => void;
};

export const generateCodelistColumns = (
  actions: CodelistActions,
): ColumnDef<Codelist>[] => [
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => (
      <button
        onClick={() => actions.onEdit(row.original)}
        className="text-primary-500 text-left font-medium hover:underline"
      >
        {row.original.name}
      </button>
    ),
  },
  {
    accessorKey: "label",
    header: "Label",
    cell: ({ row }) => <span className="text-sm">{row.original.label}</span>,
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => <FormStudioStatusBadge variant={row.original.status} />,
  },
  {
    accessorKey: "description",
    header: "Description",
    cell: ({ row }) => (
      <span className="text-sm text-gray-600">
        {row.original.description || "-"}
      </span>
    ),
  },
  {
    id: "terms",
    header: "Terms",
    cell: ({ row }) => <TermsCell terms={row.original.terms} />,
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const codelist = row.original;
      const canPublish = codelist.status === "DRAFT";
      const canArchive = codelist.status !== "ARCHIVED";
      const canDelete = codelist.status === "DRAFT";

      return (
        <div className="flex items-center gap-2">
          <TableEditButton
            type="button"
            onClick={() => actions.onEdit(codelist)}
          />

          <TableGenericButton
            type="button"
            onClick={() => actions.onCopy(codelist)}
          >
            Copy
            <MdContentCopy className="h-4 w-4" />
          </TableGenericButton>

          {canPublish && (
            <TableGenericButton
              type="button"
              onClick={() => actions.onPublish(codelist)}
            >
              Publish
              <MdPublish className="h-4 w-4" />
            </TableGenericButton>
          )}

          {canArchive && (
            <TableGenericButton
              type="button"
              className="text-red-500 hover:text-red-600"
              onClick={() => actions.onArchive(codelist)}
            >
              Archive
              <MdArchive className="h-4 w-4" />
            </TableGenericButton>
          )}

          {canDelete && (
            <TableRemoveButton
              onClick={() => actions.onDelete(codelist)}
              label="Delete"
            />
          )}
        </div>
      );
    },
  },
];

const TermsCell = ({ terms }: { terms: Codelist["terms"] }) => {
  if (terms.length === 0) {
    return <span className="text-sm text-gray-500">No terms</span>;
  }

  const termsContent = (
    <div className="rounded-lg border border-gray-200 bg-white p-3 shadow-lg dark:border-gray-600 dark:bg-gray-800">
      <div className="max-h-60 space-y-2 overflow-y-auto">
        {terms.map((term) => (
          <div key={term.id} className="text-sm">
            <div className="flex items-center gap-2">
              <span className="rounded bg-gray-100 px-2 py-1 font-mono text-xs text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                {term.submissionValue}
              </span>
              <span className="text-gray-700 dark:text-gray-300">
                {term.displayLabel}
              </span>
            </div>
            {term.definition && (
              <div className="ml-2 mt-1 text-xs italic text-gray-500 dark:text-gray-400">
                {term.definition}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <Dropdown>
      <DropdownTrigger>
        <div className="group inline-flex cursor-pointer items-center gap-1.5 rounded-full bg-blue-50 px-3 py-1.5 text-sm font-medium text-blue-700 transition-all hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-300 dark:hover:bg-blue-800/40">
          <div className="flex h-5 w-5 items-center justify-center rounded-full bg-blue-500 text-xs font-bold text-white">
            {terms.length}
          </div>
          <span>Terms</span>
          <MdList className="h-3.5 w-3.5 opacity-60 group-hover:opacity-80" />
        </div>
      </DropdownTrigger>
      <DropdownContent className="w-96">{termsContent}</DropdownContent>
    </Dropdown>
  );
};
