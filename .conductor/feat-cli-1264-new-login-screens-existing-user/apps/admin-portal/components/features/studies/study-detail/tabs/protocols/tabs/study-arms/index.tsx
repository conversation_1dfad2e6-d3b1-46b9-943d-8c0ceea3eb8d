import { Card } from "flowbite-react";
import { useMemo, useState } from "react";
import { IoMdAdd } from "react-icons/io";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Button } from "@/components/ui/button";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import { Protocol, ProtocolStudyArm } from "@/lib/apis/protocols/types";

import { generateStudyArmColumns } from "./columns";
import { useProtocolStudyArms } from "./hooks/use-study-arms-queries";
import { StudyArmModal } from "./study-arm-modal";

type Props = {
  selectedProtocol: Protocol | null;
};

export const StudyArmsTab = ({ selectedProtocol }: Props) => {
  const [isOpenStudyArmModal, setIsOpenStudyArmModal] = useState(false);
  const [selectedStudyArm, setSelectedStudyArm] =
    useState<ProtocolStudyArm | null>(null);

  const {
    data: protocolStudyArms,
    isPending,
    isPlaceholderData,
  } = useProtocolStudyArms(selectedProtocol?.id || "");

  const onEdit = (studyArm: ProtocolStudyArm) => {
    setSelectedStudyArm(studyArm);
    setIsOpenStudyArmModal(true);
  };

  const columns = useMemo(
    () =>
      generateStudyArmColumns({
        onEdit,
        isPublished: selectedProtocol?.isPublished,
      }),
    [selectedProtocol?.isPublished],
  );

  const onCloseStudyArmModal = () => {
    setIsOpenStudyArmModal(false);
    setSelectedStudyArm(null);
  };

  return (
    <>
      <Card className="[&>div]:p-0">
        <div className="mb-4 flex items-center justify-between p-4 pb-0 text-lg font-semibold">
          <div className="dark:text-gray-400">
            Study Arms {selectedProtocol && `(${selectedProtocol.name})`}
          </div>
          <Button
            disabled={selectedProtocol?.isPublished}
            variant="primary"
            onClick={() => setIsOpenStudyArmModal(true)}
          >
            <IoMdAdd />
            Add Study Arm
          </Button>
        </div>
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <LoadingWrapper isLoading={isPlaceholderData}>
            <Table columns={columns} data={protocolStudyArms?.results ?? []} />
            {protocolStudyArms?.metadata && (
              <TableDataPagination metadata={protocolStudyArms.metadata} />
            )}
          </LoadingWrapper>
        )}
      </Card>

      {isOpenStudyArmModal && selectedProtocol?.id && (
        <StudyArmModal
          selectedStudyArm={selectedStudyArm}
          isOpen={isOpenStudyArmModal}
          onClose={onCloseStudyArmModal}
          protocolId={selectedProtocol.id}
        />
      )}
    </>
  );
};
