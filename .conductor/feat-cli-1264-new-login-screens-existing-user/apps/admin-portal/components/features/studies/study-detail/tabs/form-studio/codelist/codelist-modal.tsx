import { zod<PERSON><PERSON><PERSON><PERSON> } from "@hookform/resolvers/zod";
import { Minus, Plus, Trash2 } from "lucide-react";
import { useEffect } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { z } from "zod";

import { EditorModal } from "@/components/shared/editor-modal";
import { Form, InputField } from "@/components/ui/form";
import { TextCell } from "@/components/ui/form/edc-cells/text-cell";
import { Label } from "@/components/ui/form/label";
import { Codelist } from "@/lib/apis/codelist-definitions/types";
import { cn } from "@/lib/utils";

import {
  useCreateCodelist,
  useUpdateCodelist,
} from "./hooks/use-codelist-mutations";

const optionSchema = z.object({
  submissionValue: z
    .string({
      required_error: "Value is required",
      invalid_type_error: "Value is required",
    })
    .min(1, "Value is required")
    .regex(
      /^[a-zA-Z0-9_]+$/,
      "Value can only contain letters, numbers, and underscores",
    ),
  displayLabel: z
    .string({
      required_error: "Label is required",
      invalid_type_error: "Label is required",
    })
    .min(1, "Label is required"),
  definition: z.string().optional(),
});

const schema = z
  .object({
    name: z
      .string({
        required_error: "CodeList Name is required",
        invalid_type_error: "CodeList Name is required",
      })
      .min(1, "CodeList Name is required")
      .regex(
        /^[a-zA-Z0-9_]+$/,
        "CodeList Name can only contain letters, numbers, and underscores",
      ),
    label: z
      .string({
        required_error: "Label is required",
        invalid_type_error: "Label is required",
      })
      .min(1, "Label is required"),
    description: z.string().optional(),
    codelistId: z.string().optional(),
    options: z.array(optionSchema).min(1, "At least one option is required"),
    isEditing: z.boolean().optional(),
  })
  .superRefine((data, ctx) => {
    const submissionValueCount = new Map<string, number[]>();

    data.options.forEach((item, idx) => {
      const value = item.submissionValue?.trim();
      if (value) {
        if (!submissionValueCount.has(value)) {
          submissionValueCount.set(value, []);
        }
        submissionValueCount.get(value)!.push(idx);
      }
    });

    submissionValueCount.forEach((indices, value) => {
      if (indices.length > 1) {
        indices.forEach((idx) => {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: `Value must be unique`,
            path: ["options", idx, "submissionValue"],
          });
        });
      }
    });
  });

export type CodelistFormValues = z.infer<typeof schema>;

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedCodelist: Codelist | null;
};

export const CodelistModal = function ({
  isOpen,
  onClose,
  selectedCodelist,
}: Props) {
  const { mutateAsync: createCodelist, isPending: isCreatingCodelist } =
    useCreateCodelist();
  const { mutateAsync: updateCodelist, isPending: isUpdatingCodelist } =
    useUpdateCodelist();

  const isEditing = !!selectedCodelist;
  const isDisabled =
    (selectedCodelist?.status === "PUBLISHED" ||
      selectedCodelist?.status === "ARCHIVED") &&
    isEditing;
  const isSubmitting = isCreatingCodelist || isUpdatingCodelist;

  const formMethods = useForm<CodelistFormValues>({
    resolver: zodResolver(schema),
    mode: "onChange",
    defaultValues: {
      name: selectedCodelist?.name || "",
      label: selectedCodelist?.label || "",
      description: selectedCodelist?.description || "",
      codelistId: selectedCodelist?.codelistId || "",
      options: selectedCodelist?.terms || [
        { submissionValue: "", displayLabel: "", definition: "" },
      ],
      isEditing: isEditing,
    },
    disabled: isDisabled || undefined,
  });
  const { watch } = formMethods;

  const { fields, append, remove } = useFieldArray({
    control: formMethods.control,
    name: "options",
  });

  const isValid = formMethods.formState.isValid;

  const onSubmit = async (data: CodelistFormValues) => {
    const payload = {
      name: data.name,
      label: data.label,
      description: data.description || undefined,
      terms: data.options.map((opt, index) => ({
        submissionValue: opt.submissionValue,
        displayLabel: opt.displayLabel,
        definition: opt.definition,
        displayOrder: index + 1,
      })),
    };
    if (isEditing && selectedCodelist) {
      await updateCodelist({ ...payload, id: selectedCodelist.id });
    } else {
      await createCodelist(payload);
    }
    onClose();
  };

  const addOption = () => {
    append({ submissionValue: "", displayLabel: "", definition: "" });
  };

  const removeOption = (index: number) => {
    if (fields.length > 1) {
      remove(index);
    }
  };
  useEffect(() => {
    const { unsubscribe } = watch((_, { name }) => {
      if (name?.includes("submissionValue")) {
        formMethods.formState.errors.options?.forEach?.((error) => {
          if (!error?.submissionValue) return;
          const submissionError = error?.submissionValue;
          if (submissionError.message?.includes("unique")) {
            formMethods.trigger(
              (submissionError.ref?.name as keyof CodelistFormValues) || "",
            );
          }
        });
      }
    });
    return () => unsubscribe();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <EditorModal
      isOpen={isOpen}
      onClose={onClose}
      title="CodeList Editor"
      formId="codelist-form"
      isValid={isValid && !isDisabled}
      isSubmitting={isSubmitting}
    >
      <Form
        schema={schema}
        formMethods={formMethods}
        onSubmit={onSubmit}
        id="codelist-form"
        className="space-y-4 rounded-xl border border-gray-200 p-5 shadow dark:border-gray-700"
      >
        <div
          className={cn(
            "grid gap-4",
            isEditing ? "grid-cols-4" : "grid-cols-3",
          )}
        >
          <div className="space-y-1">
            <Label htmlFor="name" className="text-gray-900 dark:text-white">
              CodeList Name
            </Label>
            <InputField
              id="name"
              name="name"
              placeholder="Enter codelist name (e.g., gender_codes)..."
            />
          </div>
          <div className="space-y-1">
            <Label htmlFor="label" className="text-gray-900 dark:text-white">
              Label
            </Label>
            <InputField
              id="label"
              name="label"
              placeholder="Enter display label (e.g., Gender Options)..."
            />
          </div>
          <div className="space-y-1">
            <Label
              htmlFor="description"
              className="text-gray-900 dark:text-white"
            >
              Description
            </Label>
            <InputField
              id="description"
              name="description"
              placeholder="Describe what this codelist represents..."
            />
          </div>

          {isEditing && (
            <div className="space-y-1">
              <Label
                required
                htmlFor="codelistId"
                className="text-gray-900 dark:text-white"
              >
                Codelist ID
              </Label>
              <InputField
                id="codelistId"
                name="codelistId"
                placeholder="Enter codelist ID..."
              />
            </div>
          )}
        </div>

        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              Add Option
            </span>
            <button
              type="button"
              onClick={() => removeOption(fields.length - 1)}
              disabled={fields.length <= 1 || isDisabled}
              className="rounded p-1 text-gray-700 transition-colors hover:bg-gray-100 disabled:opacity-50 disabled:hover:bg-transparent dark:text-gray-300 dark:hover:bg-gray-700"
            >
              <Minus size={20} />
            </button>
            <span className="rounded border border-gray-300 bg-gray-50 px-2 py-1 font-mono text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200">
              {fields.length}
            </span>
            <button
              type="button"
              onClick={addOption}
              disabled={isDisabled}
              className="rounded p-1 text-gray-700 transition-colors hover:bg-gray-100 disabled:opacity-50 disabled:hover:bg-transparent dark:text-gray-300 dark:hover:bg-gray-700"
            >
              <Plus size={20} />
            </button>
          </div>

          <div className="">
            <div className="grid grid-cols-[25%_25%_1fr] bg-gray-50 dark:bg-gray-800">
              <div className="border border-gray-300 px-3 py-2.5 text-xs font-medium uppercase tracking-wider text-gray-600 dark:border-gray-600 dark:text-gray-300">
                Label
              </div>
              <div className="border border-gray-300 px-3 py-2.5 text-xs font-medium uppercase tracking-wider text-gray-600 dark:border-gray-600 dark:text-gray-300">
                Value
              </div>
              <div className="border border-gray-300 px-3 py-2.5 text-xs font-medium uppercase tracking-wider text-gray-600 dark:border-gray-600 dark:text-gray-300">
                Description
              </div>
            </div>

            <ul>
              {fields.map((field, index) => (
                <li
                  key={field.id}
                  className="grid grid-cols-[25%_25%_1fr_auto]"
                >
                  <TextCell
                    name={`options.${index}.displayLabel`}
                    placeholderText="Enter label..."
                  />
                  <TextCell
                    name={`options.${index}.submissionValue`}
                    placeholderText="Enter value..."
                  />

                  <TextCell
                    name={`options.${index}.definition`}
                    placeholderText="Enter description..."
                    className="min-w-0"
                  />
                  <div className="flex items-center justify-center border border-gray-300 p-1 dark:border-gray-600">
                    <button
                      type="button"
                      onClick={() => removeOption(index)}
                      disabled={fields.length <= 1 || isDisabled}
                      className="flex h-8 w-8 items-center justify-center rounded-full bg-red-50 text-red-500 transition-colors hover:border-red-300 hover:bg-red-100 hover:text-red-600 disabled:cursor-not-allowed disabled:opacity-30 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-800/40 dark:hover:text-red-300"
                    >
                      <Trash2 className="h-3 w-3" />
                    </button>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </Form>
    </EditorModal>
  );
};
