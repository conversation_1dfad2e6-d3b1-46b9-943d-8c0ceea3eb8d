import { useQuery } from "@tanstack/react-query";

import api from "@/lib/apis";

export const USE_ACTIVITY_PROCEDURES_QUERY_KEY = "activity-procedures";

/**
 * Custom hook to fetch activities for a specific protocol encounter by protocol ID and encounter ID
 * @param id - activity ID
 * @returns Query result containing activity procedure data
 */
export const useActivityProcedures = (id?: string, enabled = true) => {
  return useQuery({
    queryKey: [USE_ACTIVITY_PROCEDURES_QUERY_KEY, id],
    queryFn: () => api.activities.getActivityProcedures(id!),
    enabled: !!id && enabled,
  });
};
