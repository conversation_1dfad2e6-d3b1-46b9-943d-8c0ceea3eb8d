import { skipToken, useQuery } from "@tanstack/react-query";

import api from "@/lib/apis";

export const protocolContentKeys = {
  all: () => ["protocol-content"],

  allDetail: () => [...protocolContentKeys.all(), "detail"] as const,
  detail: (protocolId: string) =>
    [...protocolContentKeys.allDetail(), protocolId] as const,
};

export const USE_PROTOCOL_CONTENT_KEY = "use-protocol-content";

export const useProtocolContent = (protocolId?: string) => {
  return useQuery({
    queryKey: protocolId ? protocolContentKeys.detail(protocolId) : [],
    queryFn: protocolId
      ? () => api.protocols.getContent(protocolId)
      : skipToken,
  });
};
