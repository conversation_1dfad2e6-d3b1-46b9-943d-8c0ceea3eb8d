import type { ColumnDef } from "@tanstack/react-table";
import Link from "next/link";

import { PillBadge } from "@/components/ui/badges/pill-badge";
import type { SiteStudy } from "@/lib/apis/studies";

export const columns: ColumnDef<SiteStudy>[] = [
  {
    header: "Name",
    accessorKey: "site.name",
    cell: ({ row }) => (
      <Link
        className=" text-primary-500 cursor-pointer gap-1 font-medium hover:underline"
        href={`/sites/${row.original.siteId}`}
      >
        {row.original.site.name}
      </Link>
    ),
  },
  {
    header: "Active",
    accessorKey: "isActive",
    cell: ({ row }) => {
      if (row.original.site?.isActive) {
        return <PillBadge variant="success">Active</PillBadge>;
      }
      return <PillBadge variant="default">Inactive</PillBadge>;
    },
  },
];
