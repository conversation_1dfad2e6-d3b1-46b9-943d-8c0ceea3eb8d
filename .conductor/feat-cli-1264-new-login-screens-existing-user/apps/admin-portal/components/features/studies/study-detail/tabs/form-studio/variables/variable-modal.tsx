import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { EditorModal } from "@/components/shared/editor-modal";
import { Form, InputField, Textarea } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";

import { Variable } from "./columns";

const schema = z.object({
  label: z
    .string({
      required_error: "Label is required",
      invalid_type_error: "Label is required",
    })
    .min(1, "Label is required"),
  name: z
    .string({
      required_error: "Name is required",
      invalid_type_error: "Name is required",
    })
    .min(1, "Name is required")
    .regex(
      /^[a-zA-Z0-9_]+$/,
      "Name can only contain letters, numbers, and underscores",
    ),
  value: z
    .string({
      required_error: "Value is required",
      invalid_type_error: "Value is required",
    })
    .min(1, "Value is required"),
  description: z.string().optional(),
});

type FormValues = z.infer<typeof schema>;

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedVariable: Variable | null;
  onSave: (data: FormValues & { id?: string }) => void;
};

export const VariableModal = function ({
  isOpen,
  onClose,
  selectedVariable,
  onSave,
}: Props) {
  const isEditing = !!selectedVariable;

  const formMethods = useForm<FormValues>({
    resolver: zodResolver(schema),
    mode: "onChange",
    defaultValues: {
      label: selectedVariable?.label || "",
      name: selectedVariable?.name || "",
      value: selectedVariable?.value || "",
      description: selectedVariable?.description || "",
    },
  });

  const isValid = formMethods.formState.isValid;

  const onSubmit = async (data: FormValues) => {
    const payload = isEditing ? { ...data, id: selectedVariable.id } : data;

    onSave(payload);
    onClose();
  };

  return (
    <EditorModal
      isOpen={isOpen}
      onClose={onClose}
      title="Variable Editor"
      formId="variable-form"
      isValid={isValid}
    >
      <Form
        schema={schema}
        formMethods={formMethods}
        onSubmit={onSubmit}
        id="variable-form"
        className="space-y-5"
      >
        <div className="space-y-4 rounded-xl border border-gray-200 p-5 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-1">
              <Label htmlFor="label">Label</Label>
              <InputField
                id="label"
                name="label"
                placeholder="Enter label..."
              />
            </div>
            <div className="space-y-1">
              <Label htmlFor="name">Name</Label>
              <InputField id="name" name="name" placeholder="Enter name..." />
            </div>
            <div className="space-y-1">
              <Label htmlFor="value">Value</Label>
              <InputField
                id="value"
                name="value"
                placeholder="Enter value..."
              />
            </div>
          </div>

          <div className="space-y-1">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              placeholder="Enter description..."
            />
          </div>
        </div>
      </Form>
    </EditorModal>
  );
};
