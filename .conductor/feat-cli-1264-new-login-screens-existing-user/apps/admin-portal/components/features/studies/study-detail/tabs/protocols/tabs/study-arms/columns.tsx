import { ColumnDef } from "@tanstack/react-table";

import { TableEditButton } from "@/components/shared/table-action-buttons";
import { PillBadge } from "@/components/ui/badges/pill-badge";
import { ProtocolStudyArm } from "@/lib/apis/protocols/types";

type Params = {
  onEdit: (studyArm: ProtocolStudyArm) => void;
  isPublished?: boolean;
};

export const generateStudyArmColumns = ({
  onEdit,
  isPublished,
}: Params): ColumnDef<ProtocolStudyArm>[] => [
  {
    header: "Name",
    accessorKey: "name",
    cell: ({ row }) => (
      <span
        onClick={() => !isPublished && onEdit(row.original)}
        role="button"
        className={
          isPublished
            ? "text-gray-600"
            : "text-primary-500 cursor-pointer hover:underline"
        }
      >
        {row.getValue("name")}
      </span>
    ),
  },
  {
    header: "Description",
    accessorKey: "description",
  },
  {
    accessorKey: "isActive",
    header: "Active",
    cell: ({ row }) => {
      const isActive = row.getValue("isActive");
      if (isActive) {
        return <PillBadge variant="success">Active</PillBadge>;
      }
      return <PillBadge variant="default">Inactive</PillBadge>;
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      return (
        <div className="flex gap-2">
          <TableEditButton
            type="button"
            onClick={() => onEdit(row.original)}
            disabled={isPublished}
          />
        </div>
      );
    },
  },
];
