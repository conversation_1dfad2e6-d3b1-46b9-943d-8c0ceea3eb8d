import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { CreateActivityProcedurePayload } from "@/lib/apis/activities";

import { USE_ACTIVITY_PROCEDURES_QUERY_KEY } from "./use-activity-procedure";
import { USE_PROTOCOL_ENCOUNTER_ACTIVITIES_QUERY_KEY } from "./use-encounter-activities";

export const useCreateActivityProcedures = (activityId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateActivityProcedurePayload) =>
      api.activities.createActivityProcedures(activityId, data),
    onSuccess: (_, payload) => {
      queryClient.invalidateQueries({
        queryKey: [USE_ACTIVITY_PROCEDURES_QUERY_KEY],
      });
      return queryClient.invalidateQueries({
        queryKey: [
          USE_PROTOCOL_ENCOUNTER_ACTIVITIES_QUERY_KEY,
          payload.encounterId,
        ],
      });
    },
    onSettled: (_, err) =>
      !err && toast.success("Procedure created successfully"),
    onError: (err) => {
      toast.error(err.message);
    },
  });
};
