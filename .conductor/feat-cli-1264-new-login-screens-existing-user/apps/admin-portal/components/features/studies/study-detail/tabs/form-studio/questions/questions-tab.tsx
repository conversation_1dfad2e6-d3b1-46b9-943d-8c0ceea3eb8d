"use client";

import { Card } from "flowbite-react";
import { useMemo, useState } from "react";
import { IoMdAdd } from "react-icons/io";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { SearchField } from "@/components/shared/search-field";
import { Button } from "@/components/ui/button";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import { useDisclosure } from "@/hooks/use-disclosure";
import { Question } from "@/lib/apis/questions/types";

import { generateQuestionColumns } from "./columns";
import { useQuestions } from "./hooks/use-questions-queries";
import { QuestionModal } from "./question-modal";

export const QuestionsTab = () => {
  const { data, isPlaceholderData, isPending } = useQuestions();
  const [selectedQuestion, setSelectedQuestion] = useState<Question | null>(
    null,
  );

  const {
    isOpen: isQuestionModalOpen,
    open: openQuestionModal,
    close: closeQuestionModal,
  } = useDisclosure();

  const handleEdit = (question: Question) => {
    setSelectedQuestion(question);
    openQuestionModal();
  };

  const handleAddNew = () => {
    setSelectedQuestion(null);
    openQuestionModal();
  };

  const handleCloseModal = () => {
    closeQuestionModal();
    setSelectedQuestion(null);
  };

  const columns = useMemo(() => generateQuestionColumns(handleEdit), []);

  return (
    <Card className="border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800 [&>div]:p-0">
      <div className="border-b border-gray-200 p-4 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Questions
          </h3>

          <div className="flex items-center gap-4">
            <SearchField
              queryKey="questionSearch"
              placeholder="Search questions..."
            />
            <Button variant="primary" onClick={handleAddNew}>
              <IoMdAdd className="h-4 w-4" /> New Question
            </Button>
          </div>
        </div>
      </div>

      <div className="p-0">
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <LoadingWrapper isLoading={isPlaceholderData}>
            <Table columns={columns} data={data?.results ?? []} />
            {/* <TableDataPagination metadata={}  /> */}
          </LoadingWrapper>
        )}
      </div>
      {isQuestionModalOpen && (
        <QuestionModal
          isOpen={isQuestionModalOpen}
          mode={selectedQuestion ? "edit" : "create"}
          onClose={handleCloseModal}
          selectedQuestion={selectedQuestion}
        />
      )}
    </Card>
  );
};
