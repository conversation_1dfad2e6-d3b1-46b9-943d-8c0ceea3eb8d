import { useQuery } from "@tanstack/react-query";

import api from "@/lib/apis";

export const studySiteKeys = {
  all: () => ["sites"] as const,
  allLists: (studyId: string) =>
    [...studySiteKeys.all(), studyId, "list"] as const,
  list: (studyId: string) => [...studySiteKeys.allLists(studyId)] as const,
};

export const useStudySites = (studyId?: string) => {
  return useQuery({
    queryKey: studySiteKeys.list(studyId!),
    queryFn: () => api.studies.getSites(studyId!),
    enabled: !!studyId,
  });
};
