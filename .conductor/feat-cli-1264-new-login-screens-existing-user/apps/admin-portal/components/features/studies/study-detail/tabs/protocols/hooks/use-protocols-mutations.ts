import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import { UpdateProtocolPayload } from "@/lib/apis/protocols";
import { AddStudyProtocolPayload } from "@/lib/apis/studies";

import { protocolKeys } from "./use-protocols-queries";

export const useUpdateProtocol = (studyId?: string, protocolId?: string) => {
  return useMutation({
    mutationFn: ({
      protocolId,
      data,
    }: {
      protocolId: string;
      data: UpdateProtocolPayload;
    }) => api.protocols.updateProtocol(protocolId, data),
    onError: (error) =>
      toast.error(error.message || "Failed to update protocol"),
    onSettled: (_, err) =>
      !err && toast.success("Protocol updated successfully"),
    meta: {
      awaits: [
        studyId ? protocolKeys.allLists(studyId) : [],
        protocolId ? protocolKeys.detail(protocolId) : [],
      ],
    },
  });
};

export const useAddStudyProtocol = (studyId: string) => {
  return useMutation({
    mutationFn: (
      data: AddStudyProtocolPayload & {
        hideToast?: boolean;
      },
    ) => api.studies.addProtocol(studyId, data),
    onError: (error) => toast.error(error.message || "Failed to add protocol"),
    onSettled: (_, err, { hideToast }) =>
      !err && !hideToast && toast.success("Protocol added successfully"),
    meta: {
      awaits: protocolKeys.allLists(studyId),
    },
  });
};

export const usePublishProtocol = (studyId: string) => {
  return useMutation({
    mutationFn: (protocolId: string) =>
      api.protocols.publishProtocol(protocolId),
    onError: (error) =>
      toast.error(error.message || "Failed to publish protocol"),
    onSettled: (_, err) =>
      !err && toast.success("Protocol published successfully"),
    meta: {
      awaits: protocolKeys.allLists(studyId),
    },
  });
};

export const useDownloadProtocol = () => {
  return useMutation({
    mutationFn: (protocolId: string) => api.protocols.download(protocolId),
    onError: (error) =>
      toast.error(error.message || "Failed to download protocol"),
  });
};
