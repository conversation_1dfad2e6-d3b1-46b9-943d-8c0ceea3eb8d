import { ColumnDef } from "@tanstack/react-table";
import { Popover } from "flowbite-react";

import {
  TableEditButton,
  TableViewButton,
} from "@/components/shared/table-action-buttons";
import { FormStatusBadge, FormTypeBadge } from "@/components/ui/badges";
import { PillBadge } from "@/components/ui/badges/pill-badge";

export type Form = {
  id: string;
  name: string;
  type: "EDC" | "eSource";
  status: "Draft" | "Published";
  editedBy: string;
  lastEdited: string;
  attachedTo: string[];
  description?: string;
  version?: string;
};

export const columns: ColumnDef<Form>[] = [
  {
    accessorKey: "name",
    header: "Form Name",
    cell: ({ row }) => (
      <div className="font-medium text-gray-900 dark:text-white">
        {row.getValue("name")}
      </div>
    ),
  },
  {
    accessorKey: "type",
    header: "Type",
    cell: ({ row }) => {
      const type = row.getValue("type") as "EDC" | "eSource";
      return <FormTypeBadge variant={type}>{type}</FormTypeBadge>;
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.getValue("status") as "Draft" | "Published";
      return <FormStatusBadge variant={status}>{status}</FormStatusBadge>;
    },
  },
  {
    accessorKey: "editedBy",
    header: "Edited By",
    cell: ({ row }) => (
      <div className="text-sm text-gray-700 dark:text-gray-300">
        {row.getValue("editedBy")}
      </div>
    ),
  },
  {
    accessorKey: "lastEdited",
    header: "Last Edited",
    cell: ({ row }) => (
      <div className="text-sm text-gray-500 dark:text-gray-400">
        {row.getValue("lastEdited")}
      </div>
    ),
  },
  {
    accessorKey: "attachedTo",
    header: "Attached To",
    cell: ({ row }) => {
      const attachedTo = row.getValue("attachedTo") as string[];
      const visibleItems = attachedTo.slice(0, 3);
      const remainingItems = attachedTo.slice(3);

      return (
        <div className="flex flex-wrap items-center gap-1">
          {visibleItems.map((item, index) => (
            <PillBadge key={index} variant="default">
              {item}
            </PillBadge>
          ))}
          {remainingItems.length > 0 && (
            <Popover
              trigger="click"
              content={
                <div className="w-64 p-3">
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                      All Attachments
                    </h4>
                    <div className="flex flex-wrap gap-1">
                      {attachedTo.map((item, index) => (
                        <PillBadge key={index} variant="default">
                          {item}
                        </PillBadge>
                      ))}
                    </div>
                  </div>
                </div>
              }
            >
              <button className="rounded-md border border-blue-200 bg-blue-100 px-2.5 py-1 text-xs font-medium text-blue-600 shadow-sm transition-all hover:bg-blue-200 hover:text-blue-700 hover:shadow dark:border-blue-700 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-800/40 dark:hover:text-blue-300">
                ...
              </button>
            </Popover>
          )}
        </div>
      );
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => (
      <div className="flex gap-2">
        <TableViewButton
          type="button"
          onClick={() => console.log("View form:", row.original.id)}
        />
        <TableEditButton
          type="button"
          onClick={() => console.log("Edit form:", row.original.id)}
        />
      </div>
    ),
  },
];
