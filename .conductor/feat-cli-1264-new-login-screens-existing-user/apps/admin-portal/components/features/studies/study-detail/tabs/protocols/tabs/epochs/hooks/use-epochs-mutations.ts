import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import {
  AddProtocolEpochPayload,
  UpdateProtocolEpochsPayload,
} from "@/lib/apis/protocols";

import { epochKeys } from "./use-epochs-queries";

export const useAddEpoch = (protocolId: string) => {
  return useMutation({
    mutationFn: (data: AddProtocolEpochPayload) =>
      api.protocols.addEpoch(protocolId, data),
    onError: (error) => toast.error(error.message || "Failed to add epoch"),
    onSettled: (_, err) => !err && toast.success("Epoch added successfully"),
    meta: {
      awaits: epochKeys.allLists(protocolId),
    },
  });
};

export const useUpdateEpoch = (protocolId: string) => {
  return useMutation({
    mutationFn: (data: UpdateProtocolEpochsPayload) =>
      api.protocols.updateProtocolsEpoch(data),
    onError: (error) => toast.error(error.message || "Failed to update epoch"),
    onSettled: (_, err) => !err && toast.success("Epoch updated successfully"),
    meta: {
      awaits: epochKeys.allLists(protocolId),
    },
  });
};
