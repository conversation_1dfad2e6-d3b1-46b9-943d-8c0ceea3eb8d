import { use<PERSON><PERSON><PERSON> } from "next/navigation";
import { parseAsString, useQueryState } from "nuqs";
import React from "react";
import { useEffect } from "react";
import { z } from "zod";

import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Form, InputField, Textarea } from "@/components/ui/form";
import { InputNumber } from "@/components/ui/form/input-number-with-buttons";
import { Label } from "@/components/ui/form/label";
import { LazySelect } from "@/components/ui/lazy-select";
import { Modal } from "@/components/ui/modal";
import { Skeleton } from "@/components/ui/skeleton";
import { useInfiniteVisitTypesByStudy } from "@/hooks/queries/use-infinite-visit-types-by-study";
import { ProtocolEncounter } from "@/lib/apis/protocols";

import { useUpdateProtocolEncounter } from "../../hooks/use-update-protocol-encounter";
import { useInfiniteEpochs } from "../epochs/hooks/use-epochs-queries";
import { useInfiniteStudyArms } from "../study-arms/hooks/use-study-arms-queries";

const schema = z.object({
  name: z
    .string({ required_error: "Name is required" })
    .min(1, "Name is required"),
  visitTypeId: z.string().min(1, "Visit type is required"),
  epochId: z.string().optional(),
  studyArmId: z.string().optional(),
  visitDay: z.coerce
    .number({
      invalid_type_error: "Visit day must be a number",
      required_error: "Visit day must be a number",
    })
    .optional(),
  visitWindowStart: z.coerce
    .number({
      invalid_type_error: "Window start must be a number",
      required_error: "Window start must be a number",
    })
    .max(0, "Maximum value is 0")
    .optional(),
  visitWindowEnd: z.coerce
    .number({
      invalid_type_error: "Window end must be a number",
      required_error: "Window end must be a number",
    })
    .min(0, "Minimum value is 0")
    .optional(),
  description: z.string().optional(),
});

type ModalEditEncounterProps = {
  encounter: ProtocolEncounter;
  isOpen: boolean;
  onClose: () => void;
  protocolId: string;
};

export const ModalEditEncounter = ({
  encounter,
  isOpen,
  onClose,
  protocolId,
}: ModalEditEncounterProps) => {
  // const studyId = useParams().id as string;
  // const [, setStudyIdQuery] = useQueryState("studyId", parseAsString);
  const { mutateAsync: updateEncounter, isPending } =
    useUpdateProtocolEncounter();

  // Set the studyId in URL query state when modal opens
  // useEffect(() => {
  //   if (isOpen && studyId) {
  //     setStudyIdQuery(studyId);
  //   }
  // }, [isOpen, studyId, setStudyIdQuery]);

  async function onSubmit(data: z.infer<typeof schema>) {
    if (!encounter) return;

    await updateEncounter({
      id: encounter.id,
      protocolId,
      ...data,
    });
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose} className="[&>div]:max-w-2xl">
      <Modal.Header>Edit Visit</Modal.Header>
      <Modal.Body>
        <Form
          key={`encounter-form-${encounter?.id}`}
          schema={schema}
          mode="onChange"
          onSubmit={onSubmit}
          defaultValues={{
            ...encounter,
            visitTypeId: encounter?.visitTypeId || "",
            epochId: encounter?.epochId || "",
            description: encounter?.description || "",
            studyArmId: encounter?.studyArmId || "",
            visitDay: encounter?.visitDay || undefined,
            visitWindowStart: encounter?.visitWindowStart || "",
            visitWindowEnd: encounter?.visitWindowEnd || "",
          }}
          formProps={{ shouldFocusError: false }}
        >
          <div className="grid gap-4 sm:grid-cols-2">
            <div className="space-y-1 sm:col-span-2">
              <Label htmlFor="name">Name</Label>
              <InputField id="name" name="name" placeholder="Enter name" />
            </div>

            <div className="space-y-1 sm:col-span-2">
              <Label htmlFor="visitDay">Visit Day</Label>
              <InputNumber
                id="visitDay"
                placeholder="0"
                name="visitDay"
                decimalScale={0}
                isShowButtons
              />
            </div>

            <div className="space-y-1">
              <Label htmlFor="visitWindowStart">Visit Window Start</Label>
              <InputNumber
                id="visitWindowStart"
                placeholder="0"
                name="visitWindowStart"
                max={0}
                decimalScale={0}
                isShowButtons
              />
            </div>
            <div className="space-y-1">
              <Label htmlFor="visitWindowEnd">Visit Window End</Label>
              <InputNumber
                id="visitWindowEnd"
                placeholder="0"
                name="visitWindowEnd"
                min={0}
                decimalScale={0}
                isShowButtons
              />
            </div>
            <div className="space-y-1 sm:col-span-2">
              <Label htmlFor="visitTypeId">Visit Type</Label>
              <LazySelect
                name="visitTypeId"
                id="visitTypeId"
                placeholder="Select visit type"
                searchPlaceholder="Search for visit types..."
                useInfiniteQuery={useInfiniteVisitTypesByStudy}
                getOptionLabel={(type) => type.name}
                getOptionValue={(type) => type.id}
              />
            </div>
            <div className="space-y-1">
              <Label htmlFor="studyArmId">Study Arm</Label>
              <LazySelect
                name="studyArmId"
                id="studyArmId"
                placeholder="Select study arm"
                searchPlaceholder="Search study arm..."
                useInfiniteQuery={useInfiniteStudyArms}
                getOptionLabel={(studyArm) => studyArm.name}
                getOptionValue={(studyArm) => studyArm.id}
                params={[protocolId]}
              />
            </div>
            <div className="space-y-1">
              <Label htmlFor="epochId">Epoch</Label>
              <LazySelect
                name="epochId"
                id="epochId"
                placeholder="Select epoch"
                searchPlaceholder="Search epoch..."
                useInfiniteQuery={useInfiniteEpochs}
                getOptionLabel={(epoch) => epoch.name}
                getOptionValue={(epoch) => epoch.id}
                params={[protocolId]}
              />
            </div>

            <div className="space-y-1 sm:col-span-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                placeholder="Enter description"
                className="resize-none"
                rows={4}
              />
            </div>
          </div>

          <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
            <CloseButton onClose={onClose} />
            <Button
              type="submit"
              variant="primary"
              enabledForDirty
              disabled={isPending}
              isLoading={isPending}
            >
              Save
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};

export const EncounterFormSkeleton = () => {
  return (
    <div className="grid grid-cols-2 gap-6">
      {/* Name field */}
      <div className="space-y-1">
        <Skeleton className="h-5 w-16" /> {/* Label */}
        <Skeleton className="h-10 w-full" /> {/* Input */}
      </div>

      {/* Visit Type field */}
      <div className="space-y-1">
        <Skeleton className="h-5 w-24" /> {/* Label */}
        <Skeleton className="h-10 w-full" /> {/* Select */}
      </div>

      {/* Epoch field */}
      <div className="space-y-1">
        <Skeleton className="h-5 w-16" /> {/* Label */}
        <Skeleton className="h-10 w-full" /> {/* Select */}
      </div>

      {/* Study Arm field */}
      <div className="space-y-1">
        <Skeleton className="h-5 w-24" /> {/* Label */}
        <Skeleton className="h-10 w-full" /> {/* Select */}
      </div>

      {/* Description field - spans 2 columns in the form */}
      <div className=" space-y-1">
        <Skeleton className="h-5 w-24" /> {/* Label */}
        <Skeleton className="h-32 w-full" /> {/* Textarea with 4 rows */}
      </div>

      {/* Visit Day field - spans 2 columns in the form */}
      <div className=" space-y-1">
        <Skeleton className="h-5 w-20" /> {/* Label */}
        <Skeleton className="h-10 w-full" /> {/* Input */}
      </div>

      {/* Visit Window Start field */}
      <div className="space-y-1">
        <Skeleton className="h-5 w-32" /> {/* Label */}
        <Skeleton className="h-10 w-full" /> {/* Input */}
      </div>

      {/* Visit Window End field */}
      <div className="space-y-1">
        <Skeleton className="h-5 w-32" /> {/* Label */}
        <Skeleton className="h-10 w-full" /> {/* Input */}
      </div>

      {/* Buttons */}
      <div className=" mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
        <Skeleton className="h-10 w-24" /> {/* Close button */}
        <Skeleton className="h-10 w-32" /> {/* Save button */}
      </div>
    </div>
  );
};
