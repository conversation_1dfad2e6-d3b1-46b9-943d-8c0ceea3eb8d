import { Tabs, TabsItem } from "@/components/ui/tabs";

import { ActivitiesTab } from "./activities-tab";

type Props = {
  totalActivities?: number;
  protocolId: string;
};

export const EncounterTabs = ({ totalActivities, protocolId }: Props) => {
  const isNumber = typeof totalActivities === "number";
  return (
    <Tabs>
      <TabsItem
        title={isNumber ? `Activities (${totalActivities})` : "Activities"}
      >
        <ActivitiesTab protocolId={protocolId} />
      </TabsItem>
    </Tabs>
  );
};
