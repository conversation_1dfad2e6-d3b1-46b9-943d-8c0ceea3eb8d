import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { UpdateVisitTemplatePayload } from "@/lib/apis/visit-schedules";

import { USE_VISIT_SCHEDULE_QUERY_KEY } from "./use-visit-schedule";

export const useUpdateVisitTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (
      payload: UpdateVisitTemplatePayload & {
        id: string;
        visitTemplateId: string;
      },
    ) => {
      await api.visitSchedules.updateVisitTemplate(
        payload.id,
        payload.visitTemplateId,
        payload,
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [USE_VISIT_SCHEDULE_QUERY_KEY],
      });
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
};
