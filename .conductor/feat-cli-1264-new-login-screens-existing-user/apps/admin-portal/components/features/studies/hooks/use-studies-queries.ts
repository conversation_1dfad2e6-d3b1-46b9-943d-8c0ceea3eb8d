import { useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import api from "@/lib/apis";
import type { MetadataParams } from "@/lib/apis/types";

export const studyKeys = {
  all: () => ["studies"] as const,
  allLists: () => [...studyKeys.all(), "list"] as const,
  list: (params?: MetadataParams) => [...studyKeys.allLists(), params] as const,
  allDetails: () => [...studyKeys.all(), "detail"] as const,
  detail: (id: string) => [...studyKeys.allDetails(), id] as const,
};

// Legacy exports for backward compatibility
export const USE_STUDIES_QUERY_KEY = studyKeys.all();
export const USE_STUDY_QUERY_KEY = "study";

export const useStudies = () => {
  const { page, take } = usePagination();
  const { search } = useSearch();

  const params = {
    page,
    take,
    filter: { name: search },
  };

  return useQuery({
    queryKey: studyKeys.list(params),
    queryFn: () => api.studies.list(params),
    placeholderData: (prevData) => prevData,
  });
};

export const useStudy = (id?: string) => {
  return useQuery({
    queryKey: studyKeys.detail(id!),
    queryFn: () => api.studies.get(id!),
    enabled: !!id,
  });
};
