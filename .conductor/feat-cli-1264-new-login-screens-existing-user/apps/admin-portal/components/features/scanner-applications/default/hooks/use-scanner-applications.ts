import { useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import api from "@/lib/apis";
import type { MetadataParams } from "@/lib/apis/types";

export const USE_SCANNER_APPLICATIONS_QUERY_KEY = "scanner-applications";

export const useScannerApplications = (params?: MetadataParams) => {
  const { page, take } = usePagination();
  const { search } = useSearch();
  return useQuery({
    queryKey: [USE_SCANNER_APPLICATIONS_QUERY_KEY, page, take, search],
    queryFn: () =>
      api.scannerApplications.list({
        page,
        take,
        ...params,
        filter: { versionNumber: search },
      }),
    placeholderData: (prevData) => prevData,
  });
};
