import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { ScannerApplicationGetUploadApkPayload } from "@/lib/apis/scanner-applications";

export const useUploadUrl = () => {
  return useMutation({
    mutationFn: (
      payload: ScannerApplicationGetUploadApkPayload & { id: string },
    ) => {
      const { id, ...rest } = payload;
      return api.scannerApplications.getUploadApk(id, rest);
    },
  });
};

export const useDownloadApk = () => {
  return useMutation({
    mutationFn: (id: string) => api.scannerApplications.downloadApk(id),
    onError: (error) => {
      toast.error(error.message || "Failed to download APK");
    },
  });
};
