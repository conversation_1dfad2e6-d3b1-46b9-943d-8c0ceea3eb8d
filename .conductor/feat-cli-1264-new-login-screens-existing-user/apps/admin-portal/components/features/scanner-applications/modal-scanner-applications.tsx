import { useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import toast from "react-hot-toast";
import { z } from "zod";

import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Form, InputField } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { Modal } from "@/components/ui/modal";
import type { ScannerApplication } from "@/lib/apis/scanner-applications";

import { useAddScannerApplication } from "./default/hooks/use-add-scanner-application";
import { useEditScannerApplication } from "./default/hooks/use-edit-scanner-application";
import { USE_SCANNER_APPLICATIONS_QUERY_KEY } from "./default/hooks/use-scanner-applications";
import { useUploadUrl } from "./default/hooks/use-upload-url";

// Validation Schema
export const scannerApplicationSchema = z.object({
  versionNumber: z
    .string({ required_error: "Version Number is required" })
    .min(1, "Version Number is required"),
  apkLink: z.string().optional(),
  releaseNotes: z.string().optional(),
});

type ModalScannerApplicationProps = {
  isOpen: boolean;
  onClose: () => void;
  application?: ScannerApplication | null;
};

export const ModalScannerApplication = ({
  isOpen,
  onClose,
  application,
}: ModalScannerApplicationProps) => {
  const queryClient = useQueryClient();
  const isEditMode = !!application;
  const { mutateAsync: addApplication, isPending: isAdding } =
    useAddScannerApplication();
  const { mutateAsync: editApplication, isPending: isEditing } =
    useEditScannerApplication();
  const { mutateAsync: uploadUrl, isPending: isUploading } = useUploadUrl();

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploadToCloud, setIsUploadToCloud] = useState(false);

  async function onSubmit(data: z.infer<typeof scannerApplicationSchema>) {
    if (!selectedFile && !isEditMode) {
      toast.error("Please upload an APK file");
      return;
    }

    if (selectedFile) {
      setIsUploadToCloud(true);
      try {
        const res = isEditMode
          ? { id: application?.id }
          : await addApplication(data);

        const uploadRes = await uploadUrl({
          id: res.id,
          fileType: selectedFile.type,
          extension: "apk",
        });

        await fetch(uploadRes.url, {
          method: "PUT",
          body: selectedFile,
          headers: {
            "Content-Type": selectedFile.type,
          },
        });

        await editApplication({
          ...data,
          apkLink: uploadRes.url,
          id: res.id,
        });
        await queryClient.invalidateQueries({
          queryKey: [USE_SCANNER_APPLICATIONS_QUERY_KEY],
        });
        if (isEditMode) {
          toast.success("Scanner Application edited successfully");
        } else {
          toast.success("Scanner Application added successfully");
        }

        onClose();
      } catch (err) {
        console.error(err);
        toast.error("Failed to upload APK file");
      } finally {
        setIsUploadToCloud(false);
      }
    }
  }

  return (
    <Modal show={isOpen} onClose={() => onClose()}>
      <Modal.Header>
        {isEditMode ? "Edit" : "Add"} Scanner Application
      </Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={scannerApplicationSchema}
          onSubmit={onSubmit}
          defaultValues={{
            versionNumber: application?.versionNumber || "",
            releaseNotes: application?.releaseNotes || "",
            apkLink: application?.apkLink || "",
          }}
          formProps={{ shouldFocusError: false }}
        >
          <ScannerApplicationForm
            onClose={() => onClose()}
            isSubmitting={
              isAdding || isEditing || isUploading || isUploadToCloud
            }
            selectedFile={selectedFile}
            setSelectedFile={setSelectedFile}
            application={application}
          />
        </Form>
      </Modal.Body>
    </Modal>
  );
};

type ScannerApplicationFormProps = {
  onClose: () => void;
  isSubmitting?: boolean;
  selectedFile: File | null;
  setSelectedFile: (file: File | null) => void;
  application?: ScannerApplication | null;
};

const ScannerApplicationForm = ({
  onClose,
  isSubmitting,
  selectedFile,
  setSelectedFile,
  application,
}: ScannerApplicationFormProps) => {
  const [isValidApk, setIsValidApk] = useState(false);
  const handleApkChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) {
      toast.error("Please select an APK file");
      setIsValidApk(false);
      return;
    }

    if (!file.name.endsWith(".apk")) {
      toast.error("Please upload a valid APK file");
      setIsValidApk(false);
      return;
    }

    setSelectedFile(file);
    setIsValidApk(true);
  };

  return (
    <div className="space-y-4 sm:space-y-6">
      <div>
        <Label
          required={!application}
          className="mb-2 block text-sm font-medium text-gray-900 dark:text-white"
        >
          {application ? "Upload new APK file" : "Upload APK file"}
        </Label>
        <input
          type="file"
          accept=".apk"
          onChange={handleApkChange}
          className="block w-full cursor-pointer rounded-lg border border-gray-300 bg-gray-50 text-sm text-gray-900 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-gray-400 dark:placeholder:text-gray-400"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="versionNumber">Version Number</Label>
        <InputField
          id="versionNumber"
          name="versionNumber"
          placeholder="Enter version number..."
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="releaseNotes">Release Notes</Label>
        <InputField
          id="releaseNotes"
          name="releaseNotes"
          placeholder="Enter release notes..."
        />
      </div>

      <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
        <CloseButton onClose={onClose} />
        <Button
          type="submit"
          variant="primary"
          isLoading={isSubmitting}
          disabled={Boolean(application ? false : !selectedFile || !isValidApk)}
        >
          Save
        </Button>
      </div>
    </div>
  );
};
