import { useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

export const isfTemplateKeys = {
  all: () => ["isf-templates"] as const,
  allList: () => [...isfTemplateKeys.all(), "list"] as const,
  list: (params: MetadataParams) => [...isfTemplateKeys.allList(), params],
  allDetail: () => [...isfTemplateKeys.all(), "detail"] as const,
  detail: (id: string) => [...isfTemplateKeys.allDetail(), id] as const,
};

export const useISFTemplates = () => {
  const { search } = useSearch();
  const { page } = usePagination();
  const params = {
    page,
    filter: {
      name: search,
    },
  };

  return useQuery({
    queryKey: isfTemplateKeys.list(params),
    queryFn: () => api.isfTemplates.getTemplates(params),
    placeholderData: (prev) => prev,
  });
};

export const useISFTemplate = (id: string) => {
  return useQuery({
    queryKey: isfTemplateKeys.detail(id),
    queryFn: () => api.isfTemplates.getTemplate(id),
  });
};
