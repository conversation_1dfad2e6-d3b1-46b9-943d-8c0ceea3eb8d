"use client";
import {
  Dnd<PERSON>ontext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { restrictToWindowEdges } from "@dnd-kit/modifiers";
import { Card, Tooltip } from "flowbite-react";
import { useParams } from "next/navigation";
import { parseAsString, useQueryState } from "nuqs";
import React, { useState } from "react";
import { IoMdAdd } from "react-icons/io";
import { Panel, PanelGroup, PanelResizeHandle } from "react-resizable-panels";

import { Button } from "@/components/ui/button";
import { useDisclosure } from "@/hooks/use-disclosure";
import { useMediaQuery } from "@/hooks/use-media-query";
import { Directory, File } from "@/lib/apis/isf-templates";

import {
  DocumentsTable,
  FileWithParentDirectoryName,
} from "./documents/documents-table";
import { FileOverlay } from "./documents/file-overlay";
import { DirectoryWithPath } from "./folders/folder-node";
import { FolderOverlay } from "./folders/folder-overlay";
import { FolderColumn } from "./folders/folders-column";
import { ROLLBACK_ACTIONS, useActionHistory } from "./hooks/use-action-history";
import {
  useMoveFile,
  useMoveFolder,
} from "./hooks/use-isf-template-detail-mutations";
import { PlaceholderModal } from "./placeholder-modal";

export const ISFTemplateDetail = () => {
  const id = useParams().id as string;
  const [parentDirectoryName] = useQueryState("folderName", parseAsString);
  const { addAction } = useActionHistory({
    onRollBack: async (lastAction) => {
      if (lastAction.type === ROLLBACK_ACTIONS.moveFolder) {
        await moveFolder({
          folderName: lastAction.folderName,
          newParentDirectoryName: lastAction.oldFolderName || undefined,
          oldParentDirectoryName: lastAction.newFolderName || undefined,
          isRollback: true,
        });
      }

      if (lastAction.type === ROLLBACK_ACTIONS.moveDocument) {
        await moveFile({
          title: lastAction.documentName,
          newParentDirectoryName: lastAction.oldFolderName,
          oldParentDirectoryName: lastAction.newFolderName,
          isRollback: true,
        });
      }
    },
    isAllowKeyboard: true,
  });

  const mouseSensor = useSensor(MouseSensor, {
    activationConstraint: {
      distance: 10, // Require the mouse to move by 10 pixels before activating
    },
  });
  const touchSensor = useSensor(TouchSensor, {
    activationConstraint: {
      delay: 250, // Press delay of 250ms, with tolerance of 5px of movement
      tolerance: 5,
    },
  });

  const sensors = useSensors(mouseSensor, touchSensor);

  const isTablet = useMediaQuery("(min-width: 768px)");

  const {
    isOpen: isPlaceholderOpen,
    open: onPlaceholderOpen,
    close: onPlaceholderClose,
  } = useDisclosure();

  const [draggingFolder, setDraggingFolder] = useState<Directory | null>(null);
  const [draggingFile, setDraggingFile] =
    useState<FileWithParentDirectoryName | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [activePath, setActivePath] = useState("");

  const { mutateAsync: moveFolder, isPending: isMovingFolder } =
    useMoveFolder(id);

  const { mutateAsync: moveFile, isPending: isMovingFile } = useMoveFile(id);

  const handleClosePlaceholderModal = () => {
    onPlaceholderClose();
    setSelectedFile(null);
  };

  const handleOpenUpdatePlaceholderModal = (file: File) => {
    onPlaceholderOpen();
    setSelectedFile(file);
  };

  const handleDragStart = (e: DragStartEvent) => {
    const unknownDraggingItem = e.active.data.current;

    if (unknownDraggingItem?.type === "document") {
      return setDraggingFile(
        unknownDraggingItem as FileWithParentDirectoryName,
      );
    }
    setDraggingFolder(unknownDraggingItem as Directory);
  };
  const handleDragEnd = async (e: DragEndEvent) => {
    const source = e.active.data.current;
    const target = e.over?.data.current as DirectoryWithPath | undefined;

    setDraggingFolder(null);
    setDraggingFile(null);

    // handle move document
    if (source?.type === "document") {
      const file = source as FileWithParentDirectoryName;
      if (file?.parentDirectoryName === target?.name || !target) return;

      await moveFile({
        title: file.title,
        newParentDirectoryName: target.name,
        oldParentDirectoryName: file.parentDirectoryName,
      });
      addAction({
        documentName: file.title,
        type: ROLLBACK_ACTIONS.moveDocument,
        newFolderName: target.name,
        oldFolderName: file.parentDirectoryName,
      });
      return;
    }

    // handle move folder
    const folder = source as DirectoryWithPath;
    if (
      (folder.name === folder?.path.slice(1) && !target) || // Dragging a a top-level to the root
      folder.name === target?.name || // Dragging a folder onto itself
      target?.directories.some((dir) => dir.name === folder.name) || // Dragging a folder to its current parent
      target?.path?.startsWith(folder.path) // Dragging a parent folder into its own subfolder
    )
      return;

    await moveFolder({
      folderName: folder.name,
      newParentDirectoryName: target?.name,
      oldParentDirectoryName: folder?.parentDirectoryName,
    });
    addAction({
      type: ROLLBACK_ACTIONS.moveFolder,
      oldFolderName: folder.parentDirectoryName || null,
      newFolderName: target?.name || null,
      folderName: folder.name,
    });
  };

  return (
    <>
      <Card className="[&>div]:p-2 sm:[&>div]:p-4">
        <div className="mb-4 flex justify-end gap-4 ">
          {parentDirectoryName ? (
            <Button
              className="w-full sm:w-fit"
              onClick={onPlaceholderOpen}
              variant="primary"
            >
              <IoMdAdd /> Create Placeholder
            </Button>
          ) : (
            <Tooltip content="Select a folder">
              <Button className="w-full sm:w-fit" variant="primary" disabled>
                <IoMdAdd /> Create Placeholder
              </Button>
            </Tooltip>
          )}
        </div>
        <div className="flex flex-1 flex-col gap-4">
          <DndContext
            sensors={sensors}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
            modifiers={[restrictToWindowEdges]}
          >
            {isTablet ? (
              <PanelGroup direction="horizontal">
                <Panel defaultSize={20} minSize={20}>
                  <FolderColumn
                    draggingFolder={draggingFolder}
                    draggingFile={draggingFile}
                    isMovingFolder={isMovingFolder}
                    isMovingDocument={isMovingFile}
                    activePath={activePath}
                    setActivePath={setActivePath}
                  />
                </Panel>
                <PanelResizeHandle className="mx-[5px] hidden w-1 hover:bg-blue-400 active:bg-blue-500 sm:block" />
                <Panel minSize={20}>
                  <DocumentsTable
                    isMovingDocument={isMovingFile}
                    onOpenEditDocument={handleOpenUpdatePlaceholderModal}
                    draggingFile={draggingFile}
                  />
                </Panel>
              </PanelGroup>
            ) : (
              <div className="space-y-4">
                <FolderColumn
                  draggingFolder={draggingFolder}
                  draggingFile={draggingFile}
                  isMovingFolder={isMovingFolder}
                  isMovingDocument={isMovingFile}
                  activePath={activePath}
                  setActivePath={setActivePath}
                />
                <DocumentsTable
                  isMovingDocument={isMovingFile}
                  onOpenEditDocument={handleOpenUpdatePlaceholderModal}
                  draggingFile={draggingFile}
                />
              </div>
            )}

            <DragOverlay adjustScale={false} zIndex={9999} dropAnimation={null}>
              {!!draggingFile && <FileOverlay document={draggingFile} />}

              {!!draggingFolder && (
                <FolderOverlay
                  folderName={draggingFolder?.name}
                  fileCount={draggingFolder?.files?.length}
                />
              )}
            </DragOverlay>
          </DndContext>
        </div>
      </Card>

      {isPlaceholderOpen && (
        <PlaceholderModal
          selectedFile={selectedFile}
          isOpen={isPlaceholderOpen}
          onClose={handleClosePlaceholderModal}
        />
      )}
    </>
  );
};
