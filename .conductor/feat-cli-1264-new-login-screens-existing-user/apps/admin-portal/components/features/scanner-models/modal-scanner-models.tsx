import { z } from "zod";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/button";
import { Form, InputField } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { Modal } from "@/components/ui/modal";
import type { ScannerModel } from "@/lib/apis/scanner-models/types";

import { useAddScannerModels } from "./default/hooks/use-add-scanner-models";
import { useEditScannerModel } from "./default/hooks/use-edit-scanner-models";

// Validation Schema for Scanner Model
export const scannerModelSchema = z.object({
  companyName: z
    .string({ required_error: "Company Name is required" })
    .min(1, "Company Name is required"),
  modelName: z
    .string({ required_error: "Model Name is required" })
    .min(1, "Model Name is required"),
  description: z.string().optional(),
});

type ModalScannerModelProps = {
  isOpen: boolean;
  onClose: () => void;
  model?: ScannerModel | null;
};

export const ModalScannerModel = ({
  isOpen,
  onClose,
  model,
}: ModalScannerModelProps) => {
  const { mutateAsync: addScannerModel, isPending: isAdding } =
    useAddScannerModels();
  const { mutateAsync: editScannerModel, isPending: isEditing } =
    useEditScannerModel();

  const isEditingMode = Boolean(model);

  async function onSubmit(data: z.infer<typeof scannerModelSchema>) {
    if (isEditingMode && model) {
      await editScannerModel({ id: model.id, ...data });
    } else {
      await addScannerModel(data);
    }
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={() => onClose()}>
      <Modal.Header>
        {isEditingMode ? "Edit Scanner Model" : "Add Scanner Model"}
      </Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={scannerModelSchema}
          onSubmit={onSubmit}
          defaultValues={{
            companyName: model?.companyName || "",
            modelName: model?.modelName || "",
            description: model?.description || "",
          }}
          formProps={{ shouldFocusError: false }}
        >
          <ScannerModelForm
            onClose={onClose}
            isSubmitting={isAdding || isEditing}
          />
        </Form>
      </Modal.Body>
    </Modal>
  );
};

type ScannerModelFormProps = {
  onClose: () => void;
  isSubmitting?: boolean;
};

const ScannerModelForm = ({ onClose, isSubmitting }: ScannerModelFormProps) => {
  return (
    <>
      <div className="space-y-4 sm:space-y-6">
        <div className="space-y-2">
          <Label htmlFor="companyName">Company Name</Label>
          <InputField
            id="companyName"
            name="companyName"
            placeholder="Enter company name..."
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="modelName">Model Name</Label>
          <InputField
            id="modelName"
            name="modelName"
            placeholder="Enter model name..."
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <InputField
            id="description"
            name="description"
            placeholder="Enter description..."
          />
        </div>
      </div>
      <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
        <CloseButton onClose={onClose} />
        <Button type="submit" variant="primary" isLoading={isSubmitting}>
          Save
        </Button>
      </div>
    </>
  );
};
