import { skipToken, useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

export const userKeys = {
  all: () => ["users-tab"] as const,

  allLists: () => [...userKeys.all(), "lists"] as const,
  list: (params?: MetadataParams) => [...userKeys.allLists(), params] as const,

  allDetails: () => [...userKeys.all(), "details"] as const,
  detail: (userId: string) => [...userKeys.allDetails(), userId] as const,

  customPermissions: (profileId: string) =>
    [...userKeys.all(), "custom-permission", profileId] as const,
};

export const useUsersByGroupId = (id?: string) => {
  const { page, take } = usePagination();

  const params = {
    page,
    take,
    filter: {
      groupId: id,
    },
  };

  return useQuery({
    queryKey: id ? userKeys.list(params) : [],
    queryFn: id ? () => api.users.list(params) : skipToken,
    placeholderData: (prev) => prev,
  });
};

export const useUser = (id?: string) => {
  return useQuery({
    queryKey: id ? userKeys.detail(id) : [],
    queryFn: id ? () => api.users.get(id) : skipToken,
  });
};

export const useUserCustomPermission = (profileId?: string) => {
  return useQuery({
    queryKey: profileId ? userKeys.customPermissions(profileId) : [],
    queryFn: profileId
      ? () => api.users.getUserCustomPermission(profileId)
      : skipToken,
  });
};
