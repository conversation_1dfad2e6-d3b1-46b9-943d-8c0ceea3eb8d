import { z } from "zod";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { LazySelect } from "@/components/ui/lazy-select";
import { Modal } from "@/components/ui/modal";
import { Skeleton } from "@/components/ui/skeleton";
import { useInfiniteRoles } from "@/hooks/queries/use-infinite-roles";
import { capitalize } from "@/utils/string";

import { useUpdateRoleProfile } from "./hooks/use-users-mutations";
import { useUser } from "./hooks/use-users-queries";

const addProfileSchema = z.object({
  roleId: z
    .string({ required_error: "Role is required" })
    .min(1, "Role is required"),
});

type Props = {
  selectedUserId: string;
  onClose: () => void;
  isOpen: boolean;
};

export const ModalEditProfile = ({
  selectedUserId,
  onClose,
  isOpen,
}: Props) => {
  const { data, isPending } = useUser(selectedUserId);
  const { mutateAsync, isPending: isUpdating } =
    useUpdateRoleProfile(selectedUserId);

  const handleSubmit = async (values: z.infer<typeof addProfileSchema>) => {
    if (!data) return;
    await mutateAsync({
      roleId: values.roleId,
      profileId: data?.currentProfile.rolesProfiles[0].profileId,
      userId: data?.id,
    });
    onClose();
  };

  return (
    <Modal show={!!isOpen} onClose={onClose}>
      <Modal.Header>Edit Profile</Modal.Header>
      <Modal.Body>
        {isPending ? (
          <div className="space-y-4">
            <div className="space-y-2">
              <Skeleton className="h-5 w-10" />
              <Skeleton className="h-[42px] w-full" />
            </div>
            <div className="flex items-center justify-end gap-4">
              <Skeleton className="h-[42px] w-[105px]" />
              <Skeleton className="h-[42px] w-[98px]" />
            </div>
          </div>
        ) : (
          <Form
            schema={addProfileSchema}
            onSubmit={handleSubmit}
            defaultValues={{
              roleId: data?.currentProfile?.rolesProfiles?.[0]?.roleId,
            }}
            className="space-y-4"
          >
            <div className="flex flex-col gap-2">
              <Label htmlFor="roleId">Role</Label>
              <LazySelect
                name="roleId"
                id="roleId"
                searchPlaceholder="Search role..."
                useInfiniteQuery={useInfiniteRoles}
                getOptionLabel={(role) =>
                  `${role.name} (${capitalize(role.type)})`
                }
                getOptionValue={(role) => role.id}
                params={[
                  data?.currentProfile.currentGroup.type === "clincove"
                    ? "admin"
                    : data?.currentProfile.currentGroup.type,
                ]}
                placeholder="Select role"
              />
            </div>

            <div className="flex flex-col justify-end gap-4 sm:flex-row">
              <CloseButton onClose={onClose} />
              <Button
                type="submit"
                variant="primary"
                enabledForDirty
                disabledForInvalid
                isLoading={isUpdating}
              >
                Save
              </Button>
            </div>
          </Form>
        )}
      </Modal.Body>
    </Modal>
  );
};
