"use client";

import { Card } from "flowbite-react";
import { useMemo } from "react";

import { useGroupStudies } from "@/components/features/settings/groups/group-detail/hooks/use-group-studies";
import { generateDefaultPermissionColumns } from "@/components/features/users/user-detail/tabs/profiles/columns";
import { TableLoading } from "@/components/ui/table";
import { TableData } from "@/components/ui/table/table";

export const DefaultPermissions = ({ groupId }: { groupId: string }) => {
  const { data: studies, isLoading } = useGroupStudies(groupId);
  const columns = useMemo(() => generateDefaultPermissionColumns(), []);

  return (
    <Card className="[&>div]:p-0">
      <div className="mb-4 flex items-center justify-between p-4 pb-0 text-lg font-semibold">
        <div className="dark:text-gray-400">Studies</div>
      </div>

      {isLoading ? (
        <TableLoading columns={columns} />
      ) : (
        <TableData
          columns={columns}
          data={studies?.results ?? []}
          isLoading={isLoading}
        />
      )}
    </Card>
  );
};
