import { customPermissionColumn } from "@/components/features/users/user-detail/tabs/profiles/columns";
import { But<PERSON> } from "@/components/ui/button";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsItem } from "@/components/ui/tabs";

import { useRestoreUserCustomPermission } from "../hooks/use-users-mutations";
import { useUser } from "../hooks/use-users-queries";
import { ActivePermissions } from "./active-permissions-tab";
import { DefaultPermissions } from "./default-permissions-tab";

type Props = {
  selectedUserId: string;
  onClose: () => void;
  isOpen: boolean;
};

const StudiesModal = ({ selectedUserId, onClose, isOpen }: Props) => {
  const { data, isPending } = useUser(selectedUserId);

  const { mutateAsync, isPending: isRestoring } =
    useRestoreUserCustomPermission(selectedUserId);

  const handleRestorePermission = () => {
    if (!data) return;
    mutateAsync({
      profileId: data.currentProfileId,
      userId: data.id,
    });
  };

  return (
    <WrapperModal
      isOpen={isOpen}
      onClose={onClose}
      title="Studies"
      className="[&>div]:max-w-3xl"
    >
      <div className="grid gap-2">
        <div className="grid grid-cols-2 gap-x-2 sm:grid-cols-3">
          <span className="text-md font-medium dark:text-gray-400">
            Group Name
          </span>
          <span className="text-sm text-gray-500 sm:col-span-2">
            {isPending ? (
              <Skeleton className="h-6 w-52" />
            ) : (
              data?.currentProfile?.currentGroup?.name || "N/A"
            )}
          </span>
        </div>
        <div className="grid grid-cols-2 gap-x-2 sm:grid-cols-3">
          <span className="text-md font-medium dark:text-gray-400">
            Group Type
          </span>
          <span className="text-sm text-gray-500 sm:col-span-2">
            {isPending ? (
              <Skeleton className="h-6 w-14" />
            ) : (
              data?.currentProfile?.currentGroup?.type || "N/A"
            )}
          </span>
        </div>
        <div className="grid grid-cols-2 gap-x-2 sm:grid-cols-3">
          <span className="text-md font-medium dark:text-gray-400">
            Profile Name
          </span>
          <span className="text-sm text-gray-500 sm:col-span-2">
            {isPending ? (
              <Skeleton className="h-6 w-52" />
            ) : (
              data?.currentProfile?.name || "N/A"
            )}
          </span>
        </div>
        <div className="grid grid-cols-2 gap-x-2 sm:grid-cols-3">
          <span className="text-md font-medium dark:text-gray-400">Site</span>
          <span className="text-sm text-gray-500 sm:col-span-2">
            {isPending ? (
              <Skeleton className="h-6 w-14" />
            ) : (
              data?.currentProfile?.currentGroup?.site?.name || "N/A"
            )}
          </span>
        </div>
        <div className="grid grid-cols-2 gap-x-2 sm:grid-cols-3">
          <span className="text-md font-medium dark:text-gray-400">
            Custom Permissions
          </span>
          <span className="text-sm text-gray-500 sm:col-span-2">
            {isPending ? (
              <Skeleton className="size-[22px]" />
            ) : (
              customPermissionColumn[
                `${data?.currentProfile.hasCustomPermissions || false}`
              ]
            )}
          </span>
        </div>
      </div>
      <div className="mt-2 flex justify-end">
        <Button
          onClick={handleRestorePermission}
          disabled={
            !data?.currentProfile.hasCustomPermissions ||
            isPending ||
            isRestoring
          }
          isLoading={isRestoring}
          variant="primary"
        >
          Restore to default
        </Button>
      </div>
      <Tabs>
        <TabsItem title="Active Permissions">
          <ActivePermissions
            profileId={data?.currentProfileId || ""}
            groupId={data?.currentProfile.currentGroupId || ""}
            userId={data?.id || ""}
          />
        </TabsItem>
        <TabsItem title="Default Permissions">
          <DefaultPermissions
            groupId={data?.currentProfile.currentGroupId || ""}
          />
        </TabsItem>
      </Tabs>
    </WrapperModal>
  );
};

export default StudiesModal;
