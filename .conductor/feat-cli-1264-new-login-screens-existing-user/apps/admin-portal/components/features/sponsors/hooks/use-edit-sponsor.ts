import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { EditSponsorPayload } from "@/lib/apis/sponsors/types";

import { USE_SPONSOR_QUERY_KEY } from "./use-sponsor";

export const useEditSponsor = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: EditSponsorPayload & { id: string }) => {
      const { id, ...payload } = data;
      return api.sponsors.edit(id, payload);
    },
    onSuccess: (_, variables) => {
      toast.success("Sponsor updated successfully");
      queryClient.invalidateQueries({
        queryKey: [USE_SPONSOR_QUERY_KEY, variables.id],
      });
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
};
