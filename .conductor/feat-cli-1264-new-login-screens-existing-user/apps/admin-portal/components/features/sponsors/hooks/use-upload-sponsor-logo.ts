import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { SponsorGetUploadLogoPayload } from "@/lib/apis/sponsors/types";

export const useUploadSponsorLogo = () => {
  return useMutation({
    mutationFn: (payload: SponsorGetUploadLogoPayload & { id: string }) =>
      api.sponsors.getUploadLogo(payload.id, payload),
    onError: () => {
      toast.error("Failed to upload logo");
    },
  });
};
