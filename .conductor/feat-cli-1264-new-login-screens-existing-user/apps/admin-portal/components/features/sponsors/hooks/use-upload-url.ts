import { useMutation } from "@tanstack/react-query";

import api from "@/lib/apis";
import type { SponsorGetUploadLogoPayload } from "@/lib/apis/sponsors/types";

export const useUploadUrl = () => {
  return useMutation({
    mutationFn: (payload: SponsorGetUploadLogoPayload & { id: string }) => {
      const { id, ...rest } = payload;
      return api.sponsors.getUploadLogo(id, rest);
    },
  });
};
