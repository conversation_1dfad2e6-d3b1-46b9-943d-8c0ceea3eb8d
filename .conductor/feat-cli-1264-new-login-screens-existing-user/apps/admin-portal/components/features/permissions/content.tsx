"use client";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { PageHeader } from "@/components/ui/page-header";
import { TabsWrapper } from "@/components/ui/tabs-wrapper";

import { ActionsTab } from "./tabs/actions";
import { SubjectsTab } from "./tabs/subjects";

const PERMISSION_TABS = [
  {
    title: "Permission Subjects",
    key: "subject",
    content: <SubjectsTab />,
  },
  {
    title: "Permission",
    key: "actions",
    content: <ActionsTab />,
  },
];

const BREADCRUMB_ITEMS = [{ label: "Permissions" }];

export const PermissionsContent = () => {
  return (
    <>
      <Breadcrumb items={BREADCRUMB_ITEMS} />
      <PageHeader>Permissions</PageHeader>
      <TabsWrapper tabs={PERMISSION_TABS} />
    </>
  );
};
