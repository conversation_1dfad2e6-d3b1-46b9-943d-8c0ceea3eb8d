import { type ColumnDef } from "@tanstack/react-table";
import Link from "next/link";
import { IoMdEye } from "react-icons/io";

import { PillBadge } from "@/components/ui/badges/pill-badge";
import type { Site } from "@/lib/apis/sites/types";

export const columns: ColumnDef<Site>[] = [
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => (
      <Link
        href={`/sites/${row.original.id}`}
        className="text-primary-500 font-medium hover:underline"
      >
        {row.getValue("name")}
      </Link>
    ),
  },
  {
    accessorKey: "address.city",
    header: "City",
  },
  {
    accessorKey: "address.stateProvince.name",
    header: "State / Province",
  },
  {
    accessorKey: "isActive",
    header: "Status",
    cell: ({ row }) => {
      if (row.original.isActive) {
        return <PillBadge variant="success">Active</PillBadge>;
      }
      return <PillBadge variant="default">Inactive</PillBadge>;
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const site = row.original;

      return (
        <div className="flex gap-4 text-xs text-blue-500">
          <Link
            href={`/sites/${site.id}`}
            className="text-destructive flex items-center gap-1"
          >
            <span>View</span>
            <IoMdEye />
          </Link>
        </div>
      );
    },
  },
];
