import { useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import api from "@/lib/apis";

export const USE_SITES_QUERY_KEY = "sites";

export const useSites = () => {
  const { page, take } = usePagination();
  const { search } = useSearch();

  return useQuery({
    queryKey: [USE_SITES_QUERY_KEY, page, take, search],
    queryFn: () => api.sites.list({ page, take, filter: { name: search } }),
    placeholderData: (prevData) => prevData,
  });
};
