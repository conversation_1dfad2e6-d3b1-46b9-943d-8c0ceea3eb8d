import { functionalUpdate } from "@tanstack/react-table";
import { Card, ToggleSwitch } from "flowbite-react";
import React, { useMemo } from "react";
import { CiWarning } from "react-icons/ci";

import { SearchField } from "@/components/shared";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Button } from "@/components/ui/button";
import { ConfirmModal } from "@/components/ui/modal/confirm-modal";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import { cn } from "@/lib/utils";

import { useLocalStudyParams } from "../../hooks/useLocalStudyParams";
import { generateTaskColumns } from "./columns";
import { useTaskFilter } from "./hooks/use-task-filter";
import { useTaskModals } from "./hooks/use-task-modals-store";
import { useDeleteTask } from "./hooks/use-task-mutations";
import { useStudyTasks } from "./hooks/use-tasks-queries";
import { TaskKanban } from "./kanban";
import { TaskDetailModal } from "./task-detail-modal";
import { TaskFilters } from "./task-filter";
import { TaskModal } from "./task-modal";

export const TasksTab = () => {
  const { studyId, siteId } = useLocalStudyParams();
  const { changeSort, orderBy, orderDirection, isTable, setIsTable } =
    useTaskFilter();

  const {
    onOpenTaskModal,
    onOpenConfirmDeleteModal,
    selectedTaskId,
    isOpenConfirmDeleteModal,
    onCloseConfirmDeleteModal,
    onOpenTaskDetailModal,
    onCloseTaskDetailModal,
  } = useTaskModals();

  const { isPending, data, isPlaceholderData } = useStudyTasks(studyId);
  const { isPending: isDeleting, mutateAsync: deleteTask } = useDeleteTask({
    siteId,
    studyId,
  });

  const columns = useMemo(
    () =>
      generateTaskColumns({
        onView: (taskId) => {
          onOpenTaskDetailModal(taskId);
        },
        onEdit: (task) => {
          onOpenTaskModal({
            task: task,
          });
        },
        onDelete: (task) => {
          onOpenConfirmDeleteModal(task.id);
        },
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [],
  );

  const onConfirmDelete = async () => {
    await deleteTask(selectedTaskId);
    onCloseConfirmDeleteModal();
    onCloseTaskDetailModal();
  };

  const sorting = !orderBy
    ? []
    : [{ id: orderBy, desc: orderDirection === "desc" }];

  return (
    <>
      <Card className="[&>div]:p-0">
        <div className="p-4">
          <div className="flex flex-col justify-between gap-4 lg:flex-row lg:items-center">
            <div className="flex flex-shrink-0 items-center gap-6">
              <h2 className="text-2xl font-semibold dark:text-white">Tasks</h2>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setIsTable(isTable ? null : true)}
                  className={cn(
                    "select-none text-sm font-medium text-gray-900 dark:text-gray-300",
                    isTable && "text-primary-500 dark:text-primary-500",
                  )}
                >
                  Table View
                </button>
                <ToggleSwitch
                  checked={!isTable}
                  className={cn(
                    !isTable &&
                      "[&>span]:text-primary-500 dark:[&>span]text-primary-500",
                  )}
                  label="Kanban View"
                  onChange={(checked) => setIsTable(checked ? null : true)}
                />
              </div>
            </div>
            <div className="flex items-center justify-end gap-4">
              <SearchField placeholder="Search..." className="max-w-52" />
              <TaskFilters />
              <Button onClick={() => onOpenTaskModal()} variant="primary">
                New Task
              </Button>
            </div>
          </div>
        </div>
        {!isTable ? (
          <div className="pb-4">
            <TaskKanban />
          </div>
        ) : isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <>
            <LoadingWrapper isLoading={isPlaceholderData}>
              <Table
                columns={columns}
                data={data?.results ?? []}
                sorting={sorting}
                enableSorting
                onSortingChange={(updater) => {
                  const newSorting = functionalUpdate(updater, sorting);
                  const sort = newSorting[0];
                  if (sort)
                    return changeSort(sort.id, sort.desc ? "desc" : "asc");
                  changeSort();
                }}
              />
            </LoadingWrapper>
            {data?.metadata && <TableDataPagination metadata={data.metadata} />}
          </>
        )}
      </Card>
      <TaskModal />
      <ConfirmModal
        isOpen={isOpenConfirmDeleteModal}
        onClose={onCloseConfirmDeleteModal}
        onConfirm={onConfirmDelete}
        isLoading={isDeleting}
      >
        <div className="flex flex-col items-center gap-2">
          <CiWarning className="size-10 text-red-600" />
          <span className="dark:text-white">
            Are you sure you want to delete this task?
          </span>
        </div>
      </ConfirmModal>
      <TaskDetailModal />
    </>
  );
};
