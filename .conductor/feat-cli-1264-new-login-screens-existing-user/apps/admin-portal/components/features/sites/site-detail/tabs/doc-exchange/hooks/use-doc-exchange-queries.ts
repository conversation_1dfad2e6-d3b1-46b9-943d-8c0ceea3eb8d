import { skipToken, useQuery } from "@tanstack/react-query";
import { endOfDay, parse, startOfDay } from "date-fns";

import api from "@/lib/apis";
import { MetadataParams, OrderDirection } from "@/lib/apis/types";

import { useFilterDocuments } from "./use-filter-documents";

export const docExchangeKeys = {
  all: () => ["doc-exchange"] as const,

  allFolderLists: () => [...docExchangeKeys.all(), "folders"] as const,
  folderList: (siteId: string) =>
    [...docExchangeKeys.allFolderLists(), siteId] as const,

  allDocumentLists: () => [...docExchangeKeys.all(), "documents"] as const,
  documentList: (params: {
    filter?: Record<string, any>;
    orderBy?: string;
    orderDirection?: string;
    page: number | null;
    take: number | null;
    siteId: string;
  }) => [...docExchangeKeys.allDocumentLists(), params] as const,

  allDocumentDetails: () => [...docExchangeKeys.all(), "details"] as const,
  documentDetail: (id?: string) =>
    [...docExchangeKeys.allDocumentDetails(), id] as const,

  allAuditLogLists: (id: string) => [
    ...docExchangeKeys.all(),
    "audit-logs",
    id,
  ],
  auditLogList: ({ id, params }: { params?: MetadataParams; id: string }) => [
    ...docExchangeKeys.allAuditLogLists(id),
    params,
  ],
  allDocumentStatuses: () => [...docExchangeKeys.all(), "document-status"],
  documentStatuses: (id?: string) => [
    ...docExchangeKeys.allDocumentStatuses(),
    id,
  ],

  allDocumentVersions: () => [...docExchangeKeys.all(), "document-versions"],
  documentVersions: (id?: string) => [
    ...docExchangeKeys.allDocumentVersions(),
    id,
  ],

  allDownloadedDocument: () => [
    ...docExchangeKeys.all(),
    "downloaded-document",
  ],
  downloadedDocument: (id?: string) => [
    ...docExchangeKeys.allDownloadedDocument(),
    id,
  ],
};

export const useFolders = (siteId: string) => {
  const params = {
    filter: {
      siteId,
    },
  };
  return useQuery({
    queryKey: docExchangeKeys.folderList(siteId),
    queryFn: () => api.docExchange.getFolders(params),
  });
};

export const useDocuments = () => {
  const {
    siteId,
    search,
    folderId,
    page,
    take,
    status,
    extension,
    fromCreatedDate,
    toCreatedDate,
    orderBy,
    orderDirection: orderDirection,
  } = useFilterDocuments();

  const fromCreatedDateUTC = fromCreatedDate
    ? startOfDay(parse(fromCreatedDate, "M/d/yyyy", new Date())).toISOString()
    : undefined;
  const toCreatedDateUTC = toCreatedDate
    ? endOfDay(parse(toCreatedDate, "M/d/yyyy", new Date())).toISOString()
    : undefined;

  const params = {
    siteId,
    page,
    take,
    orderBy: orderBy || undefined,
    orderDirection: orderDirection?.toLocaleUpperCase() as OrderDirection,
    filter: {
      title: search,
      status: status,
      extension: extension,
      fromCreatedDate: fromCreatedDateUTC,
      toCreatedDate: toCreatedDateUTC,
      parentDirectoryId: folderId,
    },
  };

  return useQuery({
    queryKey: docExchangeKeys.documentList(params),
    queryFn: () => api.docExchange.getDocuments(params),
    placeholderData: (prev) => prev,
  });
};

export const useDocument = (id?: string) =>
  useQuery({
    queryKey: docExchangeKeys.documentDetail(id),
    queryFn: id ? () => api.essentialDocumentFiles.getDocument(id) : skipToken,
  });

export const useAuditLogs = ({ page, id }: { id?: string; page: number }) => {
  const params = {
    page,
  };
  return useQuery({
    queryKey: id ? docExchangeKeys.auditLogList({ id, params }) : [],
    queryFn: id
      ? () => api.docExchange.getDocumentActivities(id, params)
      : skipToken,
    placeholderData: (prev) => prev,
  });
};

export const useDocumentStatus = (id?: string) =>
  useQuery({
    queryKey: docExchangeKeys.documentStatuses(id),
    queryFn: id ? () => api.artifactStatus.getValidStates(id) : skipToken,
  });

export const useDocumentVersions = (id?: string) =>
  useQuery({
    queryKey: docExchangeKeys.documentVersions(id),
    queryFn: id
      ? () => api.essentialDocumentFiles.getDocumentVersions(id)
      : skipToken,
  });

export const useDownloadDocument = (documentId?: string) =>
  useQuery({
    queryKey: docExchangeKeys.downloadedDocument(documentId),
    queryFn: documentId
      ? () => api.docExchange.signUrlDownload(documentId)
      : skipToken,
  });
