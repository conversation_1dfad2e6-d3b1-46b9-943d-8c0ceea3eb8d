import { Card } from "flowbite-react";
import { useParams } from "next/navigation";
import { useState } from "react";
import { IoMdAdd } from "react-icons/io";

import { Button } from "@/components/ui/button";
import { Table, TableLoading } from "@/components/ui/table";
import type { SiteStudy } from "@/lib/apis/sites";

import { useSiteStudies } from "../hooks/use-site-studies";
import { generateColumns } from "./columns";
import { ModalAddStudy } from "./modal-add-study";
import { ModalBuildIsfTemplate } from "./modal-build-isf-template";

export const StudiesTab = () => {
  const { id } = useParams();
  const [isOpen, setIsOpen] = useState(false);

  const { data: studies, isLoading } = useSiteStudies(id as string);
  const [selectedStudyIsf, setSelectedStudyIsf] = useState<SiteStudy | null>(
    null,
  );

  const columns = generateColumns((study) => {
    setSelectedStudyIsf(study);
  });

  if (isLoading) {
    return (
      <Card className="[&>div]:p-0">
        <div className="mb-4 flex items-center justify-between p-4 pb-0 text-lg font-semibold">
          <div className="dark:text-gray-400">Studies</div>
        </div>
        <TableLoading columns={columns} />
      </Card>
    );
  }

  return (
    <>
      <Card className="[&>div]:p-0">
        <div className="mb-4 flex items-center justify-between p-4 pb-0 text-lg font-semibold">
          <div className="dark:text-gray-400">Studies</div>
          <Button variant="primary" onClick={() => setIsOpen(true)}>
            <IoMdAdd />
            Add Study
          </Button>
        </div>
        <Table columns={columns} data={studies?.results ?? []} />
      </Card>
      <ModalAddStudy isOpen={isOpen} onClose={() => setIsOpen(false)} />

      {selectedStudyIsf && (
        <ModalBuildIsfTemplate
          isOpen={true}
          onClose={() => setSelectedStudyIsf(null)}
          siteId={selectedStudyIsf.siteId}
          studyId={selectedStudyIsf.study.id}
        />
      )}
    </>
  );
};
