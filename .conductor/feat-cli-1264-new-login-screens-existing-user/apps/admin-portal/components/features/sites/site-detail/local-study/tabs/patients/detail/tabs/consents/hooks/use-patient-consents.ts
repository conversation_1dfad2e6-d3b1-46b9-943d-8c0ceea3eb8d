import { useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

export const patientConsentKeys = {
  all: () => ["patient-consents"] as const,
  allList: (patientId: string) =>
    [...patientConsentKeys.all(), "list", patientId] as const,
  list: ({
    patientId,
    params,
  }: {
    params?: MetadataParams;
    patientId: string;
  }) => [...patientConsentKeys.allList(patientId), params],

  downloadedDocument: (params: { patientId: string; consentId: string }) => [
    ...patientConsentKeys.all(),
    params,
  ],
};

export const usePatientConsents = (patientId: string) => {
  const { page, take } = usePagination();
  const params = {
    page,
    take,
  };

  return useQuery({
    queryKey: patientConsentKeys.list({ patientId, params }),
    queryFn: () => api.patients.patientConsents(patientId, params),
    placeholderData: (prev) => prev,
  });
};

export const useDownloadConsentDocument = ({
  consentId,
  patientId,
}: {
  patientId: string;
  consentId: string;
}) => {
  return useQuery({
    queryKey: patientConsentKeys.downloadedDocument({ patientId, consentId }),
    queryFn: () => api.patients.consentSignedUrlDownload(patientId, consentId),
  });
};
