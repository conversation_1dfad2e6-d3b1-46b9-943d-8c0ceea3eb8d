import { Card, ToggleSwitch } from "flowbite-react";
import { parseAsBoolean, useQueryState } from "nuqs";
import React, { useMemo, useState } from "react";

import { SearchField } from "@/components/shared";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Skeleton } from "@/components/ui/skeleton";
import { Table, TableLoading } from "@/components/ui/table";
import { cn, createRandomArray } from "@/lib/utils";

import { useLocalStudyParams } from "../../../../../hooks/useLocalStudyParams";
import {
  usePatientVisitDocuments,
  usePatientVisits,
} from "../visit/hook/use-visit-queries";
import { generateColumnsDocuments } from "./columns";
import { FilterCTMRDocuments } from "./filter-documents";

export const SMRTab = () => {
  const { patientId } = useLocalStudyParams();
  const [showArchived, setShowArchived] = useQueryState(
    "showArchived",
    parseAsBoolean.withDefault(false),
  );
  const [selectedVisitId, setSelectedVisitId] = useState("");
  // const [selectedVisitDocument, setSelectedVisitDocument] =
  //   useState<PatientVisitDocument | null>(null);

  const { data, isPending } = usePatientVisits({
    patientId,
    ignoreFilters: true,
  });
  const {
    data: documents,
    isPending: isPendingDocument,
    isPlaceholderData,
  } = usePatientVisitDocuments(selectedVisitId);

  const columns = useMemo(() => generateColumnsDocuments(), []);
  return (
    <>
      <Card className="[&>div]:p-4">
        <div className="flex flex-col justify-between gap-2 md:flex-row md:items-center">
          <h2 className=" text-2xl font-semibold dark:text-white">
            Source Medical Records
          </h2>
          <div className="flex flex-col justify-end gap-2 sm:flex-row sm:items-center">
            <div>
              <SearchField placeholder="Search documents..." />
            </div>
            <div className="flex items-center justify-end gap-2">
              <FilterCTMRDocuments />
              <ToggleSwitch
                checked={showArchived}
                onChange={(checked) => setShowArchived(checked)}
                label="Show Archived"
              />
            </div>
          </div>
        </div>
      </Card>

      <div className="mt-4 flex gap-4">
        <div className="w-full max-w-[300px] rounded-lg bg-white p-4 dark:bg-gray-800">
          <p className="mb-2 flex items-center gap-4 dark:text-white">
            <span className="text-base font-bold">Visit</span>
            <span className="font-plus-jakarta bg-primary-500 grid size-6 place-content-center rounded-full text-sm font-medium text-white">
              {data?.results.length ?? 0}
            </span>
          </p>
          <ul className="grid">
            {isPending &&
              createRandomArray().map((i) => (
                <li
                  key={i}
                  className="flex w-full justify-between rounded-lg px-2.5 py-2 transition-colors dark:text-white"
                >
                  <Skeleton className="h-6 w-32" />
                </li>
              ))}
            {data?.results.map((visit) => (
              <li
                key={visit.id}
                role="button"
                onClick={() => setSelectedVisitId(visit.id)}
                className={cn(
                  "flex w-full justify-between rounded-lg px-2.5 py-2 transition-colors dark:text-white",
                  selectedVisitId === visit.id &&
                    "bg-purple-100 shadow-sm dark:bg-purple-800",
                )}
              >
                {visit.name}
                {visit.studyArm?.name && (
                  <span
                    className={cn(
                      "h-fit whitespace-nowrap rounded-full bg-gray-50 px-2 py-1 text-xs text-gray-500 transition-colors dark:bg-gray-700 dark:text-gray-400",
                      selectedVisitId === visit.id &&
                        "bg-purple-200 text-purple-600 dark:bg-purple-700 dark:text-purple-200",
                    )}
                  >
                    {visit.studyArm?.name}
                  </span>
                )}
              </li>
            ))}
          </ul>
        </div>
        <div className="h-fit flex-1 rounded-lg bg-white dark:bg-gray-800">
          {!!selectedVisitId && isPendingDocument ? (
            <TableLoading columns={columns}></TableLoading>
          ) : (
            <LoadingWrapper isLoading={isPlaceholderData}>
              <Table
                // enableSorting
                columns={columns}
                data={documents?.results ?? []}
              />
            </LoadingWrapper>
          )}
        </div>
      </div>
      {/* {selectedVisitDocument && (
        <DocumentPreviewModal
          isOpen={!!selectedVisitDocument}
          onClose={() => setSelectedVisitDocument(null)}
          document={{
            id: selectedVisitDocument?.id,
            title: selectedVisitDocument?.title,
            extension: selectedVisitDocument?.extension,
          }}
          fetchPreviewUrl={(id) => api.visitDocuments.signedUrl(id)}
        />
      )} */}
    </>
  );
};
