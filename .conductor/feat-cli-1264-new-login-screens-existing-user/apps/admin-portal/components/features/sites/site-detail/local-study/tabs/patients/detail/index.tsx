"use client";

import { OverviewCard, OverviewItem } from "@/components/shared/overview-card";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { PageHeader } from "@/components/ui/page-header";
import { Skeleton } from "@/components/ui/skeleton";
import { TabsWrapper } from "@/components/ui/tabs-wrapper";
import { cn, formatDate } from "@/lib/utils";

import { useLocalStudyParams } from "../../../hooks/useLocalStudyParams";
import { usePatient } from "./hooks/use-patient-queries";
import { ConsentsTab } from "./tabs/consents";
import { SMRTab } from "./tabs/smr";
import { VisitTab } from "./tabs/visit";

const STATUS_COLORS = {
  preScreening: "text-teal-500 dark:text-teal-300",
  screening: "text-blue-500 dark:text-blue-300",
  enrolled: "text-green-500 dark:text-green-300",
  withdrawn: "text-orange-500 dark:text-orange-300",
  complete: "text-purple-500 dark:text-purple-300",
  screenFailed: "text-red-500 dark:text-red-300",
};

const PATIENT_TABS = [
  {
    key: "visit",
    content: <VisitTab />,
  },
  {
    key: "consents",
    content: <ConsentsTab />,
  },
  {
    key: "smr",
    title: "Source Medical Records",
    content: <SMRTab />,
  },
];

export const PatientDetailContent = () => {
  const { patientId, siteId, studyId } = useLocalStudyParams();

  const { data, isPending } = usePatient(patientId);

  const siteDetailHref = `/sites/${siteId}`;
  const breadcrumbItems = [
    { label: "Sites", href: "/sites" },
    {
      label: data?.site.name ?? "Site Detail",
      href: siteDetailHref,
      loading: isPending,
    },
    {
      label: data?.study.name ?? "Local Study",
      href: data?.studyId
        ? `${siteDetailHref}/studies/${studyId}`
        : siteDetailHref,
      loading: isPending,
    },
    {
      label: "Patients",
      href: data?.studyId
        ? `${siteDetailHref}/studies/${studyId}?tab=patients`
        : siteDetailHref,
      loading: isPending,
    },
    { label: data?.name ?? "Patient Detail", loading: isPending },
  ];
  return (
    <>
      <div className="flex flex-col gap-4">
        <Breadcrumb items={breadcrumbItems} />
        <div className="flex justify-between gap-2">
          {isPending ? (
            <PageHeader
              showBackButton
              href={`/sites/${siteId}/studies/${studyId}?tab=patients`}
            >
              <Skeleton className="h-7 w-52" />
            </PageHeader>
          ) : (
            <PageHeader
              showBackButton
              href={`/sites/${siteId}/studies/${studyId}?tab=patients`}
            >
              {data?.name}
            </PageHeader>
          )}
        </div>

        {isPending ? (
          <OverviewSkeleton />
        ) : (
          <OverviewCard title="Overview">
            <div className="grid grid-cols-2 gap-4 lg:grid-cols-3 xl:grid-cols-6">
              <OverviewItem label="ID" value={data?.name ?? "N/A"} />
              <OverviewItem label="Status">
                <span
                  className={cn(
                    "text-sm capitalize",
                    data?.status && STATUS_COLORS[data?.status],
                  )}
                >
                  {data?.status ?? "N/A"}
                </span>
              </OverviewItem>
              <OverviewItem
                label="Date of birth"
                value={
                  data?.dateOfBirth
                    ? formatDate(data.dateOfBirth, "LLL dd, yyyy")
                    : "N/A"
                }
              />
              <OverviewItem label="Sex" value={data?.sex ?? "N/A"} />
              <OverviewItem
                label="Enrollment Date"
                value={
                  data?.enrollmentDate
                    ? formatDate(data.enrollmentDate, "LLL dd, yyyy")
                    : "N/A"
                }
              />
              <OverviewItem
                label="Next Scheduled Visit"
                value={
                  data?.nextVisit?.visitDate
                    ? formatDate(data.nextVisit.visitDate, "LLL dd, yyyy")
                    : "N/A"
                }
              />
            </div>
          </OverviewCard>
        )}
        <TabsWrapper tabs={PATIENT_TABS} />
      </div>
    </>
  );
};

const OverviewSkeleton = () => (
  <OverviewCard title="Overview">
    <div className="grid grid-cols-2 gap-4 lg:grid-cols-3 xl:grid-cols-6">
      <div className="space-y-1">
        <Skeleton className="h-6 w-6" />
        <Skeleton className="h-5 w-28" />
      </div>
      <div className="space-y-1">
        <Skeleton className="h-6 w-12" />
        <Skeleton className="h-5 w-20" />
      </div>
      <div className="space-y-1">
        <Skeleton className="h-6 w-24" />
        <Skeleton className="h-5 w-20" />
      </div>
      <div className="space-y-1">
        <Skeleton className="h-6 w-7" />
        <Skeleton className="h-5 w-12" />
      </div>
      <div className="space-y-1">
        <Skeleton className="h-6 w-28" />
        <Skeleton className="h-5 w-24" />
      </div>
      <div className="space-y-1">
        <Skeleton className="h-6 w-40" />
        <Skeleton className="h-5 w-28" />
      </div>
    </div>
  </OverviewCard>
);
