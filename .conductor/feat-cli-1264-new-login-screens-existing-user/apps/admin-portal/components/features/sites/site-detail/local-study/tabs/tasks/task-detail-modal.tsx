import { Check } from "lucide-react";
import Link from "next/link";
import { FiRefreshCw } from "react-icons/fi";
import { LuPencilLine } from "react-icons/lu";
import { MdDelete } from "react-icons/md";

import { TaskPriorityBadge } from "@/components/ui/badges/task-priority-badge";
import { TaskStatusBadge } from "@/components/ui/badges/task-status-badge";
import { Button } from "@/components/ui/button";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { Skeleton } from "@/components/ui/skeleton";
import { useDisclosure } from "@/hooks/use-disclosure";
import { Activity } from "@/lib/apis/tasks";
import { formatDate } from "@/lib/utils";

import { useLocalStudyParams } from "../../hooks/useLocalStudyParams";
import { useTaskModals } from "./hooks/use-task-modals-store";
import { useUpdateTask } from "./hooks/use-task-mutations";
import { useStudyTask, useTaskActivities } from "./hooks/use-tasks-queries";
import { ReassignTaskModal } from "./reassign-task-modal";

export const TaskDetailModal = () => {
  const { studyId } = useLocalStudyParams();
  const {
    isOpenTaskDetailModal,
    onCloseTaskDetailModal,
    selectedTaskId,
    onOpenTaskModal,
    onOpenConfirmDeleteModal,
  } = useTaskModals();

  const { data, isPending } = useStudyTask({
    enabled: isOpenTaskDetailModal,
    taskId: selectedTaskId,
  });
  const { mutateAsync: updateTask, isPending: isUpdating } = useUpdateTask({
    studyId,
    taskId: selectedTaskId,
  });
  const { open, close, isOpen } = useDisclosure();

  const { data: activities, isPending: isPendingActivities } =
    useTaskActivities({
      enabled: isOpenTaskDetailModal,
      taskId: selectedTaskId,
    });

  const handleMarkAsComplete = async () => {
    if (!data) return;
    await updateTask({
      ...data,
      status: "completed",
    });
  };

  const renderTitle = () => {
    return isPending ? (
      <TitleSkeleton />
    ) : (
      <div className="flex items-center gap-2.5">
        <TaskStatusBadge variant={data?.status || "notStarted"} />
        <TaskPriorityBadge variant={data?.priority || "low"} />
      </div>
    );
  };

  return (
    <>
      <WrapperModal
        size="3xl"
        isOpen={isOpenTaskDetailModal}
        onClose={onCloseTaskDetailModal}
        title={renderTitle()}
      >
        <div className="space-y-5">
          {isPending ? (
            <MainContentSkeleton />
          ) : (
            <>
              <div className="flex flex-col gap-2 sm:flex-row">
                <div className="flex-1 space-y-2">
                  <h3 className="text-lg font-bold sm:text-2xl dark:text-white">
                    {data?.name}
                  </h3>
                  <div className="grid flex-1 grid-cols-3 gap-4 dark:text-white">
                    {/* Assigned To */}
                    <div className="col-span-1 text-xs leading-6">
                      Assigned To
                    </div>
                    <div className="font-inter col-span-2 text-[16px] text-base font-semibold leading-6">
                      {data?.assignedTo?.profile ? (
                        `${data.assignedTo?.profile?.user.firstName}
                  ${data.assignedTo?.profile?.user.lastName}`
                      ) : (
                        <span className="text-sm font-normal text-gray-400">
                          Unassigned
                        </span>
                      )}
                    </div>

                    {/* Due Date */}
                    <div className="col-span-1 text-xs leading-6">Due Date</div>
                    <div className="font-inter col-span-2 text-[16px] text-base font-semibold leading-6">
                      {data?.dueDate ? (
                        formatDate(new Date(data.dueDate), "MMM dd, yyyy")
                      ) : (
                        <span className="text-sm font-normal text-gray-400">
                          N/A
                        </span>
                      )}
                    </div>

                    {/* Related Study */}
                    <div className="col-span-1 text-xs leading-6">
                      Related Study
                    </div>
                    <div className="col-span-2 text-base leading-6">
                      {data?.study?.name ? (
                        <Link
                          href={`/studies/${data.study?.id}`}
                          className="text-primary-500 cursor-pointer gap-1 font-medium hover:underline"
                        >
                          {data.study?.name}
                        </Link>
                      ) : (
                        <span className="text-gray-400">N/A</span>
                      )}
                    </div>

                    {/* Related Patient */}
                    <div className="col-span-1 text-xs leading-6">
                      Related Patient
                    </div>
                    <div className="col-span-2 text-base leading-6">
                      {data?.patient?.firstName || data?.patient?.lastName ? (
                        <Link
                          href={`/patients/${data.patient?.id}`}
                          className="text-primary-500 cursor-pointer gap-1 font-medium hover:underline"
                        >
                          {data.patient?.firstName} {data.patient?.lastName}
                        </Link>
                      ) : (
                        <span className="text-gray-400">N/A</span>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex flex-col justify-end gap-2">
                  <span className="text-xs dark:text-white">Actions</span>
                  {
                    data && data.status === "inProgress" && (
                      <Button
                        variant="outline"
                        className="flex w-full justify-start border-green-400 px-5 py-2.5 text-green-400 hover:!bg-green-400 hover:text-white dark:border-green-500 dark:text-green-500 dark:hover:!bg-green-500 dark:hover:text-white"
                        onClick={handleMarkAsComplete}
                        disabled={isPending}
                        isLoading={isUpdating}
                      >
                        <Check size={24} />
                        <span>Mark Complete</span>
                      </Button>
                    )
                    // : (
                    //   <Tooltip content="Task is already completed">
                    //     <Button
                    //       variant="outline"
                    //       className="flex w-full justify-start border-green-400 px-5 py-2.5 text-green-400 hover:!bg-inherit dark:border-green-500 dark:text-green-500"
                    //       disabled
                    //     >
                    //       <Check size={24} />
                    //       <span>Mark Complete</span>
                    //     </Button>
                    //   </Tooltip>
                    // )
                  }

                  <Button
                    variant="outline"
                    className="flex w-full justify-start border-orange-400 px-5 py-2.5 text-orange-400 hover:!bg-orange-400 hover:text-white dark:border-orange-500 dark:text-orange-500 dark:hover:!bg-orange-500 dark:hover:text-white"
                    onClick={open}
                  >
                    <FiRefreshCw size={24} />
                    <span>Reassign Task</span>
                  </Button>

                  <Button
                    variant="outline"
                    className="flex w-full justify-start border-blue-500 px-5 py-2.5 text-blue-500 hover:!bg-blue-500 hover:text-white dark:border-blue-600 dark:text-blue-400 dark:hover:!bg-blue-600 dark:hover:text-white"
                    onClick={() =>
                      onOpenTaskModal({
                        task: data,
                      })
                    }
                  >
                    <LuPencilLine size={24} />
                    <span>Edit Task</span>
                  </Button>

                  <Button
                    variant="outline"
                    className="flex w-full justify-start border-red-500 px-5 py-2.5 text-red-500 hover:!bg-red-500 hover:text-white dark:border-red-600 dark:text-red-400 dark:hover:!bg-red-600 dark:hover:text-white"
                    onClick={() => onOpenConfirmDeleteModal(data?.id as string)}
                  >
                    <MdDelete size={24} />
                    Delete
                  </Button>
                </div>
              </div>
              <div className="flex flex-col dark:text-white">
                <span className="text-xs leading-6">Description</span>
                <span className="text-base leading-6">{data?.description}</span>
              </div>
            </>
          )}
          <div className="max-h-[30vh] space-y-4 overflow-auto">
            {isPendingActivities
              ? Array(3)
                  .fill(1)
                  .map((_, idx) => <ActivitySkeleton key={idx} />)
              : activities?.results.map((activity) => (
                  <ActivityItem key={activity.id} activity={activity} />
                ))}
          </div>
        </div>
      </WrapperModal>
      <ReassignTaskModal isOpen={isOpen} onClose={close} />
    </>
  );
};

const ActivityItem = ({ activity }: { activity: Activity }) => (
  <div className="flex items-start gap-3">
    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100 text-base font-medium uppercase text-gray-900">
      {activity.profile?.user.firstName?.[0]}
      {activity.profile?.user.lastName?.[0]}
    </div>
    <div className="flex-1">
      <div className="flex items-center gap-2">
        <span className="text-sm font-medium text-gray-900 dark:text-white">
          {activity.profile?.user.firstName} {activity.profile?.user.lastName}
        </span>
        <span className="text-sm font-normal text-gray-500">
          {formatDate(new Date(activity.createdDate), "MMM dd, yyyy h:mm a")}
        </span>
      </div>
      <p className="text-sm font-normal text-gray-900 dark:text-white">
        {activity.description}
      </p>
    </div>
  </div>
);

const ActivitySkeleton = () => (
  <div className="flex items-start gap-3">
    <Skeleton className="size-8 rounded-full" />
    <div className="flex-1 space-y-1">
      <Skeleton className="h-5 w-60" />
      <Skeleton className="h-5 w-32" />
    </div>
  </div>
);

const TitleSkeleton = () => (
  <div className="flex items-center gap-2.5">
    <Skeleton className="h-[21px] w-24" />
    <Skeleton className="h-[21px] w-20" />
  </div>
);

const MainContentSkeleton = () => (
  <div className="space-y-5">
    <div className="flex flex-col gap-2 sm:flex-row">
      <div className="flex-1 space-y-2">
        <Skeleton className="h-7 w-56 sm:h-8" />
        <div className="grid flex-1 grid-cols-3 gap-4">
          {/* Assigned To */}
          <Skeleton className="h-6 w-16" />
          <div className="font-inter col-span-2 text-[16px] text-base font-semibold leading-6">
            <Skeleton className="h-6 w-28" />
          </div>

          {/* Due Date */}
          <Skeleton className="h-6 w-14" />

          <div className="font-inter col-span-2 text-[16px] text-base font-semibold leading-6">
            <Skeleton className="h-6 w-28" />
          </div>

          {/* Related Study */}
          <Skeleton className="h-6 w-24" />

          <div className="col-span-2 text-base leading-6">
            <Skeleton className="h-6 w-20" />
          </div>

          <Skeleton className="h-6 w-20" />
          <div className="col-span-2 text-base leading-6">
            <Skeleton className="h-6 w-24" />
          </div>
        </div>
      </div>

      <div className="flex flex-col justify-end gap-2">
        <Skeleton className="h-4 w-11" />
        <Skeleton className="h-[46px] w-full sm:w-[200px]" />
        <Skeleton className="h-[46px] w-full sm:w-[200px]" />
        <Skeleton className="h-[46px] w-full sm:w-[200px]" />
        <Skeleton className="h-[46px] w-full sm:w-[200px]" />
      </div>
    </div>
    <div className="space-y-1">
      <Skeleton className="h-6 w-16" />
      <Skeleton className="h-6 w-full" />
    </div>
  </div>
);
