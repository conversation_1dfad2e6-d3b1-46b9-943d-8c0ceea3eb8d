import { useMemo } from "react";
import { z } from "zod";

import { <PERSON><PERSON>, <PERSON>Button } from "@/components/ui/button";
import { Form, InputField, Select } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { DocExchangeDocument } from "@/lib/apis/doc-exchange/types";

import { useUpdateDocument } from "./hooks/use-doc-exchange-mutations";
import { useFolders } from "./hooks/use-doc-exchange-queries";
import { useFilterDocuments } from "./hooks/use-filter-documents";

const schema = z.object({
  title: z
    .string({
      invalid_type_error: "File name is required",
      required_error: "File name is required",
    })
    .min(1, { message: "File name is required" }),
  parentDirectoryId: z
    .string({
      invalid_type_error: "Parent folder is required",
      required_error: "Parent folder is required",
    })
    // .min(1, { message: "Parent folder is required" })
    .optional(),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedFile: DocExchangeDocument;
};

export const UpdateDocumentModal = function ({
  isOpen,
  onClose,
  selectedFile,
}: Props) {
  const { siteId } = useFilterDocuments();

  const { data: folders } = useFolders(siteId);

  const { mutateAsync: updatePlaceholder, isPending: isUpdating } =
    useUpdateDocument(siteId);

  const folderOptions = useMemo(() => {
    return (folders ?? []).map((folder) => ({
      value: folder.id,
      label: folder.name,
    }));
  }, [folders]);

  const onSubmit = async (data: z.infer<typeof schema>) => {
    await updatePlaceholder({
      ...data,
      parentDirectoryId: data.parentDirectoryId || null,
      documentId: selectedFile.id,
    });
    onClose();
  };

  return (
    <WrapperModal
      isOpen={isOpen}
      onClose={onClose}
      title={`${selectedFile ? "Update" : "Create"} Document`}
    >
      <Form
        mode="onChange"
        schema={schema}
        onSubmit={onSubmit}
        defaultValues={{
          title: selectedFile?.title || "",
          parentDirectoryId: selectedFile?.parentDirectoryId,
        }}
        className="space-y-4"
      >
        <div className="flex flex-col gap-2">
          <Label htmlFor="title">File Name</Label>
          <InputField
            id="title"
            name="title"
            placeholder="Enter file name..."
          />
        </div>
        <div className="flex flex-col gap-2">
          <Label htmlFor="parentDirectoryId">Parent Folder</Label>
          <Select
            id="parentDirectoryId"
            name="parentDirectoryId"
            placeholder="Select a parent folder..."
            options={folderOptions}
          />
        </div>

        <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
          <CloseButton onClose={onClose} />
          <Button
            type="submit"
            variant="primary"
            disabled={isUpdating}
            isLoading={isUpdating}
          >
            Save
          </Button>
        </div>
      </Form>
    </WrapperModal>
  );
};
