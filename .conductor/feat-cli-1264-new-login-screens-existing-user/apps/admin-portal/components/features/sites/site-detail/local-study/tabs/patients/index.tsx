import { Card, ToggleSwitch } from "flowbite-react";
import React from "react";

import { SearchField } from "@/components/shared";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { UncontrolledSelect } from "@/components/ui/form/select/uncontrolled-select";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";

import { useLocalStudyParams } from "../../hooks/useLocalStudyParams";
import { patientColumns } from "./columns";
import { usePatientFilters } from "./hooks/use-patient-filter";
import { useLocalStudyPatients } from "./hooks/use-patient-queries";

const PATIENT_STATUSES = [
  { value: "preScreening", label: "Pre-Screening" },
  { value: "screening", label: "Screening" },
  { value: "enrolled", label: "Enrolled" },
  { value: "withdrawn", label: "Withdrawn" },
  { value: "complete", label: "Complete" },
  { value: "screenFailed", label: "Screen Failed" },
];

export const PatientsTab = () => {
  const { active, archived, status, setStatus, setActive, setArchived } =
    usePatientFilters();

  const { siteId, studyId } = useLocalStudyParams();
  const { data, isPending, isPlaceholderData } = useLocalStudyPatients({
    siteId,
    studyId,
  });

  return (
    <Card className="[&>div]:p-0">
      <div className="p-4">
        <h2 className="mb-2 text-2xl font-semibold sm:mb-0 dark:text-white">
          Patients
        </h2>
        <div className="flex flex-col justify-end gap-4 sm:flex-row sm:items-center">
          <SearchField placeholder="Search..." className="sm:max-w-52" />
          <UncontrolledSelect
            className="min-w-40"
            options={PATIENT_STATUSES}
            placeholder="Select a status"
            value={status || ""}
            onChange={(value) => {
              setStatus(value || null);
            }}
          />

          <div className="flex items-center gap-4">
            <ToggleSwitch
              onChange={(checked) => setActive(checked || null)}
              checked={active || false}
              label="Active"
            />
            <ToggleSwitch
              onChange={(checked) => setArchived(checked || null)}
              checked={archived || false}
              label="Archived"
            />
          </div>
        </div>
      </div>
      {isPending ? (
        <TableLoading columns={patientColumns} />
      ) : (
        <LoadingWrapper isLoading={isPlaceholderData}>
          <Table
            columns={patientColumns}
            data={data?.results ?? []}
            enableSorting
          />
        </LoadingWrapper>
      )}
      {data?.metadata && <TableDataPagination metadata={data.metadata} />}
    </Card>
  );
};
