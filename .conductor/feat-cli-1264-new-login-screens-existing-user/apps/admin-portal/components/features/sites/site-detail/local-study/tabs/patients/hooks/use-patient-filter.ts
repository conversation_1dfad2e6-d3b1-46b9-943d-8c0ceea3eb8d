import { parseAsBoolean, parseAsString, useQueryState } from "nuqs";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import { useSort } from "@/hooks/use-sort";

export const usePatientFilters = () => {
  const { page, take } = usePagination();
  const { search } = useSearch();
  const [status, setStatus] = useQueryState("status", parseAsString);
  const [active, setActive] = useQueryState("active", parseAsBoolean);
  const [archived, setArchived] = useQueryState("archived", parseAsBoolean);
  const { orderBy, orderDirection, changeSort } = useSort();

  return {
    page,
    take,
    search,
    status,
    setStatus,
    active,
    setActive,
    archived,
    setArchived,
    orderBy,
    orderDirection,
    changeSort,
  };
};
