"use client";

import { Card } from "flowbite-react";
import { useMemo, useState } from "react";
import { IoMdAdd } from "react-icons/io";

import { generateActivePermissionColumns } from "@/components/features/users/user-detail/tabs/profiles/columns";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Button } from "@/components/ui/button";
import { TableLoading } from "@/components/ui/table";
import { TableData } from "@/components/ui/table/table";
import { GroupProfile } from "@/lib/apis/groups/types";

import { useDeleteUserCustomPermission } from "../hooks/use-users-mutations";
import { useUserCustomPermission } from "../hooks/use-users-queries";
import { ModalAddStudy } from "./add-study-modal";

export const ActivePermissions = ({
  selectedProfile,
  setSelectedProfile,
}: {
  setSelectedProfile: React.Dispatch<React.SetStateAction<GroupProfile | null>>;
  selectedProfile: GroupProfile;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const { data, isPending } = useUserCustomPermission(
    selectedProfile.profile.id,
  );
  const { mutateAsync, isPending: isRemoving } =
    useDeleteUserCustomPermission();

  const columns = useMemo(
    () =>
      generateActivePermissionColumns((studyId: string) => {
        mutateAsync(
          {
            studyId,
            userId: selectedProfile.profile.userId,
            profileId: selectedProfile.profile.id,
          },
          {
            onSuccess: (response) => {
              setSelectedProfile((prev) =>
                prev
                  ? {
                      ...prev,
                      profile: {
                        ...prev.profile,
                        hasCustomPermissions:
                          response.hasCustomStudyPermissions,
                      },
                    }
                  : null,
              );
            },
          },
        );
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [mutateAsync],
  );

  return (
    <Card className="[&>div]:p-0">
      <div className="mb-4 flex items-center justify-between p-4 pb-0 text-lg font-semibold">
        <div className="dark:text-gray-400">Studies</div>
        <Button variant="primary" onClick={() => setIsOpen(true)}>
          <IoMdAdd />
          Add Study
        </Button>
      </div>

      {isPending ? (
        <TableLoading columns={columns} />
      ) : (
        <LoadingWrapper isLoading={isRemoving}>
          <TableData columns={columns} data={data?.accessibleStudies ?? []} />
        </LoadingWrapper>
      )}

      <ModalAddStudy
        isOpen={isOpen}
        selectedProfile={selectedProfile}
        setSelectedProfile={setSelectedProfile}
        onClose={() => setIsOpen(false)}
      />
    </Card>
  );
};
