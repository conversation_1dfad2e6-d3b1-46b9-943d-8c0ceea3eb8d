import { useParams } from "next/navigation";
import React from "react";

import { OverviewCard, OverviewItem } from "@/components/shared/overview-card";
import { StudyStatusBadge } from "@/components/ui/badges";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { useDisclosure } from "@/hooks/use-disclosure";

import { useLocalStudy } from "../../hooks/use-local-study-queries";
import { ModalEditStudy } from "./modal-edit-study";

export const OverviewTab = () => {
  const params = useParams();
  const siteId = params.id as string;
  const studyId = params.studyId as string;

  const { open, close, isOpen } = useDisclosure();

  const { data, isPending } = useLocalStudy({
    siteId,
    studyId,
  });

  return (
    <>
      {isPending ? (
        <OverviewSkeleton />
      ) : (
        <OverviewCard
          rightContent={
            <Button onClick={open} variant="primary">
              Edit Study
            </Button>
          }
          title="Overview"
        >
          <div className="grid grid-cols-2 gap-4 lg:grid-cols-3 xl:grid-cols-4">
            <OverviewItem
              label="Study name"
              value={data?.study.name ?? "N/A"}
            />
            <OverviewItem label="Site name" value={data?.site.name ?? "N/A"} />
            <OverviewItem label="Status">
              <StudyStatusBadge status={data?.status as string} />
            </OverviewItem>
            <OverviewItem
              label="Study code"
              value={data?.study.studyCode ?? "N/A"}
            />
            <OverviewItem
              label="Patient target"
              value={data?.patientTarget.toString() ?? "N/A"}
            />
            <OverviewItem
              label="Protocol"
              value={data?.protocol?.name ?? "N/A"}
            />
            <OverviewItem
              label="Principal Investigator"
              value={data?.principalInvestigator ?? "N/A"}
            />
            <OverviewItem
              label="Sub Investigator"
              value={data?.subInvestigator ?? "N/A"}
            />
            <OverviewItem
              label="Site number"
              value={data?.siteNumber ?? "N/A"}
            />
          </div>
        </OverviewCard>
      )}

      <ModalEditStudy isOpen={isOpen} onClose={close} />
    </>
  );
};

const OverviewSkeleton = () => (
  <OverviewCard
    rightContent={
      <Button disabled variant="primary">
        Edit Study
      </Button>
    }
    title="Overview"
  >
    <div className="grid grid-cols-2 gap-4 lg:grid-cols-3 xl:grid-cols-4">
      <div className="space-y-1">
        <Skeleton className="h-6 w-24" />
        <Skeleton className="h-5 w-36" />
      </div>
      <div className="space-y-1">
        <Skeleton className="h-6 w-20" />
        <Skeleton className="h-5 w-28" />
      </div>
      <div className="space-y-1">
        <Skeleton className="h-6 w-14" />
        <Skeleton className="h-5 w-16" />
      </div>
      <div className="space-y-1">
        <Skeleton className="h-6 w-24" />
        <Skeleton className="h-5 w-28" />
      </div>
      <div className="space-y-1">
        <Skeleton className="h-6 w-28" />
        <Skeleton className="h-5 w-8" />
      </div>
      <div className="space-y-1">
        <Skeleton className="h-6 w-16" />
        <Skeleton className="h-5 w-32" />
      </div>
      <div className="space-y-1">
        <Skeleton className="h-6 w-40" />
        <Skeleton className="h-5 w-44" />
      </div>
      <div className="space-y-1">
        <Skeleton className="h-6 w-32" />
        <Skeleton className="h-5 w-40" />
      </div>
      <div className="space-y-1">
        <Skeleton className="h-6 w-24" />
        <Skeleton className="h-5 w-10" />
      </div>
    </div>
  </OverviewCard>
);
