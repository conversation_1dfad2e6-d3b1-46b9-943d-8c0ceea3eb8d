import { useDraggable } from "@dnd-kit/core";
import { ColumnDef } from "@tanstack/react-table";
import { GripVertical } from "lucide-react";
import { LuFileClock } from "react-icons/lu";

import { DOCUMENT_ICONS } from "@/components/icons/doc-icons";
import {
  TableEditButton,
  TableGenericButton,
  TableRemoveButton,
} from "@/components/shared/table-action-buttons";
import { ActivityBadge } from "@/components/ui/badges/activity-badge";
import {
  DocumentType,
  DocumentTypeBadge,
} from "@/components/ui/badges/document-type-badge";
import {
  DocumentStatus,
  DocumentStatusBadge,
} from "@/components/ui/badges/ebinder-document-badge";
import { Document } from "@/lib/apis/essential-document-files/types";
import {
  EBinderActivity,
  EBinderDocumentVersion,
} from "@/lib/apis/essential-document-versions/types";
import { cn, formatDate } from "@/lib/utils";

export const generateDocumentColumns = ({
  onEdit,
  isShowDetailColumns,
  onViewAuditLogs,
  onDelete,
  // onPreview,
}: {
  onEdit: (file: Document) => void;
  onDelete: (file: Document) => void;
  // onPreview: (file: Document) => void;
  onViewAuditLogs: (file: Document) => void;

  isShowDetailColumns?: boolean;
}): ColumnDef<Document>[] => [
  {
    id: "drag-handle",
    header: "",
    cell: function DragHandler({ row }) {
      const { attributes, listeners, setNodeRef, isDragging } = useDraggable({
        id: `${row.original.id}`,
        data: {
          ...row.original,
          type: "document",
        },
      });

      return (
        <div
          ref={setNodeRef}
          {...attributes}
          {...listeners}
          className="grid cursor-grab place-content-center"
          style={{ opacity: isDragging ? 0.5 : 1 }}
        >
          <GripVertical size={20} className="text-gray-500" />
        </div>
      );
    },
    enableSorting: false,
    meta: { width: "40px" },
  },

  {
    header: "Type",
    accessorKey: "extension",
    cell: ({ row }) => {
      const extension = row.original.currentVersion?.fileRecord.extension;

      if (!extension)
        return <span className="whitespace-nowrap text-gray-400">N/A</span>;

      return (
        <div className="whitespace-nowrap">
          <DocumentTypeBadge type={extension} />
        </div>
      );
    },
  },
  ...(isShowDetailColumns
    ? ([
        {
          header: "Document ID",
          accessorKey: "id",
          cell: ({ row }) => {
            return (
              <span className="whitespace-nowrap">
                {row.original.id || <span className="text-gray-400">N/A</span>}
              </span>
            );
          },
        },
      ] as ColumnDef<Document>[])
    : []),
  {
    header: "Name",
    accessorKey: "title",
    cell: ({ row }) => {
      const icon =
        DOCUMENT_ICONS[
          row.original.currentVersion?.fileRecord.extension as Exclude<
            DocumentType,
            "default"
          >
        ];

      const status =
        row.original?.isfStatus?.name || row.original.tmfStatus?.name;

      if (status === "placeholder") {
        return (
          <span className="text-gray-400/75 dark:text-gray-500">
            {row.original.title}
          </span>
        );
      }

      return (
        <div className="flex cursor-pointer items-center gap-2 whitespace-nowrap">
          {icon}
          {row.original.title}
        </div>
      );
    },
  },
  {
    header: "Artifact #",
    accessorKey: "artifactNumber",
    cell: ({ row }) => {
      return (
        <span
          className={cn(
            "whitespace-nowrap",
            row.original.category?.artifactNumber && "text-gray-400",
          )}
        >
          {row.original.category?.artifactNumber || "N/A"}
        </span>
      );
    },
  },
  {
    header: "Status",
    accessorKey: "status",
    cell: ({ row }) => {
      return (
        <div className="whitespace-nowrap">
          <DocumentStatusBadge
            status={
              (row.original?.isfStatus?.name ||
                row.original.tmfStatus?.name) as DocumentStatus
            }
          />
        </div>
      );
    },
  },
  {
    header: "Created Date",
    accessorKey: "createdDate",
    cell: ({ row }) => (
      <span className="whitespace-nowrap">
        {formatDate(row.original.createdDate)}
      </span>
    ),
  },
  ...(isShowDetailColumns
    ? ([
        {
          header: "Expiration",
          accessorKey: "expiryDate",
          cell: ({ row }) => {
            return row.original.expiryDate ? (
              <span className="whitespace-nowrap">
                {formatDate(row.original.expiryDate)}
              </span>
            ) : (
              <span className="whitespace-nowrap text-gray-400">N/A</span>
            );
          },
        },
      ] as ColumnDef<Document>[])
    : []),
  {
    header: "Actions",
    accessorKey: "actions",
    enableSorting: false,
    cell: ({ row }) => {
      const document = row.original;
      return (
        <div className="flex gap-x-4">
          {/* <TableViewButton
            type="button"
            onClick={() => {
              onPreview(document);
            }}
          /> */}
          <TableEditButton type="button" onClick={() => onEdit(document)} />
          <TableGenericButton
            type="button"
            onClick={() => {
              onViewAuditLogs(document);
            }}
          >
            <LuFileClock className="size-4" /> Audit
          </TableGenericButton>
          <TableRemoveButton
            onClick={() => {
              onDelete(document);
            }}
          />
        </div>
      );
    },
  },
];

export const auditLogColumns: ColumnDef<EBinderActivity>[] = [
  {
    header: "Action",
    accessorKey: "action",
    cell: ({ row }) => <ActivityBadge activity={row.original.action} />,
  },
  {
    header: "User Name",
    accessorKey: "profile.user.firstName",
    cell: ({ row }) => (
      <span>
        {row.original.profile?.user?.firstName}{" "}
        {row.original.profile?.user?.lastName}
      </span>
    ),
  },
  {
    header: "Date",
    accessorKey: "createdDate",
    cell: ({ row }) => (
      <span>{formatDate(row.original.createdDate, "yyyy-MM-dd")}</span>
    ),
  },
];

export const generateVersionColumns = ({
  onView,
}: {
  onView: (version: EBinderDocumentVersion) => void;
}): ColumnDef<EBinderDocumentVersion>[] => [
  {
    header: "Ver",
    accessorKey: "versionNumber",
    cell: ({ row }) => (
      <span
        onClick={() => onView(row.original)}
        className="cursor-pointer font-semibold text-purple-500 underline-offset-4 hover:underline"
      >
        {row.original?.versionNumber?.join(".")}
      </span>
    ),
  },
  {
    header: "Date",
    accessorKey: "createdDate",
    cell: ({ row }) =>
      row.original?.createdDate
        ? formatDate(row.original?.createdDate ?? "")
        : null,
  },
  {
    header: "Comments",
    accessorKey: "comments",
    cell: ({ row }) => {
      return row.original?.comments.map((comment) => (
        <span key={comment.id} className="block">
          {comment.text}
        </span>
      ));
    },
  },
];
