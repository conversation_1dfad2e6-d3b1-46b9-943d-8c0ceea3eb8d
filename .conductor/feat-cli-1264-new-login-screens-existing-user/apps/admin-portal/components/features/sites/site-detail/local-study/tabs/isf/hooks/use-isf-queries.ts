import { skipToken, useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { endOfDay, parse, startOfDay } from "date-fns";

import api from "@/lib/apis";
import {
  MetadataParams,
  OrderDirection,
  SiteStudyParams,
} from "@/lib/apis/types";

import { useFilterDocuments } from "./use-filter-documents";

export const isfKeys = {
  all: () => ["isf"] as const,

  zoneList: () => [...isfKeys.all(), "zones"] as const,
  sectionList: (zone: { zone?: string }) =>
    [...isfKeys.all(), "sections", zone] as const,
  artifactList: (params: { zone?: string; section?: string }) =>
    [...isfKeys.all(), "artifacts", params] as const,

  allFolderLists: () => [...isfKeys.all(), "folders"] as const,
  folderList: (params: { siteId: string; studyId: string }) =>
    [...isfKeys.allFolderLists(), params] as const,

  allDocumentLists: () => [...isfKeys.all(), "documents"] as const,
  documentList: (
    params: SiteStudyParams & {
      filter?: Record<string, any>;
      orderBy?: string;
      orderDirection?: string;
      page: number | null;
      take: number | null;
    },
  ) => [...isfKeys.allDocumentLists(), params] as const,

  allDocumentDetails: () => [...isfKeys.all(), "details"] as const,
  documentDetail: (id?: string) =>
    [...isfKeys.allDocumentDetails(), id] as const,

  allTemplateLists: () => [...isfKeys.all(), "templates"],
  templateList: (search: string) => [...isfKeys.allTemplateLists(), search],

  allAuditLogLists: (id: string) => [...isfKeys.all(), "audit-logs", id],
  auditLogList: ({ id, params }: { params?: MetadataParams; id: string }) => [
    ...isfKeys.allAuditLogLists(id),
    params,
  ],

  allDocumentStatuses: () => [...isfKeys.all(), "document-status"],
  documentStatuses: (id?: string) => [...isfKeys.allDocumentStatuses(), id],

  allDocumentVersions: () => [...isfKeys.all(), "document-versions"],
  documentVersions: (id?: string) => [...isfKeys.allDocumentVersions(), id],

  allDownloadedDocument: () => [...isfKeys.all(), "downloaded-document"],
  downloadedDocument: (id?: string) => [...isfKeys.allDownloadedDocument(), id],
};

export const useInfiniteTemplates = (search: string, initialPageSize = 10) => {
  return useInfiniteQuery({
    queryKey: isfKeys.templateList(search),
    queryFn: ({ pageParam = 1 }) =>
      api.isfTemplates.getTemplates({
        page: pageParam,
        take: initialPageSize,
        filter: { name: search, isActive: true },
      }),
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};

export const useIsfZones = () => {
  return useQuery({
    queryKey: isfKeys.zoneList(),
    queryFn: () => api.artifactCategories.getArtifactZones(),
  });
};

export const useIsfSections = (zone?: string) => {
  return useQuery({
    queryKey: isfKeys.sectionList({
      zone,
    }),
    queryFn: zone
      ? () => api.artifactCategories.getArtifactSections(zone!)
      : skipToken,
  });
};

export const useIsfArtifacts = (zone?: string, section?: string) => {
  return useQuery({
    queryKey: isfKeys.artifactList({
      zone,
      section,
    }),
    queryFn:
      zone && section
        ? () => api.artifactCategories.getArtifacts(zone!, section!)
        : skipToken,
  });
};

export const useFolders = (studyId: string, siteId: string) => {
  return useQuery({
    queryKey: isfKeys.folderList({ siteId, studyId }),
    queryFn: () => api.isfFolders.folders(studyId, siteId),
  });
};

export const useDocuments = () => {
  const {
    siteId,
    studyId,
    search,
    folderId,
    page,
    take,
    statuses,
    extension,
    fromCreatedDate,
    toCreatedDate,
    orderBy,
    orderDirection: orderDirection,
    isShowPlaceholder,
  } = useFilterDocuments();

  const fromCreatedDateUTC = fromCreatedDate
    ? startOfDay(parse(fromCreatedDate, "M/d/yyyy", new Date())).toISOString()
    : undefined;
  const toCreatedDateUTC = toCreatedDate
    ? endOfDay(parse(toCreatedDate, "M/d/yyyy", new Date())).toISOString()
    : undefined;

  const filterParams = {
    title: search,
    statuses: statuses,
    extension: extension,
    fromCreatedDate: fromCreatedDateUTC,
    toCreatedDate: toCreatedDateUTC,
    parentDirectoryId: folderId,
    isShowPlaceholder:
      isShowPlaceholder === true && !statuses ? "true" : undefined,
    // isExpand: true,
  };

  const params = {
    siteId,
    studyId,
    page,
    take,
    orderBy: orderBy || undefined,
    orderDirection: orderDirection?.toLocaleUpperCase() as OrderDirection,
    filter: filterParams,
  };

  return useQuery({
    queryKey: isfKeys.documentList(params),
    queryFn: () => api.essentialDocumentFiles.getListDocuments(params),
    placeholderData: (prev) => prev,
  });
};

export const useDocument = (id?: string) =>
  useQuery({
    queryKey: isfKeys.documentDetail(id),
    queryFn: id ? () => api.essentialDocumentFiles.getDocument(id) : skipToken,
  });

export const useAuditLogs = ({ page, id }: { id?: string; page: number }) => {
  const params = {
    page,
  };
  return useQuery({
    queryKey: id ? isfKeys.auditLogList({ id, params }) : [],
    queryFn: id
      ? () =>
          api.essentialDocumentVersions.getDocumentActivities({ id, params })
      : skipToken,
    placeholderData: (prev) => prev,
  });
};

export const useDocumentStatus = (id?: string) =>
  useQuery({
    queryKey: isfKeys.documentStatuses(id),
    queryFn: id ? () => api.artifactStatus.getValidStates(id) : skipToken,
  });

export const useDocumentVersions = (id?: string) =>
  useQuery({
    queryKey: isfKeys.documentVersions(id),
    queryFn: id
      ? () => api.essentialDocumentFiles.getDocumentVersions(id)
      : skipToken,
  });

export const useDownloadDocument = (documentId?: string) =>
  useQuery({
    queryKey: isfKeys.downloadedDocument(documentId),
    queryFn: documentId
      ? () => api.essentialDocuments.signUrlDownload(documentId)
      : skipToken,
  });
