import { Card } from "flowbite-react";
import React, { useMemo } from "react";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";

import { useLocalStudyParams } from "../../../../../hooks/useLocalStudyParams";
import { generateColumnsConsents } from "./columns";
import { usePatientConsents } from "./hooks/use-patient-consents";

export const ConsentsTab = () => {
  const { patientId } = useLocalStudyParams();
  // const [selectedConsent, setSelectedConsent] = useState<Consent | null>(null);

  const columns = useMemo(() => generateColumnsConsents(), []);
  const { data, isPending, isPlaceholderData } = usePatientConsents(patientId);
  return (
    <>
      <Card className="[&>div]:p-0">
        <h2 className="p-4 text-2xl font-semibold dark:text-white">Consents</h2>
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <LoadingWrapper isLoading={isPlaceholderData}>
            <Table columns={columns} enableSorting data={data?.results ?? []} />
            {data?.metadata && <TableDataPagination metadata={data.metadata} />}
          </LoadingWrapper>
        )}
      </Card>
      {/* {!!selectedConsent && (
        <DocumentPreviewModal
          isOpen={!!selectedConsent}
          onClose={() => setSelectedConsent(null)}
          document={{
            id: selectedConsent?.id,
            title: selectedConsent?.title,
            extension: selectedConsent?.sourceDocument.extension,
          }}
          fetchPreviewUrl={(id) =>
            api.patients.consentSignedUrlDownload(patientId, id)
          }
        />
      )} */}
    </>
  );
};
