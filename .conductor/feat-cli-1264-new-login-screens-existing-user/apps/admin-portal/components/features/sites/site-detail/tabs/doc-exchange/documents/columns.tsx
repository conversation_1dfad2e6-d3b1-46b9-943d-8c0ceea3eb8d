import { useDraggable } from "@dnd-kit/core";
import { ColumnDef } from "@tanstack/react-table";
import { GripVertical } from "lucide-react";
import { LuFileClock } from "react-icons/lu";

import { DOCUMENT_ICONS } from "@/components/icons/doc-icons";
import {
  TableEditButton,
  TableGenericButton,
  TableRemoveButton,
} from "@/components/shared/table-action-buttons";
import { ActivityBadge } from "@/components/ui/badges/activity-badge";
import {
  DocumentType,
  DocumentTypeBadge,
} from "@/components/ui/badges/document-type-badge";
import { PillBadge } from "@/components/ui/badges/pill-badge";
import {
  DocExchangeActivity,
  DocExchangeDocument,
} from "@/lib/apis/doc-exchange/types";
import { EBinderDocumentVersion } from "@/lib/apis/essential-document-versions/types";
import { formatDate } from "@/lib/utils";

const variants = {
  completed: "success",
  pending: "warning",
  failed: "danger",
} as const;

export const generateDocumentColumns = ({
  onEdit,
  onViewAuditLogs,
  onDelete,
  // onPreview,
}: {
  onEdit: (file: DocExchangeDocument) => void;
  onDelete: (file: DocExchangeDocument) => void;
  // onPreview: (file: DocExchangeDocument) => void;
  onViewAuditLogs: (file: DocExchangeDocument) => void;

  isShowDetailColumns?: boolean;
}): ColumnDef<DocExchangeDocument>[] => [
  {
    id: "drag-handle",
    header: "",
    cell: function DragHandler({ row }) {
      const { attributes, listeners, setNodeRef, isDragging } = useDraggable({
        id: `${row.original.id}`,
        data: {
          ...row.original,
          type: "document",
        },
      });

      return (
        <div
          ref={setNodeRef}
          {...attributes}
          {...listeners}
          className="grid cursor-grab place-content-center"
          style={{ opacity: isDragging ? 0.5 : 1 }}
        >
          <GripVertical size={20} className="text-gray-500" />
        </div>
      );
    },
    enableSorting: false,
    meta: { width: "40px" },
  },

  {
    header: "Type",
    accessorKey: "extension",
    cell: ({ row }) => {
      const extension = row.original.fileRecord.extension;

      if (!extension)
        return <span className="whitespace-nowrap text-gray-400">N/A</span>;

      return (
        <div className="whitespace-nowrap">
          <DocumentTypeBadge type={extension} />
        </div>
      );
    },
  },
  {
    header: "Name",
    accessorKey: "title",
    cell: ({ row }) => {
      const icon =
        DOCUMENT_ICONS[
          row.original.fileRecord.extension as Exclude<DocumentType, "default">
        ];

      return (
        <div className="flex cursor-pointer items-center gap-2 whitespace-nowrap">
          {icon}
          {row.original.title}
        </div>
      );
    },
  },
  {
    header: "Status",
    accessorKey: "status",
    cell: ({ row }) => {
      const status = row.original.status;

      return (
        <PillBadge variant={variants[status]}>
          {status.charAt(0).toUpperCase() + status.slice(1)}
        </PillBadge>
      );
    },
  },
  {
    header: "Created Date",
    accessorKey: "createdDate",
    cell: ({ row }) => (
      <span className="whitespace-nowrap">
        {formatDate(row.original.createdDate)}
      </span>
    ),
  },
  {
    header: "Actions",
    accessorKey: "actions",
    enableSorting: false,
    cell: ({ row }) => {
      const document = row.original;
      return (
        <div className="flex gap-x-4">
          {/* <TableViewButton
            type="button"
            onClick={() => {
              onPreview(document);
            }}
          /> */}
          <TableEditButton type="button" onClick={() => onEdit(document)} />
          <TableGenericButton
            type="button"
            onClick={() => {
              onViewAuditLogs(document);
            }}
          >
            <LuFileClock className="size-4" /> Audit
          </TableGenericButton>
          <TableRemoveButton
            onClick={() => {
              onDelete(document);
            }}
          />
        </div>
      );
    },
  },
];

export const auditLogColumns: ColumnDef<DocExchangeActivity>[] = [
  {
    header: "Action",
    accessorKey: "action",
    cell: ({ row }) => <ActivityBadge activity={row.original.action} />,
  },
  {
    header: "User Name",
    accessorKey: "profile.user.firstName",
    cell: ({ row }) => (
      <span>
        {row.original.profile?.user?.firstName}{" "}
        {row.original.profile?.user?.lastName}
      </span>
    ),
  },
  {
    header: "Date",
    accessorKey: "createdDate",
    cell: ({ row }) => (
      <span>{formatDate(row.original.createdDate, "yyyy-MM-dd")}</span>
    ),
  },
];

export const generateVersionColumns = ({
  onView,
}: {
  onView: (version: EBinderDocumentVersion) => void;
}): ColumnDef<EBinderDocumentVersion>[] => [
  {
    header: "Ver",
    accessorKey: "versionNumber",
    cell: ({ row }) => (
      <span
        onClick={() => onView(row.original)}
        className="cursor-pointer font-semibold text-purple-500 underline-offset-4 hover:underline"
      >
        {row.original?.versionNumber?.join(".")}
      </span>
    ),
  },
  {
    header: "Date",
    accessorKey: "createdDate",
    cell: ({ row }) =>
      row.original?.createdDate
        ? formatDate(row.original?.createdDate ?? "")
        : null,
  },
  {
    header: "Comments",
    accessorKey: "comments",
    cell: ({ row }) => {
      return row.original?.comments.map((comment) => (
        <span key={comment.id} className="block">
          {comment.text}
        </span>
      ));
    },
  },
];
