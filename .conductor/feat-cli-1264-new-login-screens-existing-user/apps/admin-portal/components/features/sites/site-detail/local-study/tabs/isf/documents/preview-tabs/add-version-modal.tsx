import "@uppy/core/dist/style.min.css";
import "@uppy/dashboard/dist/style.min.css";
import "@uppy/status-bar/dist/style.css";

import { useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import toast from "react-hot-toast";
import { z } from "zod";

import { Button, CloseButton } from "@/components/ui/button";
import { Form, Textarea } from "@/components/ui/form";
import { FileDropzone } from "@/components/ui/form/file-drop-zone";
import { Label } from "@/components/ui/form/label";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { Skeleton } from "@/components/ui/skeleton";
import { ALLOWED_FILE_TYPES, MAX_FILE_SIZE } from "@/lib/constants";
import { formatDate } from "@/lib/utils";

import { getExtensionFromFileName } from "../../folders/utils";
import {
  useUpdateDocument,
  useUploadPlaceholder,
} from "../../hooks/use-isf-mutations";
import { isfKeys, useDocument } from "../../hooks/use-isf-queries";

const schema = z.object({
  comment: z.string().optional(),
  file: z
    .instanceof(File)
    .refine(
      (file) => {
        const splittedName = file.name.split(".");
        const extension = splittedName[splittedName.length - 1];
        return ALLOWED_FILE_TYPES.includes(`.${extension}`);
      },
      { message: "Invalid document file type" },
    )
    .refine((file) => file.size <= MAX_FILE_SIZE, {
      message: "File size should not exceed 5MB",
    }),
});

type Props = {
  documentId: string;
  isOpen: boolean;
  onClose: () => void;
};
export const AddVersionModal = ({ documentId, isOpen, onClose }: Props) => {
  const [isManualUploading, setIsManualUploading] = useState(false);

  const { data, isPending } = useDocument(documentId);
  const { mutateAsync: uploadPlaceholder } = useUploadPlaceholder();
  const { mutateAsync: updateDocument } = useUpdateDocument();
  const queryClient = useQueryClient();

  const onSubmit = async (data: z.infer<typeof schema>) => {
    const file = data.file;
    const extension = getExtensionFromFileName(file.name);
    try {
      setIsManualUploading(true);
      const result = await uploadPlaceholder({
        id: documentId,
        extension: extension,
        fileType: file.type,
        originationType: "web",
        comment: data.comment,
      });
      if (result.uploadUrl) {
        await fetch(result.uploadUrl, {
          method: "PUT",
          body: file,
          headers: {
            "Content-Type": file.type,
          },
        });
        await updateDocument({
          id: documentId,
          processingStatus: "completed",
          hideSuccessNotification: true,
        });
        await queryClient.invalidateQueries({
          queryKey: isfKeys.allDocumentLists(),
        });
        await queryClient.invalidateQueries({
          queryKey: isfKeys.documentVersions(documentId),
        });
        await queryClient.invalidateQueries({
          queryKey: isfKeys.documentDetail(documentId),
        });
        toast.success("New version uploaded successfully");
        onClose();
      } else {
        toast.error("Failed to upload new version");
      }
    } catch (error) {
      console.error("Upload error:", error);
      toast.error("Error uploading new version");
    } finally {
      setIsManualUploading(false);
    }
  };

  return (
    <WrapperModal
      size="3xl"
      isOpen={isOpen}
      onClose={onClose}
      title="Add New Version"
    >
      {isPending ? (
        <div className="mb-4 flex items-center justify-between rounded-lg border border-gray-200 px-4 py-3 sm:px-6 dark:text-white">
          <Skeleton className="h-6 w-9" />
          <Skeleton className="h-6 w-28" />
          <Skeleton className="h-6 w-60" />
        </div>
      ) : (
        <div className="mb-4 flex items-center justify-between rounded-lg border border-gray-200 px-4 py-3 sm:px-6 dark:text-white">
          <span>{data?.currentVersion?.versionNumber?.join(".")}</span>
          {data?.currentVersion?.createdDate ? (
            <span>
              {formatDate(data?.currentVersion?.createdDate, "LLL dd, yyyy")}
            </span>
          ) : null}
          <span>{data?.title}</span>
        </div>
      )}
      <Form schema={schema} onSubmit={onSubmit}>
        <div className="grid gap-4 sm:gap-6">
          <div className="flex flex-col gap-2">
            <Label htmlFor="file">Upload New Version</Label>
            <FileDropzone name="file" acceptTypes={ALLOWED_FILE_TYPES} />
          </div>
          <div className="flex flex-col gap-2">
            <Label htmlFor="comment">Comment</Label>
            <Textarea name="comment" id="comment" placeholder="Enter comment" />
          </div>
        </div>
        <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
          <CloseButton onClose={onClose} />
          <Button type="submit" variant="primary" isLoading={isManualUploading}>
            Save
          </Button>
        </div>
      </Form>
    </WrapperModal>
  );
};
