import { useState } from "react";

import { Button } from "@/components/ui/button";
import { Form, InputField, Select } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { Modal } from "@/components/ui/modal";

// import { useExistingIsfProtocol } from "@/hooks/useExistingIsfProtocol";
// import { useUplaodIsfProtocols } from "@/hooks/useUploadIsfProtocols";
// import { IsfTemplateProtocol } from "@/lib/apis/isf-template-protocols";
import { useIsfProtocols } from "../hooks/use-isf-protocols";
// import { error } from "console";

type ModalAddStudyProps = {
  isOpen: boolean;
  onClose: () => void;
  studyId: string | null;
  siteId: string | null;
};

export const ModalBuildIsfTemplate = ({
  isOpen,
  onClose,
}: ModalAddStudyProps) => {
  // const { mutateAsync: useExistingTemplate } = useExistingIsfProtocol();
  // const { mutateAsync: useUploadJson } = useUploadIsfProtocols();

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  // const [name, setName] = useState<string>("");
  // const [selectedTemplate, setSelectedTemplate] = useState<string>("");

  const { data: templates } = useIsfProtocols();
  //   const { data2: studies } = useStudies();

  const handleUploadJson = async () => {
    if (!selectedFile) {
      alert("Please select a file to upload.");
      return;
    }

    // try {
    //   console.log(siteId, studyId, selectedFile, name);
    //   await useUploadJson({
    //     file: selectedFile,
    //     siteId: siteId,
    //     studyId: studyId,
    //     name: name,
    //   });

    //   onClose();
    // } catch (error) { }
  };

  const handleUseExisting = async () => {
    // if (!selectedTemplate) {
    //   alert("Please select a template.");
    //   return;
    // }
    // try {
    //   if (!siteId || !studyId) {
    //     throw ErrorEvent;
    //   }
    //   await useExistingTemplate({
    //     id: selectedTemplate,
    //     siteId: siteId,
    //     studyId: studyId,
    //   });
    //   onClose();
    // } catch (error) { }
  };

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Build ISF Template</Modal.Header>
      <Modal.Body>
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
          <div className="col-span-1">
            <Form onSubmit={handleUseExisting}>
              <div className="col-span-1">
                <Label htmlFor="templates">Existing Templates</Label>
                <Select
                  id="templates"
                  name="templates"
                  options={templates?.results.map((template) => ({
                    label: template.name,
                    value: template.id,
                  }))}
                  placeholder="Select template"
                />
              </div>

              <Button className="mt-2 " type="submit" variant="primary">
                Build Existing
              </Button>
            </Form>
          </div>
          <div className="col-span-1">
            <Form onSubmit={handleUploadJson}>
              <div className="col-span-1">
                <Label htmlFor="name">Template Name</Label>
                <InputField
                  id="name"
                  name="name"
                  type="string"
                  // onChange={(e) => setName(e.target.value)}
                  placeholder="Enter template name..."
                />
              </div>

              <div className="col-span-1 mt-2">
                <input
                  type="file"
                  onChange={(e) =>
                    setSelectedFile(e.target.files ? e.target.files[0] : null)
                  }
                ></input>
              </div>

              <Button className="mt-2 " type="submit" variant="primary">
                Upload Template
              </Button>
            </Form>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};
