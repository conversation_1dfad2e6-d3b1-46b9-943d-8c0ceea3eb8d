import type { ColumnDef } from "@tanstack/react-table";

import { DOCUMENT_ICONS, DocumentType } from "@/components/icons/doc-icons";
import { DocumentTypeBadge } from "@/components/ui/badges/document-type-badge";
import { PillBadge } from "@/components/ui/badges/pill-badge";
import { PatientVisitDocument } from "@/lib/apis/essential-documents";
import { formatDate } from "@/lib/utils";

// type Props = {
//   onPreview: (document: PatientVisitDocument) => void;
// };

export const generateColumnsDocuments =
  (): ColumnDef<PatientVisitDocument>[] => [
    {
      header: "Type",
      accessorKey: "extension",
      cell: ({ row }) => (
        <DocumentTypeBadge type={row.original.fileRecord!.extension} />
      ),
    },
    {
      header: "Name",
      accessorKey: "title",
      cell: ({ row }) => {
        return (
          <span
            // onClick={() => onPreview(row.original)}
            className="text-primary-500 flex cursor-pointer items-center gap-1 font-medium hover:underline"
          >
            {DOCUMENT_ICONS[row.original.fileRecord?.extension as DocumentType]}{" "}
            {row.original.title}
          </span>
        );
      },
    },
    {
      header: "Source",
      accessorKey: "originationType",
      cell: ({ row }) => {
        const original = row.original;
        if (original.fileRecord?.originationType === "web")
          return <PillBadge variant="success">Upload</PillBadge>;
        if (original.fileRecord?.originationType === "scanner")
          return <PillBadge variant="primary">Scanner</PillBadge>;
        return "";
      },
    },
    {
      header: "Added By",
      accessorKey: "originationUser",
      cell: ({ row }) => {
        const original = row.original.fileRecord?.originationProfile;
        return (
          <span className="whitespace-nowrap">
            {original
              ? `${original?.user?.firstName} ${original?.user?.lastName}`
              : "-"}
          </span>
        );
      },
    },
    {
      header: "Date Added",
      accessorKey: "createdDate",
      cell: ({ row }) => {
        const original = row.original;
        const value = original.createdDate;
        return (
          <div className="whitespace-nowrap">
            {value ? (
              formatDate(value)
            ) : (
              <span className="text-gray-500">N/A</span>
            )}
          </div>
        );
      },
    },
    // {
    //   header: "Action",
    //   accessorKey: "id",
    //   enableSorting: false,
    //   cell: ({ row }) => {
    //     return (
    //       <TableViewButton
    //         type="button"
    //         onClick={() => onPreview(row.original)}
    //       />
    //     );
    //   },
    // },
  ];
