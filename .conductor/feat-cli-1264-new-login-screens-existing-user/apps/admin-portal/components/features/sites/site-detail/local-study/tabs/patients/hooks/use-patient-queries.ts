import { useQuery } from "@tanstack/react-query";

import api from "@/lib/apis";
import { LocalStudyParams } from "@/lib/apis/sites";
import { MetadataParams } from "@/lib/apis/types";

import { usePatientFilters } from "./use-patient-filter";

export const localStudyPatientKeys = {
  all: () => ["local-study-patients"] as const,

  allList: (localStudyParams: LocalStudyParams) =>
    [...localStudyPatientKeys.all(), localStudyParams] as const,
  list: (localStudyParams: LocalStudyParams, params?: MetadataParams) =>
    [...localStudyPatientKeys.allList(localStudyParams), params] as const,
};

export const useLocalStudyPatients = (localStudyParams: LocalStudyParams) => {
  const {
    page,
    take,
    orderBy,
    orderDirection,
    search,
    active,
    archived,
    status,
  } = usePatientFilters();
  const params = {
    page,
    take,
    orderBy: orderBy || undefined,
    orderDirection: orderDirection || undefined,
    filter: {
      name: search,
      isActive: active || undefined,
      archived: archived || undefined,
      status: status || undefined,
    },
  };

  return useQuery({
    queryKey: localStudyPatientKeys.list(localStudyParams, params),

    queryFn: () =>
      api.studies.getPatients({
        payload: localStudyParams,
        params,
      }),
    placeholderData: (prev) => prev,
  });
};
