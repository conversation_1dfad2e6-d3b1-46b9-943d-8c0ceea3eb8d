import { capitalize } from "lodash";
import { ListFilter } from "lucide-react";
import { useRef, useState } from "react";
import { z } from "zod";

import { Button } from "@/components/ui/button";
import {
  Dropdown,
  DropdownContent,
  DropdownTrigger,
} from "@/components/ui/dropdown";
import { Form, FormRef, Select } from "@/components/ui/form";
import { DateRangePicker } from "@/components/ui/form/date-picker/date-range-picker";
import { Label } from "@/components/ui/form/label";
import { useMediaQuery } from "@/hooks/use-media-query";
import { cn } from "@/lib/utils";

import { useFilterDocuments } from "./hooks/use-filter-documents";

// List of document types
const DOC_TYPES = [
  { label: "PDF", value: "pdf" },
  { label: "DOC", value: "doc" },
  { label: "DOCX", value: "docx" },
  { label: "XLS", value: "xls" },
  { label: "XLSX", value: "xlsx" },
  { label: "CSV", value: "csv" },
  { label: "TXT", value: "txt" },
  { label: "JPG", value: "jpg" },
  { label: "JPEG", value: "jpeg" },
  { label: "PNG", value: "png" },
  { label: "EML", value: "eml" },
];

// List of status options
const STATUS_OPTIONS = ["completed", "pending", "failed"];

const schema = z.object({
  extension: z.string().optional(),
  status: z.string().optional(),
  dateRange: z
    .object({
      from: z.date().optional().nullable(),
      to: z.date().optional().nullable(),
    })
    .optional()
    .nullable(),
});

export const FilterEBinder = () => {
  const [open, setOpen] = useState(false);
  const { fromCreatedDate, toCreatedDate, extension, status } =
    useFilterDocuments();
  const filters = [!!extension, !!status, !!fromCreatedDate || !!toCreatedDate];

  const totalFilters = filters.filter(Boolean).length;

  return (
    <Dropdown open={open} onOpenChange={setOpen} placement="bottom-end">
      <DropdownTrigger>
        <Button variant="outline" className="relative !py-2 ">
          <ListFilter className="size-5" />

          {!!totalFilters && (
            <i
              style={{
                padding: "4px !important",
              }}
              className="bg-primary-600 absolute right-1 top-0 block rounded-lg px-1 not-italic text-white md:right-4 "
            >
              {totalFilters}
            </i>
          )}
        </Button>
      </DropdownTrigger>
      <DropdownContent className="z-30 rounded-lg bg-white drop-shadow-2xl dark:bg-gray-700">
        <FilterContent setOpen={setOpen} />
      </DropdownContent>
    </Dropdown>
  );
};

const FilterContent = ({ setOpen }: { setOpen: (open: boolean) => void }) => {
  const ref = useRef<FormRef<typeof schema>>(null);
  const isTablet = useMediaQuery("(min-width: 768px)");

  const {
    setExtension,
    setStatus,
    setFromCreatedDate,
    setToCreatedDate,
    fromCreatedDate,
    toCreatedDate,
    extension,
    status,
    goToPage,
  } = useFilterDocuments();

  function onSubmit(data: z.infer<typeof schema>) {
    setExtension(data.extension || null);
    setStatus(data.status || null);
    setFromCreatedDate(
      data.dateRange?.from ? data.dateRange.from.toLocaleDateString() : null,
    );
    setToCreatedDate(
      data.dateRange?.to ? data.dateRange.to.toLocaleDateString() : null,
    );
    goToPage(1);
    setOpen(false);
  }
  function onClear() {
    setExtension(null);
    setStatus(null);
    setFromCreatedDate(null);
    setToCreatedDate(null);
    goToPage(1);

    ref.current?.formHandler?.reset();
    setOpen(false);
  }

  const dateRange =
    fromCreatedDate || toCreatedDate
      ? {
          from: fromCreatedDate ? new Date(fromCreatedDate) : undefined,
          to: toCreatedDate ? new Date(toCreatedDate) : undefined,
        }
      : undefined;

  const defaultValues = {
    extension: extension ?? "",
    status: status ?? "",
    dateRange,
  };

  const hasFilters = isTablet
    ? !!extension || !!status || !!fromCreatedDate || !!toCreatedDate
    : !!extension || !!status || !!fromCreatedDate || !!toCreatedDate;

  return (
    <div className="">
      <Form
        schema={schema}
        mode="onChange"
        onSubmit={onSubmit}
        className="w-80 md:w-96"
        ref={ref}
        defaultValues={defaultValues}
        key={Object.values(defaultValues).join("-")}
      >
        <div className="flex flex-col divide-y">
          {/* Document Type */}
          <div className="px-[15px] py-2 md:pb-5 md:pt-2.5">
            <Label
              htmlFor="extension"
              className="mb-1 block text-sm font-normal leading-6 text-gray-700 sm:text-base md:mb-2"
            >
              Extension
            </Label>
            <Select
              id="extension"
              name="extension"
              className="h-fit w-full"
              options={DOC_TYPES}
              placeholder="Select extension"
            />
          </div>

          {/* Status */}
          <div className="px-[15px] py-2 md:pb-5 md:pt-2.5">
            <Label
              htmlFor="status"
              className="mb-1 block text-sm font-normal leading-6 text-gray-700 sm:text-base md:mb-2"
            >
              Status
            </Label>
            <Select
              id="status"
              name="status"
              className="h-fit w-full"
              options={STATUS_OPTIONS.map((s) => ({
                label: capitalize(s),
                value: s,
              }))}
              placeholder="Select status"
            />
          </div>

          {/* Date Range */}
          <div className="px-[15px] py-2 md:pb-5 md:pt-2.5">
            <Label
              htmlFor="dateRange"
              className="mb-1 block text-sm font-normal leading-6 text-gray-700 sm:text-base md:mb-2"
            >
              Created Date
            </Label>
            <DateRangePicker
              name="dateRange"
              placeholder="Select date"
              maxDate={new Date()}
            />
          </div>
        </div>

        <div className="flex justify-end gap-5 px-[15px] py-2 md:pb-5 md:pt-2.5">
          <Button
            variant="outline"
            className={cn(
              "w-full justify-center",
              !hasFilters && "pointer-events-none invisible",
            )}
            onClick={() => {
              onClear();
            }}
            type="button"
          >
            Clear Filters
          </Button>
          <Button
            variant="primary"
            type="submit"
            className="w-full justify-center"
          >
            Apply Filters
          </Button>
        </div>
      </Form>
    </div>
  );
};
