import { create } from "zustand";

import { TaskStatus } from "@/components/ui/badges/task-status-badge";
import { Task } from "@/lib/apis/tasks";

type TaskStore = {
  isOpenTaskModal: boolean;
  isOpenTaskDetailModal: boolean;
  isOpenConfirmDeleteModal: boolean;

  status: TaskStatus | null;
  selectedTask: Task | null;
  selectedTaskId: string;

  onOpenTaskModal: (payload?: { status?: TaskStatus; task?: Task }) => void;
  onCloseTaskModal: () => void;
  onCloseConfirmDeleteModal: () => void;
  onOpenConfirmDeleteModal: (id: string) => void;
  onOpenTaskDetailModal: (id: string) => void;
  onCloseTaskDetailModal: () => void;
};

export const useTaskModals = create<TaskStore>()((set) => ({
  isOpenConfirmDeleteModal: false,
  isOpenTaskModal: false,
  isOpenTaskDetailModal: false,
  selectedTask: null,
  status: null,
  selectedTaskId: "",

  onOpenTaskModal: (payload) =>
    set({
      isOpenTaskModal: true,
      selectedTask: payload?.task || null,
      status: payload?.status || null,
    }),
  onCloseTaskModal: () =>
    set({
      isOpenTaskModal: false,
      selectedTask: null,
      status: null,
    }),
  onCloseConfirmDeleteModal: () =>
    set((state) => ({
      isOpenConfirmDeleteModal: false,
      selectedTaskId: state.isOpenTaskDetailModal ? state.selectedTaskId : "",
    })),
  onOpenConfirmDeleteModal: (id) =>
    set({
      isOpenConfirmDeleteModal: true,
      selectedTaskId: id,
    }),
  onOpenTaskDetailModal: (id) =>
    set({
      isOpenTaskDetailModal: true,
      selectedTaskId: id,
    }),
  onCloseTaskDetailModal: () =>
    set({
      isOpenTaskDetailModal: false,
      selectedTaskId: "",
    }),
}));
