import { Card } from "flowbite-react";
import React, { useMemo } from "react";
import { IoMdAdd } from "react-icons/io";

import { SearchField } from "@/components/shared";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Button } from "@/components/ui/button";
import { Table, TableLoading } from "@/components/ui/table";

import { generateQuestionColumns } from "./columns";

export const QuestionTab = () => {
  const columns = useMemo(() => generateQuestionColumns(() => {}), []);
  const isPending = false;
  const isPlaceholderData = false;
  return (
    <Card className="border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800 [&>div]:p-0">
      <div className="border-b border-gray-200 p-4 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Question Templates
          </h3>

          <div className="flex items-center gap-4">
            <SearchField placeholder="Search questions..." />
            <Button variant="primary">
              <IoMdAdd className="h-4 w-4" /> New Question
            </Button>
          </div>
        </div>
      </div>

      <div className="p-0">
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <LoadingWrapper isLoading={isPlaceholderData}>
            <Table columns={columns} data={[]} />
            {/* <TableDataPagination metadata={}  /> */}
          </LoadingWrapper>
        )}
      </div>
    </Card>
  );
};
