import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import { codelistTemplates } from "@/lib/apis/codelist-templates";
import {
  CreateCodelistTemplatePayload,
  UpdateCodelistTemplatePayload,
} from "@/lib/apis/codelist-templates/types";

import { codelistTemplateKeys } from "./use-codelist-template-queries";

export const useCreateCodelistTemplate = () => {
  return useMutation({
    mutationFn: (payload: CreateCodelistTemplatePayload) =>
      codelistTemplates.create(payload),
    onError: (err) =>
      toast.error(err?.message || "Failed to create codelist template"),
    onSettled: (_, err) =>
      !err && toast.success("Codelist template created successfully"),
    meta: {
      awaits: codelistTemplateKeys.allLists(),
    },
  });
};

export const useUpdateCodelistTemplate = () => {
  return useMutation({
    mutationFn: (payload: UpdateCodelistTemplatePayload) =>
      codelistTemplates.update(payload),
    onError: (err) =>
      toast.error(err?.message || "Failed to update codelist template"),
    onSettled: (_, err) =>
      !err && toast.success("Codelist template updated successfully"),
    meta: {
      awaits: codelistTemplateKeys.allLists(),
    },
  });
};

export const useDeleteCodelistTemplate = () => {
  return useMutation({
    mutationFn: ({ id }: { id: string }) => codelistTemplates.delete(id),
    onError: (err) =>
      toast.error(err?.message || "Failed to delete codelist template"),
    onSettled: (_, err) =>
      !err && toast.success("Codelist template deleted successfully"),
    meta: {
      awaits: codelistTemplateKeys.allLists(),
    },
  });
};
