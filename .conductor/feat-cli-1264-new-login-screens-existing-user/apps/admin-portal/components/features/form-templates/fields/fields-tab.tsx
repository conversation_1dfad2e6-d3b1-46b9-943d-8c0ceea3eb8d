"use client";

import { Card } from "flowbite-react";
import { useMemo, useState } from "react";
import { IoMdAdd } from "react-icons/io";

import { ConfirmationModal } from "@/components/shared/confirmation-modal";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { SearchField } from "@/components/shared/search-field";
import { Button } from "@/components/ui/button";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import { useDisclosure } from "@/hooks/use-disclosure";
import { FieldTemplateItem } from "@/lib/apis/field-templates/types";

import { generateFieldTemplateColumns } from "./columns";
import { FieldModal } from "./field-modal";
import { FilterFieldTemplates } from "./filter-field-templates";
import { useDeleteFieldTemplate } from "./hooks/use-field-template-mutations";
import { useFieldTemplates } from "./hooks/use-field-template-queries";

export const FieldsTab = () => {
  const { data, isPending, isPlaceholderData } = useFieldTemplates();
  const { mutateAsync: deleteFieldTemplate, isPending: isDeleting } =
    useDeleteFieldTemplate();

  const {
    isOpen: isDeleteModalOpen,
    open: openDeleteModal,
    close: closeDeleteModal,
  } = useDisclosure();

  const {
    isOpen: isModalOpen,
    open: openModal,
    close: closeModal,
  } = useDisclosure();

  const [selectedField, setSelectedField] = useState<FieldTemplateItem | null>(
    null,
  );
  const [isViewMode, setIsViewMode] = useState(false);

  const handleView = (field: FieldTemplateItem) => {
    setSelectedField(field);
    setIsViewMode(true);
    openModal();
  };

  const handleEdit = (field: FieldTemplateItem) => {
    setSelectedField(field);
    setIsViewMode(false);
    openModal();
  };

  const handleShowDeleteModal = (field: FieldTemplateItem) => {
    setSelectedField(field);
    openDeleteModal();
  };

  const handleConfirmDelete = async () => {
    if (selectedField) {
      await deleteFieldTemplate(selectedField.id);
      closeDeleteModal();
      setSelectedField(null);
    }
  };

  const handleAddField = () => {
    setSelectedField(null);
    setIsViewMode(false);
    openModal();
  };

  const handleCloseModal = () => {
    closeModal();
    setSelectedField(null);
    setIsViewMode(false);
  };

  const columns = useMemo(
    () =>
      generateFieldTemplateColumns({
        onView: handleView,
        onEdit: handleEdit,
        onDelete: handleShowDeleteModal,
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [],
  );

  return (
    <Card className="border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800 [&>div]:p-0">
      <div className="border-b border-gray-200 p-4 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Field Templates
          </h3>

          <div className="flex items-center gap-4">
            <SearchField placeholder="Search ..." />
            <FilterFieldTemplates />
            <Button variant="primary" onClick={handleAddField}>
              <IoMdAdd className="h-4 w-4" /> New Field
            </Button>
          </div>
        </div>
      </div>

      <div className="p-0">
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <LoadingWrapper isLoading={isPlaceholderData || isDeleting}>
            <Table columns={columns} data={data?.results ?? []} />
            {data?.metadata && <TableDataPagination metadata={data.metadata} />}
          </LoadingWrapper>
        )}
      </div>

      {isModalOpen && (
        <FieldModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          selectedField={selectedField}
          isViewMode={isViewMode}
        />
      )}

      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={closeDeleteModal}
        onConfirm={handleConfirmDelete}
        variant="danger"
        message="Are you sure you want to delete this field template?"
        itemName={selectedField?.friendlyName}
        confirmLabel="Delete"
        isLoading={isDeleting}
      />
    </Card>
  );
};
