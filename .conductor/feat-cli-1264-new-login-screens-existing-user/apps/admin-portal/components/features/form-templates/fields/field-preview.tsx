import { X } from "lucide-react";

import {
  createValidationSchema,
  renderFieldPreview,
} from "@/components/shared/field-builder";
import { Form } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";

import { type FieldTemplateValues } from "./field-modal";

type Props = {
  onClose: () => void;
  values: FieldTemplateValues;
};

const FieldPreview = ({ onClose, values }: Props) => {
  const { name, label, description, type, config } = values;
  const previewSchema = createValidationSchema({
    name: values.name,
    label: values.label,
    type: values.type,
    config: values.config,
  });

  return (
    <div className="flex h-full flex-col rounded-xl border border-blue-500 bg-white shadow dark:border-blue-400 dark:bg-gray-900">
      <div className="flex items-center justify-between rounded-tl-xl rounded-tr-xl border-b border-b-blue-500 bg-blue-50 p-5 dark:border-b-blue-400 dark:bg-blue-900/20">
        <span className="text-xl font-bold text-gray-900 dark:text-white">
          Preview Mode
        </span>
        <button
          onClick={onClose}
          className="rounded-full bg-blue-500 p-2 text-white transition-colors hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700"
        >
          <X className="h-5 w-5" />
        </button>
      </div>
      <div className="flex-1 space-y-2.5 overflow-y-auto rounded-bl-xl rounded-br-xl bg-white p-5 dark:bg-gray-900">
        <Form
          onSubmit={(data) => {
            //
          }}
          mode="all"
          defaultValues={{
            [values.name]:
              type === "Date" || type === "DateTime" ? undefined : "",
          }}
          schema={previewSchema}
        >
          {name && label && type ? (
            <div className="space-y-2">
              <Label htmlFor={name}>{label}</Label>
              {description && values.config?.isDisplayOnForm && (
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {description}
                </p>
              )}
              {config?.unitOfMeasure && (
                <p className="text-sm text-gray-500">
                  Unit: {config.unitOfMeasure}
                </p>
              )}
              {renderFieldPreview({
                name: values.name,
                label: values.label,
                type: values.type,
                config: values.config,
              })}
            </div>
          ) : (
            renderFieldPreview({
              name: values.name,
              label: values.label,
              type: values.type,
              config: values.config,
            })
          )}
        </Form>
      </div>
    </div>
  );
};

export default FieldPreview;
