import { useQuery } from "@tanstack/react-query";
import { parseAsString } from "nuqs";
import { useQueryState } from "nuqs";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

export const fieldTemplateKeys = {
  all: () => ["field-templates"] as const,
  allLists: () => [...fieldTemplateKeys.all(), "list"] as const,
  list: (params?: MetadataParams) =>
    [...fieldTemplateKeys.allLists(), params] as const,
};

export const useFieldTemplates = () => {
  const { page, take } = usePagination();
  const { search } = useSearch();
  const [fieldType] = useQueryState("fieldType", parseAsString);

  const params = {
    page,
    limit: take,
    filter: {
      search: search || undefined,
      fieldType: fieldType || undefined,
    },
  };

  return useQuery({
    queryKey: fieldTemplateKeys.list(params),
    queryFn: () => api.fieldTemplates.list(params),
    placeholderData: (prev) => prev,
  });
};
