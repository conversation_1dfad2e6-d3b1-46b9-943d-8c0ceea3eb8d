import Image from "next/image";

export const DOCUMENT_ICONS = {
  pdf: (
    <Image
      className="flex-shrink-0"
      src="/doc-icons/pdf.svg"
      alt="pdf"
      width={20}
      height={20}
    />
  ),
  doc: (
    <Image
      className="flex-shrink-0"
      src="/doc-icons/doc.svg"
      alt="doc"
      width={20}
      height={20}
    />
  ),
  docx: (
    <Image
      className="flex-shrink-0"
      src="/doc-icons/docx.svg"
      alt="docx"
      width={20}
      height={20}
    />
  ),
  jpg: (
    <Image
      className="flex-shrink-0"
      src="/doc-icons/jpg.svg"
      alt="jpg"
      width={20}
      height={20}
    />
  ),
  png: (
    <Image
      className="flex-shrink-0"
      src="/doc-icons/png.svg"
      alt="png"
      width={20}
      height={20}
    />
  ),
  eml: (
    <Image
      className="flex-shrink-0"
      src="/doc-icons/eml.svg"
      alt="eml"
      width={20}
      height={20}
    />
  ),
  txt: (
    <Image
      className="flex-shrink-0"
      src="/doc-icons/txt.svg"
      alt="txt"
      width={20}
      height={20}
    />
  ),
  jpeg: (
    <Image
      className="flex-shrink-0"
      src="/doc-icons/jpeg.svg"
      alt="jpeg"
      width={20}
      height={20}
    />
  ),
  csv: (
    <Image
      className="flex-shrink-0"
      src="/doc-icons/csv.svg"
      alt="csv"
      width={20}
      height={20}
    />
  ),
  xlsx: (
    <Image
      className="flex-shrink-0"
      src="/doc-icons/xlsx.svg"
      alt="xlsx"
      width={20}
      height={20}
    />
  ),
  xls: (
    <Image
      className="flex-shrink-0"
      src="/doc-icons/xls.svg"
      alt="xls"
      width={20}
      height={20}
    />
  ),
};

export type DocumentType = keyof typeof DOCUMENT_ICONS;
