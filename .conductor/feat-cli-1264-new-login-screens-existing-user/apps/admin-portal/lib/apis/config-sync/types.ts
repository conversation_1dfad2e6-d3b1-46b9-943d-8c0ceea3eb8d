import { ListBaseResponse } from "../types";

export type ConfigSyncStatus = {
  syncTimestamp: string;
  hasChanged: boolean;
};

export type ConfigSyncUpdate = {
  hasUpdates: boolean;
  availableVersion: string | null;
  currentVersion: string | null;
  changes?: string[];
};

export type ConfigSyncRunResponse = {
  success: boolean;
  message: string;
  syncId: string;
};

export type EntityType =
  | "artifactCategories"
  | "artifactCategoryVersions"
  | "aiPromptTemplate"
  | "aiPromptCache"
  | "aiPromptVariable"
  | "artifactIsfRefModel"
  | "artifactTmfRefModel";

export type MethodType = "update" | "deletion";

export type FetchByEntityParams = {
  entityType: EntityType;
  method: MethodType;
  take?: number;
  page?: number;
};

// API response with pagination
export type FetchByEntityResponse<T = any> = ListBaseResponse<T>;

export type OperationType = "updates" | "deletes";
