import <PERSON><PERSON><PERSON> from "../base";
import { EBinderDocumentVersion } from "../essential-document-versions/types";
import { EbinderDocument } from "../isf-folders";
import { MetadataParams } from "../types";
import {
  Document,
  DocumentListResponse,
  EBinderCreatePlaceHolderPayload,
  EBinderMoveDocumentPayload,
  EBinderMultipleUploadPayload,
  EBinderMultipleUploadResponse,
  EBinderStatistics,
  EBinderUpdatePlaceholderPayload,
  StatisticParams,
} from "./types";

class EssentialDocumentFilesApi extends BaseApi {
  constructor() {
    super("/essential-document-files", true);
  }

  async getListDocuments(
    params: MetadataParams & {
      studyId: string;
      siteId: string;
    },
  ) {
    const { studyId, siteId, ...rest } = params;
    const queryParams = this.generateQueryParams(rest);
    return this.http.get<DocumentListResponse>(
      `/study/${studyId}/site/${siteId}?${queryParams}`,
    );
  }

  async getDocument(id: string) {
    return this.http.get<Document>(`/${id}`);
  }

  async uploadMultiple(payload: EBinderMultipleUploadPayload) {
    const { studyId, siteId, ...rest } = payload;
    return this.http.post<EBinderMultipleUploadResponse>(
      `/study/${studyId}/site/${siteId}/upload-multiple`,
      rest,
    );
  }

  async createPlaceHolder(payload: EBinderCreatePlaceHolderPayload) {
    return this.http.post(`/isf`, payload);
  }

  async updateDocument(payload: EBinderUpdatePlaceholderPayload) {
    const { id, ...rest } = payload;
    return this.http.put(`/${id}`, rest);
  }

  async deleteDocument(id: string) {
    return this.http.delete(`/${id}`);
  }
  async moveDocument(payload: EBinderMoveDocumentPayload) {
    return this.http.patch(`/${payload.id}/move/isf`, {
      parentDirectory: payload.parentDirectoryId,
    });
  }

  async getPlaceholderPresignedUrl(id: string) {
    return this.http.get<{
      uploadUrl: string;
      documentId: string;
    }>(`/${id}/signed-upload-url`);
  }

  async getDocumentVersions(id: string) {
    return this.http.get<EBinderDocumentVersion[]>(`/${id}/versions`);
  }

  async getStatistics(params: StatisticParams) {
    return this.http.get<EBinderStatistics>(
      `/study/${params.studyId}/site/${params.siteId}/count`,
    );
  }

  async upcomingExpiration() {
    return this.http.get<EbinderDocument[]>(`/upcoming-expirations?limit=10`);
  }
}

export const essentialDocumentFiles = new EssentialDocumentFilesApi();
