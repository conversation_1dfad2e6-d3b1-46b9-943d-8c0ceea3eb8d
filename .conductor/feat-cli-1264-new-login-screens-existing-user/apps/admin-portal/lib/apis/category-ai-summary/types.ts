import { ArtifactVersion } from "../artifact-categories";

export type AddCategorySummaryPayload = {
  artifactCategoryVersionId: string;
  content: string;
};

export type UpdateCategorySummaryPayload = {
  id: string;
  content: string;
};

export type CategorySummary = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  artifactCategoryVersionId: string;
  content: string;
  needsProcessing: boolean;
  isActive: boolean;
};

export type CategorySummaryResponse = CategorySummary & {
  artifactCategoryVersion: ArtifactVersion;
};
