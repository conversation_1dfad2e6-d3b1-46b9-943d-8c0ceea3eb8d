import BaseApi from "../base";
import { MetadataParams } from "../types";
import type { CohortItem, CohortListResponse, CohortPayload } from "./types";

export class CohortApi extends BaseApi {
  constructor() {
    super("/cohorts", true);
  }

  public async getCohorts(id: string, params: MetadataParams) {
    const queryParams = this.generateQueryParams(params);
    return this.http.get<CohortListResponse>(`/study/${id}?${queryParams}`);
  }

  public async getCohort(id: string) {
    return this.http.get<CohortItem>(`/${id}`);
  }

  public async addCohort(data: CohortPayload) {
    return this.http.post<CohortPayload>("/", data);
  }

  public async updateCohort(
    data: CohortPayload & {
      id: string;
    },
  ) {
    return this.http.put<CohortPayload>(`/${data.id}`, data);
  }

  public async deleteCohort(id: string) {
    return this.http.patch<CohortPayload>(`/${id}/archive`);
  }
}
