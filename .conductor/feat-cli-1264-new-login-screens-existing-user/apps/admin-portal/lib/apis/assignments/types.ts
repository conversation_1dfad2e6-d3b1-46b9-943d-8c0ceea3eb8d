import type { User } from "../auth/types";
import type { Study } from "../studies";
import type { ListBaseResponse } from "../types";

export type AssignmentGroup = {
  id: string;
  name: string;
};

export type Assignment = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  name: string;
  description: string;
  type: string;
  groupId: string;
  group: AssignmentGroup;
  profile?: {
    id: string;
    assignmentId: string;
    siteId: string;
  };
};

export type AssignmentUser = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  userId: string;
  assignmentId: string;
  user: User;
};

export type AssignmentStudy = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  assignmentId: string;
  studyId: string;
  study: Study;
};

export type AssignmentListResponse = ListBaseResponse<Assignment>;
export type AssignmentUserListResponse = ListBaseResponse<AssignmentUser>;
export type AssignmentStudyListResponse = ListBaseResponse<AssignmentStudy>;

export type AddAssignmentPayload = {
  groupId: string;
  description: string;
  type: string;
  siteId?: string;
};

export type UpdateAssignmentPayload = AddAssignmentPayload;
export type AddStudyToAssignmentPayload = {
  studyId: string;
};
