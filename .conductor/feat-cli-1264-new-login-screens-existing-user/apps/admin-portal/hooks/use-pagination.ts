import { parseAsInteger, useQueryState } from "nuqs";
import { useCallback } from "react";

export function usePagination() {
  const [page, setPage] = useQueryState("page", parseAsInteger.withDefault(1));
  const [take, setTake] = useQueryState(
    "take",
    parseAsInteger.withDefault(100),
  );

  const goToPage = useCallback(
    (newPage: number) => {
      setPage(newPage);
    },
    [setPage],
  );

  const nextPage = useCallback(() => {
    setPage(page + 1);
  }, [page, setPage]);

  const previousPage = useCallback(() => {
    setPage(page - 1);
  }, [page, setPage]);

  const changePageSize = useCallback(
    (newTake: number) => {
      setTake(newTake);
      setPage(1);
    },
    [setPage, setTake],
  );

  return {
    page,
    take,
    goToPage,
    nextPage,
    previousPage,
    changePageSize,
  };
}
