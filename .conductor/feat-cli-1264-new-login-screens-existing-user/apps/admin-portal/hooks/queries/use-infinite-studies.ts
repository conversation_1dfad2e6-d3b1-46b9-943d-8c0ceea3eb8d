import { useInfiniteQuery } from "@tanstack/react-query";

import api from "@/lib/apis";

export const useInfiniteStudies = (search: string, initialPageSize = 10) => {
  return useInfiniteQuery({
    queryKey: ["infinite-studies", search],
    queryFn: ({ pageParam = 1 }) =>
      api.studies.list({
        page: pageParam,
        take: initialPageSize,
        filter: { name: search },
      }),
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};

export const useInfiniteStudiesByGroup = (
  search: string,
  groupId: string,
  initialPageSize = 10,
) => {
  return useInfiniteQuery({
    queryKey: ["infinite-studies-by-group-id", search],
    queryFn: ({ pageParam = 1 }) =>
      api.groups.studies(groupId, {
        page: pageParam,
        take: initialPageSize,
        filter: { name: search },
      }),
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};

export const useInfiniteStudiesBySponsor = (
  search: string,
  sponsorsId?: string,
  initialPageSize = 10,
) => {
  return useInfiniteQuery({
    queryKey: ["infinite-studies", sponsorsId, search],
    queryFn: ({ pageParam = 1 }) =>
      api.studies.list({
        page: pageParam,
        take: initialPageSize,
        filter: { name: search, sponsorId: sponsorsId },
      }),
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};

export const useInfiniteStudiesBySite = (
  search: string,
  siteId?: string,
  initialPageSize = 50,
) => {
  return useInfiniteQuery({
    queryKey: ["infinite-studies-by-site", search, siteId],
    queryFn: ({ pageParam = 1 }) =>
      api.studies.list({
        page: pageParam,
        take: initialPageSize,
        filter: {
          name: search,
          siteId: siteId || undefined,
        },
      }),
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
    enabled: !!siteId,
  });
};
