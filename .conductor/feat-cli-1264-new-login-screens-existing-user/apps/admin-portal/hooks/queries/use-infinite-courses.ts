import { useInfiniteQuery } from "@tanstack/react-query";

import api from "@/lib/apis";

export const useInfiniteCourses = (search?: string, initialPageSize = 50) => {
  return useInfiniteQuery({
    queryKey: ["training-modules", "courses", search],
    queryFn: ({ pageParam = 1 }) =>
      api.trainingModules.getCourses({
        page: pageParam,
        take: initialPageSize,
        filter: { name: search },
      }),
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};
