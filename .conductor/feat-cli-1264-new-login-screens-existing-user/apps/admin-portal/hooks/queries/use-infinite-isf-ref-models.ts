import { useInfiniteQuery } from "@tanstack/react-query";

import api from "@/lib/apis";

export const useInfiniteISFRefModels = (
  search: string,
  initialPageSize = 50,
) => {
  return useInfiniteQuery({
    queryKey: ["isf-ref-models", search],
    queryFn: ({ pageParam = 1 }) =>
      api.isfRefModelApi.list({
        page: pageParam,
        take: initialPageSize,
        filter: { isfRefModel: search },
      }),
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};
