import { useInfiniteQuery } from "@tanstack/react-query";

import api from "@/lib/apis";
import type { CodelistTemplateStatus } from "@/lib/apis/codelist-templates/types";

export const useInfiniteCodelistTemplates = (
  search: string,
  status?: CodelistTemplateStatus,
  initialPageSize = 50,
) => {
  return useInfiniteQuery({
    queryKey: ["infinite-codelist-templates", search, status],
    queryFn: ({ pageParam = 1 }) =>
      api.codelistTemplates.list({
        page: pageParam,
        take: initialPageSize,
        filter: {
          name: search,
          ...(status && { status }),
        },
      }),
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};
