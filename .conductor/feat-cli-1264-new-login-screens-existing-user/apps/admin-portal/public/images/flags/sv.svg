<svg width="28" height="20" viewBox="0 0 28 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="0.25" y="0.25" width="27.5" height="19.5" rx="1.75" fill="white" stroke="#F5F5F5" stroke-width="0.5"/>
<mask id="mask0" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="28" height="20">
<rect x="0.25" y="0.25" width="27.5" height="19.5" rx="1.75" fill="white" stroke="white" stroke-width="0.5"/>
</mask>
<g mask="url(#mask0)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 6.66667H28V0H0V6.66667Z" fill="#1B57C4"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 20.0002H28V13.3335H0V20.0002Z" fill="#1B57C4"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.3999 7.8667C11.7522 8.35321 11.3333 9.12778 11.3333 10.0002C11.3333 11.4729 12.5272 12.6669 13.9999 12.6669C15.4727 12.6669 16.6666 11.4729 16.6666 10.0002C16.6666 9.12778 16.2476 8.35321 15.6 7.8667L14.6666 10.6669H13.3333L12.3999 7.8667Z" fill="url(#paint0_linear)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M13.9999 7.3335L14.6666 10.6668H13.3333L13.9999 7.3335Z" fill="#F9C92E"/>
</g>
<defs>
<linearGradient id="paint0_linear" x1="11.3333" y1="7.8667" x2="11.3333" y2="12.6669" gradientUnits="userSpaceOnUse">
<stop stop-color="#3C7839"/>
<stop offset="1" stop-color="#31642E"/>
</linearGradient>
</defs>
</svg>
