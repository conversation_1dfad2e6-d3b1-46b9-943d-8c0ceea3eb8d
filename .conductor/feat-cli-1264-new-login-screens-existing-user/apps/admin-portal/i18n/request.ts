import { getRequestConfig } from "next-intl/server";

import { getUserLocale } from "../lib/locale";

const SUPPORTED_LOCALES = ["en"];

export default getRequestConfig(async () => {
  // Provide a static locale, fetch a user setting,
  // read from `cookies()`, `headers()`, etc.
  let locale = await getUserLocale();

  if (!SUPPORTED_LOCALES.includes(locale)) {
    locale = "en";
  }

  return {
    locale,
    messages: (await import(`../messages/${locale}.json`)).default,
  };
});
