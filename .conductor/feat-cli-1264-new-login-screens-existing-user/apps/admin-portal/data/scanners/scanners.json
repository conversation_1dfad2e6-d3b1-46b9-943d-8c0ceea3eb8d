[{"id": "scanner1", "displayName": "Scanner Alpha", "description": "High-performance scanner for inventory management.", "encryptionKey": "ABCD1234", "hmac": "HMACVALUE1", "site": {"id": "site1", "name": "Main Warehouse", "address": {"addressLine": "1234 Elm Street", "city": "Metropolis", "countryId": "USA", "stateProvinceIdId": "NY", "zipPostalCode": "10001"}, "phone": "+1234567890", "email": "<EMAIL>", "isActive": true, "institutionId": "inst1"}, "assignment": {"id": "assignment1", "createdDate": "2024-01-01T08:00:00Z", "lastUpdatedDate": "2024-06-01T10:00:00Z", "name": "Warehouse Operations", "description": "Manage daily operations in the warehouse.", "type": "Operations"}, "isActive": true, "model": {"id": "model1", "modelName": "X-Scanner 2000", "companyName": "ScannerCorp", "description": "A reliable scanner model for industrial use."}, "currentVersion": {"id": "app1", "versionNumber": "1.0.0", "apkLink": "https://example.com/scanner-alpha-1.0.0.apk", "releaseNotes": "Initial release with basic scanning capabilities."}, "targetVersion": {"id": "app2", "versionNumber": "1.1.0", "apkLink": "https://example.com/scanner-alpha-1.1.0.apk", "releaseNotes": "Added support for QR code scanning."}, "diagnostics": "No issues detected.", "lastCheckIn": "2024-06-20T12:00:00Z", "updateFrequency": 7, "lastUpdatedDate": "2024-06-18T09:00:00Z", "rebootFrequency": 7, "rebootHour": 3, "rebootMinute": 30, "lastReboot": "2024-06-19T03:30:00Z", "timeZone": "America/New_York", "activatedDate": "2024-01-15T09:00:00Z", "macAddress": "00:1A:2B:3C:4D:5E", "ipAddress": "************", "isWifi": true}, {"id": "scanner2", "displayName": "Scanner Beta", "description": "Compact scanner for on-the-go scanning tasks.", "encryptionKey": "EFGH5678", "hmac": "HMACVALUE2", "site": {"id": "site2", "name": "Downtown Office", "address": {"addressLine": "5678 Oak Avenue", "city": "Gotham", "countryId": "USA", "stateProvinceId": "CA", "zipPostalCode": "90210"}, "phone": "+0987654321", "email": "<EMAIL>", "isActive": true, "institutionId": "inst2"}, "assignment": {"id": "assignment2", "createdDate": "2024-02-01T08:00:00Z", "lastUpdatedDate": "2024-06-02T10:00:00Z", "name": "Field Operations", "description": "Manage field operations and mobile scanning tasks.", "type": "Field"}, "isActive": true, "model": {"id": "model2", "modelName": "X-Scanner 3000", "companyName": "ScannerCorp", "description": "Portable scanner model with advanced features."}, "currentVersion": {"id": "app3", "versionNumber": "2.0.0", "apkLink": "https://example.com/scanner-beta-2.0.0.apk", "releaseNotes": "Improved scanning speed and battery efficiency."}, "targetVersion": {"id": "app4", "versionNumber": "2.1.0", "apkLink": "https://example.com/scanner-beta-2.1.0.apk", "releaseNotes": "Added support for barcode scanning."}, "diagnostics": "Battery level low.", "lastCheckIn": "2024-06-21T12:00:00Z", "updateFrequency": 14, "lastUpdatedDate": "2024-06-20T09:00:00Z", "rebootFrequency": 14, "rebootHour": 4, "rebootMinute": 0, "lastReboot": "2024-06-22T04:00:00Z", "timeZone": "America/Los_Angeles", "activatedDate": "2024-02-15T09:00:00Z", "macAddress": "11:22:33:44:55:66", "ipAddress": "************", "isWifi": false}]