[{"name": "Google", "address": {"addressLine": "1600 Amphitheatre Parkway", "city": "Mountain View", "country": "USA", "stateProvince": "CA", "zipPostalCode": "94043"}, "phone": "+16502530000", "email": "<EMAIL>"}, {"name": "Facebook", "address": {"addressLine": "1 Hacker Way", "city": "Menlo Park", "country": "USA", "stateProvince": "CA", "zipPostalCode": "94025"}, "phone": "+16505434567", "email": "<EMAIL>"}, {"name": "Apple", "address": {"addressLine": "1 Apple Park Way", "city": "Cupertino", "country": "USA", "stateProvince": "CA", "zipPostalCode": "95014"}, "phone": "+14089961010", "email": "<EMAIL>"}, {"name": "Microsoft", "address": {"addressLine": "One Microsoft Way", "city": "<PERSON><PERSON>", "country": "USA", "stateProvince": "WA", "zipPostalCode": "98052"}, "phone": "+14258828080", "email": "<EMAIL>"}, {"name": "Amazon", "address": {"addressLine": "410 Terry Ave North", "city": "Seattle", "country": "USA", "stateProvince": "WA", "zipPostalCode": "98109"}, "phone": "+12062664064", "email": "<EMAIL>"}, {"name": "Tesla", "address": {"addressLine": "3500 Deer Creek Road", "city": "Palo Alto", "country": "USA", "stateProvince": "CA", "zipPostalCode": "94304"}, "phone": "+16506811000", "email": "<EMAIL>"}, {"name": "Netflix", "address": {"addressLine": "100 Winchester Circle", "city": "Los Gatos", "country": "USA", "stateProvince": "CA", "zipPostalCode": "95032"}, "phone": "+14085031010", "email": "<EMAIL>"}, {"name": "Adobe", "address": {"addressLine": "345 Park Avenue", "city": "San Jose", "country": "USA", "stateProvince": "CA", "zipPostalCode": "95110"}, "phone": "+14085385000", "email": "<EMAIL>"}, {"name": "Intel", "address": {"addressLine": "2200 Mission College Blvd", "city": "Santa Clara", "country": "USA", "stateProvince": "CA", "zipPostalCode": "95054"}, "phone": "+14087653000", "email": "<EMAIL>"}]