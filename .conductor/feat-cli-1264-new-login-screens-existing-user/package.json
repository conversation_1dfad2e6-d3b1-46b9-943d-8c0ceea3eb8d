{"name": "@clincove-eng/frontend", "version": "1.0.0", "description": "Monorepo for client and admin portal", "scripts": {"prepare": "husky", "dev-client": "pnpm nx dev client-portal", "build-client": "pnpm nx build client-portal", "dev-admin": "pnpm nx dev admin-portal", "build-admin": "pnpm nx build admin-portal", "dev-config": "pnpm nx dev config-service", "build-config": "pnpm nx build config-service", "build-all": "pnpm nx run-many --target=build --all", "lint": "pnpm exec nx run-many --target=lint --all=true", "test-client": "npx playwright test ./tests/client", "lint:fix": "pnpm exec nx run-many --target=lint --all=true --fix=true", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,css}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,css}\"", "type-check": "pnpm exec tsc --noEmit --project apps/client-portal/tsconfig.json && pnpm exec tsc --noEmit --project apps/admin-portal/tsconfig.json && pnpm exec tsc --noEmit --project apps/config-service/tsconfig.json && pnpm exec tsc --noEmit --project packages/shared-ui/tsconfig.json"}, "author": "", "license": "UNLICENSED", "dependencies": {"@clerk/nextjs": "^6.9.9", "@clerk/themes": "^2.2.5", "@clincove-eng/backend-sdk-temp": "^0.2.2", "@cyntler/react-doc-viewer": "^1.17.0", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@faker-js/faker": "^9.6.0", "@floating-ui/react": "^0.27.3", "@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^3.10.0", "@mdx-js/mdx": "^3.1.0", "@mdx-js/react": "^3.1.0", "@monaco-editor/react": "^4.7.0", "@nx/workspace": "^20.8.2", "@react-pdf-viewer/core": "3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@react-pdf-viewer/get-file": "^3.12.0", "@react-pdf-viewer/page-navigation": "^3.12.0", "@react-pdf-viewer/print": "^3.12.0", "@react-pdf-viewer/rotate": "^3.12.0", "@react-pdf-viewer/selection-mode": "^3.12.0", "@react-pdf-viewer/toolbar": "^3.12.0", "@react-pdf-viewer/zoom": "^3.12.0", "@tanstack/react-query": "^5.63.0", "@tanstack/react-query-devtools": "^5.63.0", "@tanstack/react-table": "^8.20.6", "@tryghost/content-api": "^1.11.25", "@types/papaparse": "^5.3.15", "@uppy/core": "^4.4.7", "@uppy/dashboard": "^4.3.1", "@uppy/drag-drop": "^4.1.1", "@uppy/file-input": "^4.1.1", "@uppy/progress-bar": "^4.2.1", "@uppy/react": "^4.2.1", "@uppy/status-bar": "^4.1.3", "@uppy/xhr-upload": "^4.3.3", "@xyflow/react": "^12.8.2", "axios": "^1.7.9", "canvas": "2.11.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "ejs": "^3.1.10", "emailjs-mime-parser": "^2.0.7", "flowbite-react": "^0.10.2", "framer-motion": "^11.3.24", "html2canvas": "^1.4.1", "input-otp": "^1.4.2", "jspdf": "^3.0.1", "lodash": "^4.17.21", "lucide-react": "^0.475.0", "next": "15.4.3", "next-intl": "^3.17.0", "nuqs": "^2.2.3", "papaparse": "^5.5.2", "pdfjs-dist": "3.4.120", "posthog-js": "^1.249.2", "react": "18.3.1", "react-csv": "^2.2.2", "react-day-picker": "^9.6.4", "react-dom": "18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.1", "react-icons": "^5.4.0", "react-json-view": "^1.21.3", "react-magic-motion": "^1.1.2", "react-number-format": "^5.4.4", "react-paginate": "^8.2.0", "react-pdf": "^9.2.1", "react-resizable-panels": "^2.1.7", "reactflow": "^11.11.4", "recharts": "^3.0.0", "remark-gfm": "^4.0.0", "remeda": "^2.19.0", "sharp": "^0.33.3", "signature_pad": "^5.0.7", "slate": "^0.117.2", "slate-history": "^0.113.1", "slate-react": "^0.117.4", "svgmap": "^2.10.1", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.17", "tailwindcss-motion": "^1.1.0", "timescape": "^0.7.1", "ts-pattern": "^5.6.2", "type-fest": "^4.32.0", "usehooks-ts": "^3.1.1", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@changesets/changelog-github": "^0.5.0", "@changesets/cli": "^2.27.1", "@clerk/types": "^4.74.0", "@eslint/compat": "^1.1.1", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.8.0", "@next/bundle-analyzer": "^15.4.2", "@next/eslint-plugin-next": "^15.1.4", "@nx/devkit": "20.3.1", "@nx/eslint": "20.3.1", "@nx/eslint-plugin": "20.3.1", "@nx/js": "20.3.1", "@nx/linter": "^19.8.4", "@nx/next": "20.3.1", "@playwright/test": "^1.54.1", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@testing-library/react": "15.0.6", "@types/ejs": "^3.1.5", "@types/lodash": "^4.17.17", "@types/node": "18.16.9", "@types/react": "18.3.1", "@types/react-csv": "^1.1.10", "@types/react-dom": "18.3.0", "@types/tryghost__content-api": "^1.3.17", "@typescript-eslint/eslint-plugin": "^8.19.1", "@typescript-eslint/parser": "^8.19.1", "autoprefixer": "10.4.13", "cz-conventional-changelog": "^3.3.0", "eslint": "^9.8.0", "eslint-config-next": "^15.1.4", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-tailwindcss": "^3.17.5", "husky": "^9.1.7", "lint-staged": "^15.4.3", "nx": "20.3.1", "postcss": "8.4.38", "prettier": "^3.4.2", "prettier-eslint": "^16.3.0", "prettier-plugin-organize-imports": "^3.2.4", "prettier-plugin-tailwindcss": "^0.5.14", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "~5.6.2", "typescript-eslint": "^8.13.0"}}